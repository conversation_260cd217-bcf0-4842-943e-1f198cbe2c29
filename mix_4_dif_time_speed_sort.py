import heapq
import os

def mix_files_by_arrival_time(files, output_file, rates):
    # 打开所有输入文件
    file_handles = [open(file, 'r') for file in files]

    # 创建一个优先队列（最小堆）
    min_heap = []

    # 初始化堆，将每个文件的第一行放入堆中
    for file_index, (fh, rate) in enumerate(zip(file_handles, rates)):
        line = fh.readline().strip()
        if line:
            parts = line.split()
            arrival_time = int(parts[0])  # 假设到达时间在第一列
            adjusted_time = arrival_time * rate  # 调整时间流速
            heapq.heappush(min_heap, (adjusted_time, line, file_index))
            print(f"Initial line from file {files[file_index]}: {line} (adjusted time: {adjusted_time})")  # Debug output

    # 打开输出文件
    with open(output_file, 'w') as out:
        print(f"Writing to output file: {os.path.abspath(output_file)}")  # Debug output
        # 不断从堆中取出最小的元素
        while min_heap:
            adjusted_time, line, file_index = heapq.heappop(min_heap)
            out.write(f'{line} {file_index + 1}\n')

            # 从同一个文件读取下一行
            next_line = file_handles[file_index].readline().strip()
            if next_line:
                parts = next_line.split()
                next_arrival_time = int(parts[0])
                adjusted_next_time = next_arrival_time * rates[file_index]  # 调整时间流速
                heapq.heappush(min_heap, (adjusted_next_time, next_line, file_index))

    # 关闭所有文件
    for fh in file_handles:
        fh.close()

if __name__ == "__main__":
    files = ['pr_t3_t1.pagetrace', 'spec505_mcf_t1.pagetrace', 'spec519_lbm_t1.pagetrace', 'gap_cc_t1.pagetrace']
    output_file = 'mix1_pr_mcf_lbm_cc_time_dif_speed_t2.pagetrace'
    rates = [1.0, 0.8, 0.5, 0.2]  # 每个文件的速率比例

    mix_files_by_arrival_time(files, output_file, rates)
