#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <math.h>  // 新增，用于计算指数函数

#define BASE_FILENAME "trace/gap_bc_new_16CNs"
#define INPUT_FILENAME BASE_FILENAME ".pagetrace"
#define LOG_FILENAME BASE_FILENAME ".log"
#define CACHE_SIZE (1L * 64 * 1024 * 1024 / 4096)
#define SSD_DELAY 75000
#define CXL_DELAY 100
#define DDR_DELAY 46
#define PREFETCH_N 4
#define LINES_PER_SET 4
#define NUM_SETS (CACHE_SIZE / LINES_PER_SET)
#define NUM_MACHINES 16  // 定义机器数量

// RRIP constants
#define MAX_RRPV 3   // Maximum Re-Reference Prediction Value
#define INIT_RRPV 3  // Initial RRPV for new cache lines
#define LONG_RRPV 0  // RRPV for long re-reference intervals

// LRFU constants
#define LAMBDA 0.000001  // LRFU权重参数，0表示纯LFU，1表示纯LRU
#define TIME_INTERVAL 1.0  // 时间单位

typedef struct CachePage {
  unsigned long long page_number;
  int valid;
  int lru_counter;
  int fifo_order;
  int load_id;
  int accessed;
  int rrpv;  // 新增字段，用于RRIP算法
  int access_frequency;  // 新增字段，用于LFU算法
  double crf_value;  // 新增字段，用于LRFU算法的CRF值
  long last_access_time;  // 新增字段，记录最后访问时间
} CachePage;

CachePage cache[NUM_SETS][LINES_PER_SET];
int fifo_counter[NUM_SETS] = {0};
int strategy = 6;  // 6表示使用LRFU策略
int prefetch_enabled = 0;
int log_enabled = 0;
int separate_miss_rate = 1;
long current_time = 0;  // 全局时间计数器

// 逐出统计
int eviction_count = 0;
int non_accessed_eviction_count = 0;

// 访存统计
int access_counts[NUM_MACHINES + 1] = {0};     // 每个节点的访存次数
int total_memory_accesses = 0;  // 所有节点的总访存次数

// 计算LRFU的CRF值
double calculate_crf(double old_crf, long time_diff) {
    // F(x) = (1/2)^(λx)，其中x是时间差
    double decay = pow(0.5, LAMBDA * time_diff * TIME_INTERVAL);
    return old_crf * decay + 1.0;
}

void init() {
  for (int i = 0; i < NUM_SETS; i++) {
    for (int j = 0; j < LINES_PER_SET; j++) {
      cache[i][j].valid = 0;
      cache[i][j].lru_counter = 0;
      cache[i][j].fifo_order = 0;
      cache[i][j].load_id = -1;
      cache[i][j].accessed = 0;
      cache[i][j].rrpv = INIT_RRPV;  // 初始化RRPV
      cache[i][j].access_frequency = 0;  // 初始化访问频率
      cache[i][j].crf_value = 0.0;  // 初始化CRF值
      cache[i][j].last_access_time = 0;  // 初始化最后访问时间
    }
  }
  eviction_count = 0;
  non_accessed_eviction_count = 0;
  current_time = 0;  // 初始化全局时间
}

int find_in_cache(unsigned long long page_number) {
  int set_index = page_number % NUM_SETS;
  
  // 更新全局时间
  current_time++;
  
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (cache[set_index][i].valid &&
        cache[set_index][i].page_number == page_number) {
      
      // 根据策略不同进行不同处理
      if (strategy == 6) {  // LRFU策略
        long time_diff = current_time - cache[set_index][i].last_access_time;
        cache[set_index][i].crf_value = calculate_crf(cache[set_index][i].crf_value, time_diff);
        cache[set_index][i].last_access_time = current_time;
      } else {
        cache[set_index][i].accessed = 1;      // 记录被再次访问
        cache[set_index][i].rrpv = LONG_RRPV;  // 重置RRPV为长期使用
        cache[set_index][i].access_frequency++; // 增加访问频率计数
      }
      
      return i;
    }
  }
  return -1;
}

void update_lru(int set_index, int accessed_index) {
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (cache[set_index][i].valid) {
      cache[set_index][i].lru_counter++;
    }
  }
  cache[set_index][accessed_index].lru_counter = 0;
}

void evict_and_load(int set_index, int load_index,
                    unsigned long long page_number, int load_id) {
  if (cache[set_index][load_index].valid) {
    eviction_count++;
    if (!cache[set_index][load_index].accessed) {
      non_accessed_eviction_count++;
    }
  }
  // 更新缓存行信息
  cache[set_index][load_index].page_number = page_number;
  cache[set_index][load_index].valid = 1;
  cache[set_index][load_index].load_id = load_id;
  cache[set_index][load_index].accessed = 0;
  cache[set_index][load_index].rrpv = INIT_RRPV;  // 初始化RRPV
  cache[set_index][load_index].access_frequency = 1;  // 初始访问频率为1
  
  // LRFU特有的初始化
  if (strategy == 6) {
    cache[set_index][load_index].crf_value = 1.0;  // 初始CRF值为1
    cache[set_index][load_index].last_access_time = current_time;  // 设置访问时间
  }
}

// LRFU缓存替换策略
void load_to_cache_lrfu(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  int lrfu_index = 0;
  double min_crf = __DBL_MAX__;
  
  // 首先检查是否有空行
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid) {
      lrfu_index = i;
      break;
    }
    
    // 计算当前的CRF值（考虑时间衰减）
    long time_diff = current_time - cache[set_index][i].last_access_time;
    double current_crf = calculate_crf(cache[set_index][i].crf_value, time_diff);
    
    // 找到CRF值最小的行
    if (current_crf < min_crf) {
      min_crf = current_crf;
      lrfu_index = i;
    }
  }
  
  evict_and_load(set_index, lrfu_index, page_number, load_id);
  cache[set_index][lrfu_index].fifo_order = fifo_counter[set_index]++;
  cache[set_index][lrfu_index].crf_value = 1.0;  // 初始CRF值为1
  cache[set_index][lrfu_index].last_access_time = current_time;  // 设置访问时间
}

void load_to_cache_lru(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  int lru_index = 0;
  int max_counter = -1;
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid) {
      lru_index = i;
      break;
    }
    if (cache[set_index][i].lru_counter > max_counter) {
      max_counter = cache[set_index][i].lru_counter;
      lru_index = i;
    }
  }
  evict_and_load(set_index, lru_index, page_number, load_id);
  update_lru(set_index, lru_index);
}

void load_to_cache_fifo(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  int fifo_index = 0;
  int oldest_order = __INT_MAX__;
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid) {
      fifo_index = i;
      break;
    }
    if (cache[set_index][i].fifo_order < oldest_order) {
      oldest_order = cache[set_index][i].fifo_order;
      fifo_index = i;
    }
  }
  evict_and_load(set_index, fifo_index, page_number, load_id);
  cache[set_index][fifo_index].fifo_order = fifo_counter[set_index]++;
}

void load_to_cache_rrip(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  while (1) {
    for (int i = 0; i < LINES_PER_SET; i++) {
      if (!cache[set_index][i].valid || cache[set_index][i].rrpv == MAX_RRPV) {
        evict_and_load(set_index, i, page_number, load_id);
        return;
      }
    }
    for (int i = 0; i < LINES_PER_SET; i++) {
      cache[set_index][i].rrpv++;
    }
  }
}

void load_to_cache_lfu(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  int lfu_index = 0;
  int min_frequency = __INT_MAX__;
  
  // 首先检查是否有空行
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid) {
      lfu_index = i;
      break;
    }
    // 找到访问频率最低的行
    if (cache[set_index][i].access_frequency < min_frequency) {
      min_frequency = cache[set_index][i].access_frequency;
      lfu_index = i;
    }
    // 如果访问频率相同，优先选择先进入缓存的行
    else if (cache[set_index][i].access_frequency == min_frequency) {
      if (cache[set_index][i].fifo_order < cache[set_index][lfu_index].fifo_order) {
        lfu_index = i;
      }
    }
  }
  
  evict_and_load(set_index, lfu_index, page_number, load_id);
  cache[set_index][lfu_index].fifo_order = fifo_counter[set_index]++;
  cache[set_index][lfu_index].access_frequency = 1;  // 初始访问频率为1
}

void prefetch(unsigned long long page_number) {
  if (!prefetch_enabled) return;
  for (int i = 1; i <= PREFETCH_N; i++) {
    unsigned long long next_page = page_number + i;
    if (find_in_cache(next_page) == -1) {
      if (strategy == 6) {
        load_to_cache_lrfu(next_page, -1);
      } else if (strategy == 3) {
        load_to_cache_lfu(next_page, -1);
      } else if (strategy == 2) {
        load_to_cache_rrip(next_page, -1);
      } else if (strategy == 1) {
        load_to_cache_lru(next_page, -1);
      } else if (strategy == 0) {
        load_to_cache_fifo(next_page, -1);
      }
    }
  }
}

void simulate_accesses(const char *input_filename, const char *log_filename) {
  FILE *input_file = fopen(input_filename, "r");
  if (!input_file) {
    fprintf(stderr, "Failed to open file %s\n", input_filename);
    return;
  }
  FILE *log_file = NULL;
  if (log_enabled) {
    log_file = fopen(log_filename, "w");
    if (!log_file) {
      fprintf(stderr, "Failed to open log file %s\n", log_filename);
      fclose(input_file);
      return;
    }
  }
  int hits[NUM_MACHINES + 1] = {0};
  int total_accesses[NUM_MACHINES + 1] = {0};
  long long total_delay = 0;
  long long delay_per_load[NUM_MACHINES + 1] = {0};
  int conflict_misses[NUM_MACHINES + 1] = {0};
  int inter_load_conflict_misses[NUM_MACHINES + 1] = {0};
  long timestamp, dummy1, dummy2, rw_flag, load_id;
  unsigned long long page_number;
  while (fscanf(input_file, "%ld %ld %llu %ld %ld %ld", &timestamp, &dummy1,
                &page_number, &dummy2, &rw_flag, &load_id) == 6) {
    if (load_id > NUM_MACHINES) continue;  // 如果load_id超过NUM_MACHINES则跳过
    total_accesses[load_id]++;
    access_counts[load_id]++;  // 更新节点的访存次数
    total_memory_accesses++;   // 更新总体的访存次数
    int set_index = page_number % NUM_SETS;
    int cache_index = find_in_cache(page_number);
    if (cache_index != -1) {
      hits[load_id]++;
      total_delay += (DDR_DELAY + CXL_DELAY);
      delay_per_load[load_id] += (DDR_DELAY + CXL_DELAY);
      if (strategy == 1) {
        update_lru(set_index, cache_index);
      }
      if (log_enabled) {
        fprintf(log_file, "Timestamp %ld: Page %llu, Cache hit\n", timestamp,
                page_number);
      }
    } else {
      total_delay += (SSD_DELAY + CXL_DELAY + DDR_DELAY);
      delay_per_load[load_id] += (SSD_DELAY + CXL_DELAY + DDR_DELAY);
      int is_conflict_miss = 1;
      for (int i = 0; i < LINES_PER_SET; i++) {
        if (!cache[set_index][i].valid) {
          is_conflict_miss = 0;
          break;
        }
      }
      if (is_conflict_miss) {
        conflict_misses[load_id]++;
        int inter_load_conflict = 0;
        for (int i = 0; i < LINES_PER_SET; i++) {
          if (cache[set_index][i].load_id != load_id) {
            inter_load_conflict = 1;
            break;
          }
        }
        if (inter_load_conflict) {
          inter_load_conflict_misses[load_id]++;
        }
      }
      if (strategy == 6) {
        load_to_cache_lrfu(page_number, load_id);
      } else if (strategy == 3) {
        load_to_cache_lfu(page_number, load_id);
      } else if (strategy == 2) {
        load_to_cache_rrip(page_number, load_id);
      } else if (strategy == 1) {
        load_to_cache_lru(page_number, load_id);
      } else if (strategy == 0) {
        load_to_cache_fifo(page_number, load_id);
      }
      if (log_enabled) {
        fprintf(log_file, "Timestamp %ld: Page %llu, Cache miss\n", timestamp,
                page_number);
      }
      prefetch(page_number);
    }
  }

  fclose(input_file);
  if (log_enabled) {
    fclose(log_file);
  }

  int total_hits = 0;
  int total_access = 0;
  for (int i = 1; i <= NUM_MACHINES; i++) {
    total_hits += hits[i];
    total_access += total_accesses[i];
  }
  double overall_miss_rate = 1.0 - ((double)total_hits / total_access);
  printf("Overall miss rate: %.4f%%\n", overall_miss_rate * 100);

  if (separate_miss_rate) {
    for (int i = 1; i <= NUM_MACHINES; i++) {
      if (total_accesses[i] > 0) {
        double miss_rate = 1.0 - ((double)hits[i] / total_accesses[i]);
        printf("Miss rate for load %d: %.4f%%\n", i, miss_rate * 100);
        printf("Total delay for load %d: %lld ns\n", i, delay_per_load[i]);
        double conflict_miss_rate =
            (double)conflict_misses[i] / total_accesses[i];
        double inter_load_conflict_miss_rate =
            (double)inter_load_conflict_misses[i] / total_accesses[i];
        printf("Conflict miss rate for load %d: %.4f%%\n", i,
               conflict_miss_rate * 100);
        printf("Inter-load conflict miss rate for load %d: %.4f%%\n", i,
               inter_load_conflict_miss_rate * 100);
      }
    }
  }
  printf("Total delay: %lld ns\n", total_delay);
  // 输出逐出统计结果
  printf("Total evictions: %d\n", eviction_count);
  printf("Evictions without re-access: %d\n", non_accessed_eviction_count);
  // 输出每个节点和总访存次数
  for (int i = 1; i <= NUM_MACHINES; i++) {
    printf("Access count for node %d: %d\n", i, access_counts[i]);
  }
  printf("Total memory accesses: %d\n", total_memory_accesses);
  
  // 输出LRFU参数
  if (strategy == 6) {
    printf("\nLRFU Parameters:\n");
    printf("Lambda: %.2f\n", LAMBDA);
    printf("Time Interval: %.2f\n", TIME_INTERVAL);
  }
}

int main() {
  init();
  clock_t start_time = clock();
  simulate_accesses(INPUT_FILENAME, LOG_FILENAME);
  clock_t end_time = clock();
  double duration = (double)(end_time - start_time) / CLOCKS_PER_SEC;
  printf("Simulation time: %.2f seconds\n", duration);
  return 0;
}
