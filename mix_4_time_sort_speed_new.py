def merge_pagetrace_files(files, output_file, performance_ratios):
	# 初始化一个列表来作为优先队列
	priority_queue = []

	# 打开所有文件，并初始化文件指针
	file_pointers = [open(file, 'r') for file in files]
	last_arrival_times = [0] * len(files)  # 存储每个文件最后一条记录的到达时间
	completed_files = [False] * len(files)  # 记录每个文件是否已读取完一遍

	try:
		# 首先读取每个文件的第一行并加入优先队列
		for source, fp in enumerate(file_pointers, start=1):
			line = fp.readline().strip()
			if line:
				parts = line.split()
				# print(' '.join(parts) + f' {source}\n')
				arrival_time = int(parts[0])
				# 根据性能比例调整到达时间
				adjusted_arrival_time = arrival_time / performance_ratios[source - 1]
				# 将调整后的到达时间、行内容、来源索引加入队列
				priority_queue.append((adjusted_arrival_time, parts, source))
				last_arrival_times[source - 1] = arrival_time

		# 自定义排序函数
		def sort_priority_queue(queue):
			# 按到达时间排序
			queue.sort(key=lambda x: x[0])

		# 打开输出文件
		with open(output_file, 'w') as out_fp:
			# 处理队列中的每个元素
			while priority_queue:
				# 对队列进行排序
				sort_priority_queue(priority_queue)
				# 弹出队列中最小的元素
				adjusted_arrival_time, parts, source = priority_queue.pop(0)
				# 写入输出文件，附加来源信息
				out_fp.write(' '.join(parts) + f' {source}\n')
				# print(' '.join(parts) + f' {source}\n')

				# 从相应的文件中读取下一行
				next_line = file_pointers[source - 1].readline().strip()
				if next_line:
					next_parts = next_line.split()
					# print(next_line)
					# print(' '.join(parts) + f' {source}\n')
					next_arrival_time = int(next_parts[0]) + last_arrival_times[source - 1]
					# 根据性能比例调整到达时间
					adjusted_next_arrival_time = next_arrival_time / performance_ratios[source - 1]
					# 将新行加入队列
					priority_queue.append((adjusted_next_arrival_time, next_parts, source))
					last_arrival_times[source - 1] = next_arrival_time
				else:
					# 文件到达末尾，标记为已完成
					completed_files[source - 1] = True
					print(f"File {files[source - 1]} has been read completely once.")
					print(completed_files)			
					# 检查是否所有文件都已完成
					if all(completed_files):
							print("All files have been read completely once. Exiting.")
							break
					# 重置文件指针并读取第一行
					file_pointers[source - 1].seek(0)
					next_line = file_pointers[source - 1].readline().strip()
					if next_line:
							next_parts = next_line.split()
							# 计算新的到达时间
							next_arrival_time = int(next_parts[0]) + last_arrival_times[source - 1]
							# 根据性能比例调整到达时间
							adjusted_next_arrival_time = next_arrival_time / performance_ratios[source - 1]
							# 将新行加入队列
							priority_queue.append((adjusted_next_arrival_time, next_parts, source))
							last_arrival_times[source - 1] = next_arrival_time
	finally:
		# 确保所有文件指针都被关闭
		for fp in file_pointers:
			fp.close()

# 假设文件名为 file1.txt, file2.txt, file3.txt, file4.txt
# files = ['radiosity_off1.pagetrace', 'radiosity_off2.pagetrace', 'radiosity_off3.pagetrace', 'radiosity_off4.pagetrace']
# output_file = 'radiosity_mix1_difspeed.pagetrace'

# files = ['spec_lbm_off1.pagetrace', 'spec_lbm_off2.pagetrace', 'spec_lbm_off3.pagetrace', 'spec_lbm_off4.pagetrace']
# output_file = 'spec_lbm_mix1_difspeed.pagetrace'

# files = ['spec508_namd_off1.pagetrace', 'spec508_namd_off2.pagetrace', 'spec508_namd_off3.pagetrace', 'spec508_namd_off4.pagetrace']
# output_file = 'spec508_namd_mix1_difspeed.pagetrace'

# files = ['pr_t3_off1.pagetrace', 'pr_t3_off2.pagetrace', 'pr_t3_off3.pagetrace', 'pr_t3_off4.pagetrace']
# output_file = 'pr_t3_mix1_difspeed.pagetrace'

# files = ['gap_cc_off1.pagetrace', 'gap_cc_off2.pagetrace', 'gap_cc_off3.pagetrace', 'gap_cc_off4.pagetrace']
# output_file = 'gap_cc_mix1_difspeed.pagetrace'

# files = ['spec_lbm_new_off1.pagetrace', 'spec_lbm_new_off2.pagetrace', 'spec_lbm_new_off3.pagetrace', 'spec_lbm_new_off4.pagetrace']
# output_file = 'spec_lbm_new_mix1_difspeed.pagetrace'

# # mix1混合负载
# files = ['pr_t3_mix1.pagetrace', 'spec508_namd_mix1.pagetrace', 'spec_lbm_new_mix1.pagetrace', 'gap_cc_mix1.pagetrace']
# output_file = 'pr_namd_lbm_cc_mix1_difspeed.pagetrace'

# mix2混合负载
files = ['spec505_mcf_mix2.pagetrace', 'deepsjeng_t2_mix2.pagetrace', 'cactusBSSN_new_mix2.pagetrace', 'gap_bc_new_mix2.pagetrace']
output_file = 'mcf_deep_cactus_bc_mix2_difspeed.pagetrace'

performance_ratios = [1.0, 0.8, 0.6, 0.4]  # 示例性能比例
merge_pagetrace_files(files, output_file, performance_ratios)
