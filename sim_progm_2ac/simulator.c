#include "simulator.h"

#include <stdio.h>

#include "cache.h"
#include "config.h"
#include "eq.h"
#include "prefetcher.h"
#include "q_table.h"
#include "stdlib.h"

// extern int separate_miss_rate;
// extern int log_enabled;

int samp_log = 2968;

void simulate_accesses(const char *input_filename, const char *log_filename) {
  // printf("input:%s",INPUT_FILENAME);
  FILE *input_file = fopen(input_filename, "r");
  if (!input_file) {
    fprintf(stderr, "Failed to open file %s\n", input_filename);
    return;
  }

  long timestamp, dummy1, dummy2, rw_flag, load_id;
  unsigned long long page_number;
  int hits[NUM_MACHINES + 1] = {0};
  int bypass_account[NUM_MACHINES + 1] = {0};
  int total_accesses[NUM_MACHINES + 1] = {0};
  long long total_delay = 0;
  long long delay_per_load[NUM_MACHINES + 1] = {0};

  //   const int SKIP_COUNT = *********/2;  // 前 105,440,564 次访问不计数
  //   const int LAST_COUNT = *********;  // 前 105,440,564 次访问不计数
  const int SKIP_COUNT = 0;          // 前 105,440,564 次访问不计数
  const int LAST_COUNT = *********;  // 前 105,440,564 次访问不计数
  int skip_count_enabled = 0;    // 标志位，用于控制是否跳过计数
  int current_access_count = 0;  // 当前的访问计数器

  while (fscanf(input_file, "%ld %ld %llu %ld %ld %ld", &timestamp, &dummy1,
                &page_number, &dummy2, &rw_flag, &load_id) == 6) {
    if (skip_count_enabled && current_access_count > SKIP_COUNT &&
        current_access_count < LAST_COUNT) {
      total_accesses[load_id]++;
    } else if (skip_count_enabled == 0) {
      total_accesses[load_id]++;
    }
    int set_index = page_number % NUM_SETS;  // 获取映射到cache set的组号
    int cache_index = find_in_cache(page_number, load_id);
    int action_now = H_EPV;  // 您可以根据实际情况设置这个变量的值

    uint32_t PN = (uint32_t)(page_number & 0xFFFFFF);  // 取page_number的后15位
    uint32_t source = (uint32_t)load_id;  // 直接使用load_id作为source

    current_access_count++;  // 当前访问计数

    int find_in_eq = 0;

    // 检查是否在采样集中	阶段1，chrome中分配奖励这部分
    if (set_index % sep_num == 0) {
      // printf("sample set %d\r\n", set_index);
      // 查看该地址在EQ中
      Naper_EQEntry *check_entry =
          find_in_evaluation_queue((set_index / sep_num), PN);
      if (check_entry != NULL) {
        // printf("found");
        find_in_eq = 1;
        // if(check_entry->address == 810432){
        // 	printf("find it\r\n");
        // 	//调试用
        // 	int index_reward = find_eq_entry_index(set_index / 64, PN);
        // 	modify_eq_entry_reward(set_index / 64, index_reward, 6);
        // }

        // if (set_index / 64 == 55)
        //   printf("check %d %d\r\n", check_entry->address,
        //   check_entry->reward);
        // printf("PN:%d,source :%d
        // \r\n",check_entry->address,check_entry->cpu);
        // printf("find eq\r\n");
        // 该缓存在cache中，分配奖励
        if (check_entry->has_reward == false) {
          // if (cache_index != -1) {
          if (check_entry->in_cache == true) {  // 命中，说明之前选择正确
            int index_reward = find_eq_entry_index(set_index / sep_num, PN);
            modify_eq_entry_reward(set_index / sep_num, index_reward, R_AC);
            // modify_last_eq_entry_reward(set_index / 64,R_AC);
            // check_entry->reward = R_AC;
            // check_entry->trigger = 1;
            // check_entry->has_reward = true;
            // printf("hit reward for %d cpu %d reward %d
            // r\n",set_index/64,check_entry->cpu,check_entry->reward);
          } else {  // 在之前被逐出了
            int index_reward = find_eq_entry_index(set_index / sep_num, PN);
            modify_eq_entry_reward(set_index / sep_num, index_reward, R_IN);
            // check_entry->reward = R_IN;
            // check_entry->trigger = 1;
            // check_entry->has_reward = true;
          }
        } else {
          // 调试用
          if (set_index / sep_num == samp_log) {
            printf("reward fail : PN %d", PN);
          }
          int index_reward = find_eq_entry_index(set_index / sep_num, PN);
          modify_eq_entry_reward(set_index / sep_num, index_reward, 6);
          // if (set_index / 64 == 55)
          // printf("reward %d\r\n",check_entry->reward);
        }
      } else {
        // if (set_index / 64 == 55) {
        //   printf("check null,set_index:%d\t", set_index);
        //   printf("PN:%d \r\n", PN);
        // }
      }
    }

    // 获取state状态向量	阶段2，chrome中执行动作这部分
    // uint32_t PN = (uint32_t)(page_number & 0x7FFF);  // 取page_number的后15位
    // uint32_t source = (uint32_t)load_id;  // 直接使用load_id作为source
    // 根据source和PN选取动作
    if (cache_index != -1) {  // 命中，只选择EPV
      action_now = select_action_hit(source, PN);
    } else {  // 未命中，有bpyass的选项
      action_now = select_action(source, PN);
    }

    if (cache_index != -1) {
      // printf("hit??\r\n");
      // 预热
      if (skip_count_enabled && current_access_count > SKIP_COUNT &&
          current_access_count < LAST_COUNT) {
        hits[load_id]++;
        total_delay += (DDR_DELAY + CXL_DELAY);
        delay_per_load[load_id] += (DDR_DELAY + CXL_DELAY);
      } else if (skip_count_enabled == 0) {
        hits[load_id]++;
        total_delay += (DDR_DELAY + CXL_DELAY);
        delay_per_load[load_id] += (DDR_DELAY + CXL_DELAY);
      }

      // hits[load_id]++;
      // total_delay += (DDR_DELAY + CXL_DELAY);
      // delay_per_load[load_id] += (DDR_DELAY + CXL_DELAY);
      if (strategy == 1) {
        update_lru(set_index, cache_index);
      }
      // find_in_cache_and_update_epv(page_number, load_id, action_now);
    } else {
      if (action_now == BYPASS) {
        // 预热
        if (skip_count_enabled && current_access_count > SKIP_COUNT &&
            current_access_count < LAST_COUNT) {
          bypass_account[load_id]++;
          total_delay += (SSD_DELAY + CXL_DELAY);  // 只有SSD和CXL延迟
          delay_per_load[load_id] +=
              (SSD_DELAY + CXL_DELAY);  // 更新每个负载的延迟
        } else if (skip_count_enabled == 0) {
          bypass_account[load_id]++;
          total_delay += (SSD_DELAY + CXL_DELAY);  // 只有SSD和CXL延迟
          delay_per_load[load_id] +=
              (SSD_DELAY + CXL_DELAY);  // 更新每个负载的延迟
        }
        // printf("bypass once\r\n");

        // bypass_account[load_id]++;
        // total_delay += (SSD_DELAY + CXL_DELAY);  // 只有SSD和CXL延迟
        // delay_per_load[load_id] +=
        //     (SSD_DELAY + CXL_DELAY);  // 更新每个负载的延迟
      } else {
        // 预热
        if (skip_count_enabled && current_access_count > SKIP_COUNT &&
            current_access_count < LAST_COUNT) {
          total_delay +=
              (SSD_DELAY + CXL_DELAY + DDR_DELAY);  // SSD、CXL和DDR延迟
          delay_per_load[load_id] +=
              (SSD_DELAY + CXL_DELAY + DDR_DELAY);  // 更新每个负载的延迟
        } else if (skip_count_enabled == 0) {
          total_delay +=
              (SSD_DELAY + CXL_DELAY + DDR_DELAY);  // SSD、CXL和DDR延迟
          delay_per_load[load_id] +=
              (SSD_DELAY + CXL_DELAY + DDR_DELAY);  // 更新每个负载的延迟
        }
        // total_delay +=
        //     (SSD_DELAY + CXL_DELAY + DDR_DELAY);  // SSD、CXL和DDR延迟
        // delay_per_load[load_id] +=
        //     (SSD_DELAY + CXL_DELAY + DDR_DELAY);  // 更新每个负载的延迟

        if (strategy == 1) {
          // load_to_cache_evp(page_number, load_id, action_now);
          load_to_cache_lru_evp(page_number, load_id, action_now);
          // load_to_cache_lru(page_number, load_id);  // 根据LRU策略加载到缓存
        } else {
          load_to_cache_fifo(page_number, load_id);  // 根据FIFO策略加载到缓存
        }
      }
    }
    // 记录sampled set的数据进入EQ
    if (set_index % sep_num == 0) {  // 检查是否是采样的 set index
      // 即将要加入的EQ
      Naper_EQEntry *entry = (Naper_EQEntry *)malloc(sizeof(Naper_EQEntry));
      // Naper_EQEntry *evi_entry = (Naper_EQEntry
      // *)malloc(sizeof(Naper_EQEntry));
      Naper_EQEntry *evi_entry = NULL;
      Naper_EQEntry *head_entry = NULL;
      // Naper_EQEntry *head_entry = (Naper_EQEntry
      // *)malloc(sizeof(Naper_EQEntry));
      if (entry) {
        // entry->address = page_number;
        entry->address = PN;
        entry->action_index = action_now;  // 根据实际情况设置
                                           // if (action_now != BYPASS)
                                           //   entry->in_cache = 1;
                                           // else
                                           //   entry->in_cache = 0;
        if (cache_index != -1 || action_now != BYPASS)  // 命中
          entry->in_cache = true;
        else
          entry->in_cache = false;

        if (find_in_eq == 1)  // 命中
        {
          entry->hit_eq = true;
          // printf("hit\t\n");
        } else {
          entry->hit_eq = false;
        }

        entry->cpu = load_id;  // 根据实际情况设置
        entry->has_reward = false;
        entry->reward = 0;
        entry->position = 0;
        entry->trigger = false;
        entry->cache_set = set_index;

        uint32_t eq_index =
            set_index / sep_num;  // 计算 EQ 索引
                                  // printf("new eq PN:%d\r\n",entry->address);
        evi_entry = enqueue(eq_index, entry);
        // if (evi_entry != NULL && set_index / 64 == 55)
        //   printf("evi r:%d\r\n", evi_entry->reward);
#ifdef PRINT_EQ_LOG
        // 打印所有EQ的内容到eq_buf.log文件中
        if (set_index / sep_num == samp_log) {
          print_evaluation_queues("log/eq_buf.log");
        }

#endif

#ifdef PRINT_LOG
// if(set_index / 64 == 55)
//         log_eq_entry(entry, "log/eq.log");
#endif
      }

      // 逐出一个EQ
      // Naper_EQEntry *evi_entry = dequeue(set_index);
      int32_t reward_evi = 0;
      if (evi_entry != NULL) {
        // 此时我需要赋值奖励，确保之前没有reward
        if (evi_entry->has_reward == 0) {
          // if (evi_entry->has_reward == 0 && entry->hit_eq == 0) {
          // if (evi_entry->trigger == 0) {
          if (evi_entry->hit_eq == 0) {
            if (evi_entry->action_index == BYPASS) {
              reward_evi = R_AC_NR;
            } else {
              reward_evi = R_IN_NR;
            }
          } else {
            // printf("wrong,nevercome\r\n");
            if (evi_entry->action_index == H_EPV) {
              reward_evi = R_AC_NR;
            } else {
              reward_evi = R_IN_NR;
            }
          }
        } else {
          reward_evi = evi_entry->reward;
        }
        // printf("evi\r\n");

        // 考虑在这里更新Q表
        // evi_entry信息
        // int32_t reward_evi = evi_entry->reward;
        uint32_t source_evi = evi_entry->cpu;
        uint32_t addr_evi = evi_entry->address;
        int32_t action_evi = evi_entry->action_index;  // 动作索引
        // head_entry信息
        uint32_t eq_index = set_index / sep_num;  // 计算 EQ 索引
        head_entry = peek_evaluation_queue(eq_index);
        uint32_t source_head = head_entry->cpu;
        uint32_t addr_head = head_entry->address;
        int32_t action_head = head_entry->action_index;  // 动作索引
        // printf("??\r\n");
        float next_q_value = get_q_value(source_head, addr_head, action_head);
        // float next_q_value =
        // q_table[source_head][addr_head].q_values[action_head];
        // printf("??\r\n");
        float now_q = get_q_value(source_evi, addr_evi, action_evi);

        update_q_table(source_evi, addr_evi, action_evi, reward_evi,
                       next_q_value);
        float up_q = get_q_value(source_evi, addr_evi, action_evi);

        float q0 = get_q_value(source_evi, addr_evi, 0);
        float q1 = get_q_value(source_evi, addr_evi, 1);
        // float q2 = get_q_value(source_evi, addr_evi, 2);
        // float q3 = get_q_value(source_evi, addr_evi, 3);

#ifdef PRINT_EQ_LOG
        if (set_index / sep_num == samp_log) {
          FILE *log_file = fopen("log/eq_buf.log", "a");
          if (log_file == NULL) {
            perror("Failed to open log file");
          }
          fprintf(log_file,
                  "src: %d addr: %d act %d R_evi %d next_q %f now_q %f upd_q "
                  "%f q list%f %f \n",
                  source_evi, addr_evi, action_evi, reward_evi, next_q_value,
                  now_q, up_q, q0, q1);
          fclose(log_file);
        }
#endif
      } else {
        // printf("evi null\r\n");
      }
      // free(entry);
      free(evi_entry);
      // free(head_entry);
    }
#ifdef PRINT_EQ_LOG
    // 打印所有EQ的内容到eq_buf.log文件中
    // print_evaluation_queues("log/eq_buf.log");
#endif
  }

  fclose(input_file);

  // 打印结果
  int total_hits = 0, total_access = 0;
  for (int i = 1; i <= NUM_MACHINES; i++) {
    total_hits += hits[i];
    total_access += total_accesses[i];
  }
  double overall_miss_rate = 1.0 - ((double)total_hits / total_access);
  printf("Overall miss rate: %.4f%%\n", overall_miss_rate * 100);

  if (separate_miss_rate) {
    for (int i = 1; i <= NUM_MACHINES; i++) {
      if (total_accesses[i] > 0) {
        double miss_rate = 1.0 - ((double)hits[i] / total_accesses[i]);
        double bypass_rate = ((double)bypass_account[i] / total_accesses[i]);
        printf("Miss rate for load %d: %.4f%%\n", i, miss_rate * 100);
        printf("bypass rate for load %d: %.4f%%\n", i, bypass_rate * 100);
        printf("Total delay for load %d: %lld ns\n", i, delay_per_load[i]);
      }
    }
  }

  printf("Total delay: %lld ns\n", total_delay);
}
