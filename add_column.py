#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本功能：在pagetrace文件的每行末尾添加一列数据（值为1）
使用方法：python add_column.py input_file [output_file]
"""

import sys
import os

def add_column_to_file(input_file, output_file=None, column_value="1"):
    """
    在文件的每行末尾添加一列数据
    
    参数:
    input_file: 输入文件路径
    output_file: 输出文件路径，如果为None则覆盖原文件
    column_value: 要添加的列值，默认为"1"
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：输入文件 '{input_file}' 不存在")
        return False
    
    # 如果没有指定输出文件，则在原文件名基础上添加后缀
    if output_file is None:
        output_file = input_file.replace('.pagetrace', '_modified.pagetrace')
        if output_file == input_file:  # 如果文件名没有.pagetrace后缀
            output_file = input_file + '_modified'
    
    try:
        with open(input_file, 'r') as infile, open(output_file, 'w') as outfile:
            line_count = 0
            for line in infile:
                line = line.strip()  # 去除行尾的换行符和空白字符
                if line:  # 跳过空行
                    # 在行末添加一列数据
                    new_line = line + ' ' + column_value
                    outfile.write(new_line + '\n')
                    line_count += 1
                else:
                    outfile.write('\n')  # 保留空行
        
        print(f"处理完成！")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"处理了 {line_count} 行数据")
        return True
        
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python add_column.py input_file [output_file]")
        print("")
        print("参数说明:")
        print("  input_file   : 输入文件路径")
        print("  output_file  : 输出文件路径（可选，默认为input_file_modified）")
        print("")
        print("示例:")
        print("  python add_column.py gap_bc_new_CN1.pagetrace")
        print("  python add_column.py gap_bc_new_CN1.pagetrace gap_bc_new_CN1_with_column.pagetrace")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    success = add_column_to_file(input_file, output_file)
    if success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()