def add_offset_to_trace(input_file, output_file, offset):
    with open(input_file, 'r') as infile, open(output_file, 'w') as outfile:
        for line in infile:
            try:
                parts = line.split()
                if len(parts) > 2:
                    # 将第三列的地址转换为整数并加上偏置
                    address = int(parts[2]) + offset
                    # 用新的地址替换原有地址
                    parts[2] = str(address)
                    # 将修改后的行写入输出文件
                    outfile.write(' '.join(parts) + '\n')
            except UnicodeDecodeError:
                # Skip lines that cannot be decoded
                continue

if __name__ == "__main__":
    files = [
        # ('gap_bc.pagetrace', 'gap_bc_mix2.pagetrace', 1000000),
        # ('deepsjeng_t2.pagetrace', 'deepsjeng_t2_mix2.pagetrace', 2000000),
        # ('cactusBSSN.pagetrace', 'cactusBSSN_mix2.pagetrace', 3000000),
        # ('spec505_mcf.pagetrace', 'spec505_mcf_mix2.pagetrace', 4000000)
				# ('pr_t3_t1.pagetrace', 'pr_t3_t1_mix1.pagetrace', 1000000),
        # ('spec508_namd.pagetrace', 'spec508_namd_mix1.pagetrace', 2000000),
        # ('spec_lbm.pagetrace', 'spec_lbm_mix1.pagetrace', 3000000),
        # ('gap_cc.pagetrace', 'gap_cc_mix1.pagetrace', 4000000)
				# ('spec_lbm_new.pagetrace', 'spec_lbm_new_off1.pagetrace', 1000000),
        # ('spec_lbm_new.pagetrace', 'spec_lbm_new_off2.pagetrace', 2000000),
        # ('spec_lbm_new.pagetrace', 'spec_lbm_new_off3.pagetrace', 3000000),
        # ('spec_lbm_new.pagetrace', 'spec_lbm_new_off4.pagetrace', 4000000)
				# ('radiosity.pagetrace', 'radiosity_off1.pagetrace', 1000000),
        # ('radiosity.pagetrace', 'radiosity_off2.pagetrace', 2000000),
        # ('radiosity.pagetrace', 'radiosity_off3.pagetrace', 3000000),
        # ('radiosity.pagetrace', 'radiosity_off4.pagetrace', 4000000)
				# ('spec508_namd.pagetrace', 'spec508_namd_off1.pagetrace', 1000000),
        # ('spec508_namd.pagetrace', 'spec508_namd_off2.pagetrace', 2000000),
        # ('spec508_namd.pagetrace', 'spec508_namd_off3.pagetrace', 3000000),
        # ('spec508_namd.pagetrace', 'spec508_namd_off4.pagetrace', 4000000)
				# ('pr_t3.pagetrace', 'pr_t3_off1.pagetrace', 1000000),
        # ('pr_t3.pagetrace', 'pr_t3_off2.pagetrace', 2000000),
        # ('pr_t3.pagetrace', 'pr_t3_off3.pagetrace', 3000000),
        # ('pr_t3.pagetrace', 'pr_t3_off4.pagetrace', 4000000)

				# mix1_pr_namd_lbm_cc_time
				# ('pr_t3.pagetrace', 'pr_t3_mix1.pagetrace', 1000000),
        # ('spec508_namd.pagetrace', 'spec508_namd_mix1.pagetrace', 2000000),
        # ('spec_lbm_new.pagetrace', 'spec_lbm_new_mix1.pagetrace', 3000000),
        # ('gap_cc.pagetrace', 'gap_cc_mix1.pagetrace', 4000000)

				# mix2 ['spec505_mcf.pagetrace','gap_bc.pagetrace', 'gap_deepsjeng.pagetrace', 'cactusBSSN_mix2.pagetrace']
				# ('spec505_mcf.pagetrace', 'spec505_mcf_mix2.pagetrace', 1000000),
        # ('deepsjeng_t2.pagetrace', 'deepsjeng_t2_mix2.pagetrace', 2000000),
        # ('cactusBSSN_new.pagetrace', 'cactusBSSN_new_mix2.pagetrace', 3000000),
        # ('gap_bc_new.pagetrace', 'gap_bc_new_mix2.pagetrace', 4000000)

				# # for 2 CNs
				# ('gap_cc.pagetrace', 'gap_cc_2_CN0.pagetrace', 1000000),
        # ('gap_cc.pagetrace', 'gap_cc_2_CN1.pagetrace', 2000000)

				# for 4 CNs
				# ('gap_sssp.pagetrace', 'gap_sssp_CN0.pagetrace', 1000000),
        # ('gap_sssp.pagetrace', 'gap_sssp_CN1.pagetrace', 2000000),
        # ('gap_sssp.pagetrace', 'gap_sssp_CN2.pagetrace', 3000000),
        # ('gap_sssp.pagetrace', 'gap_sssp_CN3.pagetrace', 4000000)

				# for 4 CNs
				# ('gap_sssp.pagetrace', 'gap_sssp_CN0.pagetrace', 1000000),
        # ('gap_sssp.pagetrace', 'gap_sssp_CN1.pagetrace', 2000000),
        # ('gap_sssp.pagetrace', 'gap_sssp_CN2.pagetrace', 3000000),
        # ('gap_sssp.pagetrace', 'gap_sssp_CN3.pagetrace', 4000000)，
		('trace/gap_sssp.pagetrace', 'trace/gap_sssp_CN1.pagetrace', 5002072),
        ('trace/gap_sssp.pagetrace', 'trace/gap_sssp_CN2.pagetrace', 6040018),
        ('trace/gap_sssp.pagetrace', 'trace/gap_sssp_CN3.pagetrace', 7002093),
        ('trace/gap_sssp.pagetrace', 'trace/gap_sssp_CN4.pagetrace', 8006045)

    ]

    for input_file, output_file, offset in files:
        add_offset_to_trace(input_file, output_file, offset)
