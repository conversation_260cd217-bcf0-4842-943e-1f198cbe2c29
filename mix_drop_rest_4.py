import random

def mix_files(files, output_file, ratios):
    # 解析比例
    ratios = list(map(int, ratios.split(':')))
    total_ratio = sum(ratios)

    # 计算每个文件的选择概率
    probabilities = [r / total_ratio for r in ratios]

    # 打开所有输入文件
    file_handles = [open(file, 'r') for file in files]

    # 创建生成器来逐行读取文件
    def line_generator(file_handle):
        for line in file_handle:
            yield line.strip()

    line_generators = [line_generator(fh) for fh in file_handles]
    indices = [0] * len(files)
    lengths = [len(open(file).readlines()) for file in files]

    with open(output_file, 'w') as out:
        while all(index < length for index, length in zip(indices, lengths)):
            # 选择一个文件
            rand = random.random()
            cumulative_prob = 0
            selected_file = -1
            for i, prob in enumerate(probabilities):
                cumulative_prob += prob
                if rand < cumulative_prob:
                    selected_file = i
                    break

            # 从选定的生成器中获取一行，并附加标识符
            try:
                line = next(line_generators[selected_file])
                out.write(line + f' {selected_file + 1}\n')
                indices[selected_file] += 1
            except StopIteration:
                # 如果某个文件已经读完，跳过
                continue

    # 关闭所有文件
    for fh in file_handles:
        fh.close()

if __name__ == "__main__":
    # files = ['pr_t3_t1.pagetrace', 'spec505_mcf_t1.pagetrace', 'spec519_lbm_t1.pagetrace', 'gap_cc_t1.pagetrace']
		files = ['gap_bc.pagetrace', 'gap_deepsjeng.pagetrace', 'cactusBSSN_mix2.pagetrace', 'spec505_mcf.pagetrace']
		# output_file = 'mix1_pr_mcf_lbm_cc.pagetrace'
		output_file = 'mix2_bc_deepsjeng_cactusBSSN_mcf.pagetrace'
		ratios = '20:20:20:20'  # 你可以根据需要更改比例

		mix_files(files, output_file, ratios)
