import matplotlib.pyplot as plt
import numpy as np
from matplotlib.ticker import ScalarFormatter, FormatStrFormatter

# 工作负载名称 - 只保留b图和d图
workloads = ["SPEC_lbm", "GAP_pr"]

# 缓存策略名称
strategies = ["LRU", "LFU", "LRFU", "Alchemy"]

# 为每个策略分配不同的颜色和标记
colors = ["blue", "orange", "green", "red"]
markers = ["o", "s", "^", "D"]

# 延迟参数
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# 只保留load2和load4的miss rate数据（对应b图和d图）
load2_miss_rate = {
    "LRU": [1.4155, 1.4080, 1.4014, 1.4050, 1.4158, 1.4130, 1.4108],
    "LFU": [1.6005, 1.6500, 1.7184, 1.7100, 1.7001, 1.6700, 1.6413],
    "LRFU": [1.5847, 1.5950, 1.6136, 1.6150, 1.6168, 1.6000, 1.5825],
    "Alchemy": [1.2190, 1.2150, 1.2122, 1.2100, 1.2077, 1.2130, 1.2182],
}

load4_miss_rate = {
    "LRU": [14.7200, 15.1000, 15.5707, 15.3000, 15.1115, 14.7000, 14.2430],
    "LFU": [28.2059, 28.0000, 27.7862, 27.7000, 27.6405, 27.4000, 27.1168],
    "LRFU": [19.1610, 19.5000, 19.8482, 19.3000, 18.7829, 18.4000, 18.0582],
    "Alchemy": [12.5562, 12.7000, 12.8920, 12.8000, 12.7416, 12.6000, 12.4936],
}

# 计算延迟数据
def calculate_latency(miss_rate):
    return (miss_rate / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
            (1 - miss_rate / 100) * (DDR_DELAY + CXL_DELAY))

# 转换miss rate为延迟数据
load2_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load2_miss_rate.items()}

load4_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load4_miss_rate.items()}

# 创建一个1x2的子图布局
fig, axs = plt.subplots(1, 2, figsize=(8, 2.5))

# 创建x轴的7个位置
x = np.arange(7)

# 创建标签列表，只在位置0,2,4,6显示标签
stage_labels = ["", "", "", "", "", "", ""]
stage_labels[0] = "Ckpt. 1"
stage_labels[2] = "Ckpt. 3"
stage_labels[4] = "Ckpt. 5"
stage_labels[6] = "Ckpt. 7"

# 子图标签
subplot_labels = ['(b)', '(d)']

# 绘制两个子图
for i, (ax, workload, data) in enumerate(zip(axs.flatten(), workloads, [load2_data, load4_data])):
    for j, strategy in enumerate(strategies):
        ax.plot(x, data[strategy], color=colors[j], marker=markers[j], label=strategy, linewidth=2, markersize=6)
    
    # 设置每个子图的标题和标签
    ax.set_title(f"{workload}", fontsize=12, fontweight="bold")
    
    # 只为左侧图设置y轴标签
    if i % 2 == 0:
        ax.set_ylabel("Avg Access Latency (ns)", fontsize=11)
    
    # 设置刻度参数
    ax.tick_params(axis='y', labelsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(stage_labels, fontsize=12.5)
    
    ax.grid(True, linestyle="--", alpha=0.7)
    
    # 设置科学计数法
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0))
    
    # 调整x轴范围
    ax.set_xlim(-0.2, len(x)-0.8)
    
    # 添加子图标签 (b)(d)
    ax.text(-0.09, 1.1, subplot_labels[i], transform=ax.transAxes, fontsize=14, 
             va='top', ha='left')

# 自定义格式化器，强制使用1位小数
class MyScalarFormatter(ScalarFormatter):
    def __init__(self, decimal_places=1):
        super().__init__(useOffset=True, useMathText=True)
        self.decimal_places = decimal_places
        
    def _set_format(self):
        # 调用父类方法设置基本格式
        super()._set_format()
        # 强制使用指定的小数位数
        self.format = f'%1.{self.decimal_places}f'

# 为所有子图应用自定义格式化器
for ax in axs.flatten():
    formatter = MyScalarFormatter(1)  # 1位小数
    ax.yaxis.set_major_formatter(formatter)
    # 确保科学计数法指数移至顶部
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0), useMathText=True)

# 添加共享图例
handles, labels = axs[0].get_legend_handles_labels()
fig.legend(handles, labels, loc='upper center', bbox_to_anchor=(0.5, -0.0), 
           ncol=4, fontsize=12, frameon=True)

# 手动调整子图间距
plt.subplots_adjust(left=0.1, right=0.95, bottom=0.1, top=0.85, wspace=0.2, hspace=0.3)

# 保存并显示图形
plt.savefig("cache_strategies_comparison_latency_bd_only.pdf", dpi=300, bbox_inches="tight")
plt.show()
