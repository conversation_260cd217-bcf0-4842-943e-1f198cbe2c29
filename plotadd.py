import matplotlib.pyplot as plt
import numpy as np
from collections import Counter

# 定义采样比例因子
scale_factor = 10  # 你可以根据需要调整这个值

# 初始化数据列表
addresses = []

# 读取文件数据
with open('radiosity_off1_4CNs.pagetrace', 'r') as file:
    for i, line in enumerate(file):
        if i % scale_factor != 0:
            continue  # 采样：跳过不需要的行

        parts = line.split()
        address = int(parts[2])
        addresses.append(address)

# 计算每个地址的访问次数
address_counts = Counter(addresses)
unique_addresses = list(address_counts.keys())
access_counts = list(address_counts.values())

# 绘制图形
plt.figure(figsize=(12, 6))
plt.bar(unique_addresses, access_counts, width=1.0, color='blue', alpha=0.7)

# 设置图形标签和标题
plt.xlabel('Memory Address')
plt.ylabel('Access Count')
plt.title('Access Count per Memory Address')

# 保存图形到文件
plt.savefig('address_access_counts.png', dpi=300, bbox_inches='tight')

# 显示图形
plt.show()
