import heapq

def mix_files_by_arrival_time(files, output_file):
    # 打开所有输入文件
    file_handles = [open(file, 'r') for file in files]

    # 创建一个优先队列（最小堆）
    min_heap = []
    files_finished = [False] * len(files)  # 记录每个文件是否已处理完

    # 初始化堆，将每个文件的第一行放入堆中
    for file_index, fh in enumerate(file_handles):
        line = fh.readline().strip()
        if line:
            parts = line.split()
            arrival_time = int(parts[0])  # 假设到达时间在第一列
            heapq.heappush(min_heap, (arrival_time, line, file_index))

    # 打开输出文件
    with open(output_file, 'w') as out:
        # 不断从堆中取出最小的元素
        while min_heap:
            arrival_time, line, file_index = heapq.heappop(min_heap)
            out.write(f'{line} {file_index + 1}\n')

            # 从同一个文件读取下一行
            next_line = file_handles[file_index].readline().strip()
            if next_line:
                parts = next_line.split()
                next_arrival_time = int(parts[0])
                heapq.heappush(min_heap, (next_arrival_time, next_line, file_index))
            else:
                # 标记该文件已处理完
                files_finished[file_index] = True

            # 如果有任何一个文件已处理完，则停止处理
            if any(files_finished):
                break

    # 关闭所有文件
    for fh in file_handles:
        fh.close()

if __name__ == "__main__":
    # files = ['pr_t3_t1.pagetrace', 'spec505_mcf_t1.pagetrace', 'spec519_lbm_t1.pagetrace', 'gap_cc_t1.pagetrace']
    # output_file = 'mix1_pr_mcf_lbm_cc_time.pagetrace'

		files = ['gap_bc_mix2.pagetrace', 'gap_deepsjeng_mix2.pagetrace', 'cactusBSSN_mix2.pagetrace', 'spec505_mcf_mix2.pagetrace']
		output_file = 'mix2_bc_deepsjeng_cactusBSSN_mcf_time.pagetrace'

		mix_files_by_arrival_time(files, output_file)
