#include "cache.h"

#include <limits.h>

#include "eq.h"
#include "stdio.h"
#include "stdlib.h"
CachePage cache[NUM_SETS][LINES_PER_SET];
int fifo_counter[NUM_SETS] = {0};
// int strategy = 1;

void init_cache() {
  for (int i = 0; i < NUM_SETS; i++) {
    for (int j = 0; j < LINES_PER_SET; j++) {
      cache[i][j].valid = 0;
      cache[i][j].lru_counter = 0;
      cache[i][j].fifo_order = 0;
      cache[i][j].load_id = -1;
    }
  }

  init_evaluation_queues();  // 初始化 EQ
}

void evict_and_load(int set_index, int load_index,
                    unsigned long long page_number, int load_id) {
  // 更新缓存行信息
  cache[set_index][load_index].page_number = page_number;
  cache[set_index][load_index].valid = 1;
  cache[set_index][load_index].load_id = load_id;
  cache[set_index][load_index].rrpv = INIT_RRPV;  // 初始化RRPV

}
void load_to_cache_rrip(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  while (1) {
    for (int i = 0; i < LINES_PER_SET; i++) {
      if (!cache[set_index][i].valid || cache[set_index][i].rrpv == MAX_RRPV) {
        evict_and_load(set_index, i, page_number, load_id);
        return;
      }
    }
    for (int i = 0; i < LINES_PER_SET; i++) {
      cache[set_index][i].rrpv++;
    }
  }
}

int find_in_cache(unsigned long long page_number, long load_id) {
  int set_index = page_number % NUM_SETS;
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (cache[set_index][i].valid &&
        cache[set_index][i].page_number == page_number) {
      return i;
    }
  }
  return -1;
}
int find_in_cache_and_update_epv(unsigned long long page_number, long load_id,
                                 int epv) {
  int set_index = page_number % NUM_SETS;
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (cache[set_index][i].valid &&
        cache[set_index][i].page_number == page_number) {
      // 找到缓存行，更新EPV
      cache[set_index][i].epv = epv;
      return i;
    }
  }
  printf("not found\r\n");
  return -1;
}

void update_lru(int set_index, int accessed_index) {
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (cache[set_index][i].valid) {
      cache[set_index][i].lru_counter++;
    }
  }
  cache[set_index][accessed_index].lru_counter = 0;
}

void load_to_cache_lru(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  int lru_index = 0;
  int max_counter = -1;

  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid) {
      lru_index = i;
      break;
    }
    if (cache[set_index][i].lru_counter > max_counter) {
      max_counter = cache[set_index][i].lru_counter;
      lru_index = i;
    }
  }

  cache[set_index][lru_index].page_number = page_number;
  cache[set_index][lru_index].valid = 1;
  cache[set_index][lru_index].load_id = load_id;
  update_lru(set_index, lru_index);
}


// 分情况讨论epv 2 1 0 3
void load_to_cache_lru_evp(unsigned long long page_number, int load_id,
                           int epv) {
  int set_index = page_number % NUM_SETS;
  int lru_index = -1;
  int max_counter = -1;

  switch (epv) {
    case M_EPV:  // 中等优先级
      // 优先替换 EPV 为 1 的条目
      for (int i = 0; i < LINES_PER_SET; i++) {
        if (!cache[set_index][i].valid) {
          lru_index = i;
          break;
        }
        if (cache[set_index][i].epv == H_EPV) {
          lru_index = i;
          break;
        }
      }
      // 如果没有找到 EPV 为 1 的条目，寻找 EPV 为 0 的条目中 LRU 的条目
      if (lru_index == -1) {
        for (int i = 0; i < LINES_PER_SET; i++) {
          if (cache[set_index][i].epv == M_EPV) {
            if (cache[set_index][i].lru_counter > max_counter) {
              max_counter = cache[set_index][i].lru_counter;
              lru_index = i;
            }
          }
        }
      }
      // 如果没有找到合适的，选择第一行
      if (lru_index == -1) {
        lru_index = 0;
				// printf("??");
      }
      break;

    case H_EPV:  // 最容易逐出
      // 直接根据 LRU 替换 EPV 为 1 的条目
      for (int j = 0; j < LINES_PER_SET; j++) {
        if (!cache[set_index][j].valid) {
          lru_index = j;
          break;
        }
        if (cache[set_index][j].epv == H_EPV) {
          if (cache[set_index][j].lru_counter > max_counter) {
            max_counter = cache[set_index][j].lru_counter;
            lru_index = j;
          }
        }
      }
			if(lru_index == -1){
				lru_index = 0;
				// printf("something wrong\r\n");
			}
      break;

    case L_EPV:  // 最不容易逐出
      // 优先替换 EPV 为 0 的条目
      for (int k = 0; k < LINES_PER_SET; k++) {
        if (!cache[set_index][k].valid) {
          lru_index = k;
          break;
        }
        if (cache[set_index][k].epv == H_EPV) {
          lru_index = k;
          break;
        }
      }
      // 如果没有找到 EPV 为 0 的条目，寻找 EPV 为 1 的条目
      if (lru_index == -1) {
        for (int k = 0; k < LINES_PER_SET; k++) {
          if (cache[set_index][k].epv == M_EPV) {
            lru_index = k;
            break;
          }
        }
      }
      // 如果没有找到合适的，直接根据 LRU 替换 EPV 为 2 的条目
      if (lru_index == -1) {
        for (int k = 0; k < LINES_PER_SET; k++) {
          if (cache[set_index][k].epv == L_EPV) {
            if (cache[set_index][k].lru_counter > max_counter) {
              max_counter = cache[set_index][k].lru_counter;
              lru_index = k;
            }
          }
        }
      }
      break;

    default:
      printf("Invalid EPV value\n");
      return;
  }

  // 执行替换
  if (lru_index != -1) {
    cache[set_index][lru_index].page_number = page_number;
    cache[set_index][lru_index].valid = 1;
    cache[set_index][lru_index].load_id = load_id;
    cache[set_index][lru_index].epv = epv;  // 设置EPV
    update_lru(set_index, lru_index);
  } else {
    printf("No suitable entry found for replacement\n");
  }
}

void load_to_cache_evp(unsigned long long page_number, int load_id, int epv) {
  int set_index = page_number % NUM_SETS;
  int replace_index = -1;

  // 查找EPV低于输入EPV的缓存行
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid || cache[set_index][i].epv < epv) {
      replace_index = i;
      break;
    }
  }

  // 如果没有找到EPV低于输入EPV的缓存行，选择第一行进行替换
  if (replace_index == -1) {
    replace_index = 0;
    // printf("No lower EPV found, replacing first line in set %d\n",
    // set_index);
  }

  // 执行替换操作
  cache[set_index][replace_index].page_number = page_number;
  cache[set_index][replace_index].valid = 1;
  cache[set_index][replace_index].load_id = load_id;
  cache[set_index][replace_index].epv = epv;  // 设置EPV
  update_lru(set_index, replace_index);
}

void load_to_cache_fifo(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  int fifo_index = 0;
  int oldest_order = INT_MAX;

  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid) {
      fifo_index = i;
      break;
    }
    if (cache[set_index][i].fifo_order < oldest_order) {
      oldest_order = cache[set_index][i].fifo_order;
      fifo_index = i;
    }
  }

  cache[set_index][fifo_index].page_number = page_number;
  cache[set_index][fifo_index].valid = 1;
  cache[set_index][fifo_index].fifo_order = fifo_counter[set_index]++;
  cache[set_index][fifo_index].load_id = load_id;
}
