#include "top.h"

#include "cache.h"
#include "config.h"
#include "eq.h"
#include "q_table.h"

int samp_log = 6;

int Alchemy_top(unsigned long long page_number, long load_id, int cache_index) {
  int set_index = page_number % NUM_SETS;  // 获取映射到cache set的组号
  uint32_t PN = (uint32_t)(page_number & 0xFFFFFF);  // 取page_number的后15位
  uint32_t source = (uint32_t)load_id;  // 直接使用load_id作为source
  int find_in_eq = 0;
  int action_now = H_EPV;
  int32_t reward_evi = 0;
  // 检查是否在采样集中	阶段1，chrome中分配奖励这部分
  if (set_index % sep_num == 0) {
    // printf("sample set %d\r\n", set_index);
    // 查看该地址在EQ中
    if (set_index / sep_num == 6) {
      // printf("test\r\n");
    }
    Naper_EQEntry check_entry;
    int check_bool = 0;
    check_bool =
        find_in_evaluation_queue((set_index / sep_num), PN, &check_entry);
    if (check_bool != 0) {
      // printf("found");
      find_in_eq = 1;
      // 该缓存在cache中，分配奖励
      if (check_entry.has_reward == false) {
        // if (cache_index != -1) {
        if (check_entry.in_cache == true) {  // 命中，说明之前选择正确
          int index_reward = find_eq_entry_index(set_index / sep_num, PN);
          modify_eq_entry_reward(set_index / sep_num, index_reward, R_AC);
        } else {  // 在之前被逐出了
          int index_reward = find_eq_entry_index(set_index / sep_num, PN);
          modify_eq_entry_reward(set_index / sep_num, index_reward, R_IN);
        }
      } else {
        // 调试用
        if (set_index / sep_num == samp_log) {
          printf("reward fail : PN %d", PN);
        }
        int index_reward = find_eq_entry_index(set_index / sep_num, PN);
        modify_eq_entry_reward(set_index / sep_num, index_reward, 6);
      }
    } else {
      // 无事发生
    }
  }
  // 根据source和PN选取动作
  if (cache_index != -1) {  // 命中，只选择EPV
    action_now = select_action_hit(source, PN);
  } else {  // 未命中，有bpyass的选项
    action_now = select_action(source, PN);
  }

  // 记录sampled set的数据进入EQ
  if (set_index % sep_num == 0) {  // 检查是否是采样的 set index
                                   // 即将要加入的EQ
    Naper_EQEntry entry;
    Naper_EQEntry evi_entry;
    Naper_EQEntry head_entry;
    int has_evi = 0;

    entry.address = PN;
    entry.action_index = action_now;                // 根据实际情况设置
                                                    // if (action_now != BYPASS)
                                                    //   entry->in_cache = 1;
                                                    // else
                                                    //   entry->in_cache = 0;
    if (cache_index != -1 || action_now != BYPASS)  // 命中
      entry.in_cache = true;
    else
      entry.in_cache = false;

    if (find_in_eq == 1)  // 命中
    {
      entry.hit_eq = true;
      // printf("hit\t\n");
    } else {
      entry.hit_eq = false;
    }

    entry.cpu = load_id;  // 根据实际情况设置
    entry.has_reward = false;
    entry.reward = 0;
    entry.position = 0;
    entry.trigger = false;
    entry.cache_set = set_index;

    uint32_t eq_index = set_index / sep_num;

    has_evi = enqueue(eq_index, &entry, &evi_entry);
#ifdef PRINT_EQ_LOG
    //   打印所有EQ的内容到eq_buf.log文件中
    if (set_index / sep_num == samp_log) {
      print_evaluation_queues("log/eq_buf.log");
    }

#endif

    if (has_evi != 0) {
      // 此时我需要赋值奖励，确保之前没有reward
      if (evi_entry.has_reward == 0) {
        // if (evi_entry->has_reward == 0 && entry->hit_eq == 0) {
        // if (evi_entry->trigger == 0) {
        if (evi_entry.hit_eq == 0) {
          if (evi_entry.action_index == BYPASS) {
            reward_evi = R_AC_NR;
          } else {
            reward_evi = R_IN_NR;
          }
        } else {
          // printf("wrong,nevercome\r\n");
          if (evi_entry.action_index == H_EPV) {
            reward_evi = R_AC_NR;
          } else {
            reward_evi = R_IN_NR;
          }
        }
      } else {
        reward_evi = evi_entry.reward;
      }

      // 考虑在这里更新Q表
      // evi_entry信息
      uint32_t source_evi = evi_entry.cpu;
      uint32_t addr_evi = evi_entry.address;
      int32_t action_evi = evi_entry.action_index;  // 动作索引

      // head_entry信息
      uint32_t eq_index = set_index / sep_num;  // 计算 EQ 索引
      int head_bool = peek_evaluation_queue(eq_index, &head_entry);
      uint32_t source_head = head_entry.cpu;
      uint32_t addr_head = head_entry.address;
      int32_t action_head = head_entry.action_index;  // 动作索引
      float next_q_value = get_q_value(source_head, addr_head, action_head);

      update_q_table(source_evi, addr_evi, action_evi, reward_evi,
                     next_q_value);

#ifdef PRINT_EQ_LOG

      float now_q = get_q_value(source_evi, addr_evi, action_evi);
      float up_q = get_q_value(source_evi, addr_evi, action_evi);
      float q0 = get_q_value(source_evi, addr_evi, 0);
      float q1 = get_q_value(source_evi, addr_evi, 1);
      float q2 = get_q_value(source_evi, addr_evi, 2);
      float q3 = get_q_value(source_evi, addr_evi, 3);
      if (set_index / sep_num == samp_log) {
        FILE *log_file = fopen("log/eq_buf.log", "a");
        if (log_file == NULL) {
          perror("Failed to open log file");
        }
        fprintf(log_file,
                "src: %d addr: %d act %d R_evi %d next_q %f now_q %f upd_q "
                "%f q list %f %f %f %f\n",
                source_evi, addr_evi, action_evi, reward_evi, next_q_value,
                now_q, up_q, q0, q1, q2, q3);
        fclose(log_file);
      }
#endif
    } else {
      // printf("evi null\r\n");
    }
    // free(evi_entry);
  }
  return action_now;
}