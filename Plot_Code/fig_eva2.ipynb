import matplotlib.pyplot as plt
import numpy as np

# 设置x轴的方差值
x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]

# 定义常量和数据
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# 使用NumPy数组来存储数据以便进行计算
miss_rate1 = np.array([
    [13.5245, 13.5245, 13.5245, 13.5245],  # 0
    [13.3654,13.9613,13.7722,12.9922],  # 0.05
    [13.8773,12.5561,13.6145,13.2483],   # 0.1
    [14.8649,12.1167,14.3195,12.7638],  # 0.15
    [13.7527,12.7334,12.0161,16.1458],   # 0.2
    [17.1195,13.3654,12.3517,11.7299],   # 0.25
    [11.4717,11.5630,16.3082,15.6864],    # 0.3
    [17.2171,10.4774,15.2132,12.6276],  # 0.35
    [19.9405,10.6939,12.9488,12.482],   # 0.4
    [10.2857,11.3586,17.6018,17.3128],   # 0.45
    [10.4085,18.8821,10.2195,17.3646]    # 0.5
])

miss_rate2 = np.array([
    [13.4913, 13.7627, 13.7659, 13.4231],  # 0
    [11.9021, 10.7415, 11.7794, 12.2140],  # 0.05
    [11.8459, 12.7726, 12.7264, 9.8086],   # 0.1
    [10.9287, 10.3429, 10.1843, 11.7396],  # 0.15
    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2
    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25
    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3
    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35
    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4
    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45
    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5
])

miss_rate3 = np.array([
    [12.8445,13.1008,13.0639,12.8571],  # 0
    [11.4220,10.2345,11.4041,11.8096],  # 0.05
    [11.6084,12.1991,12.1368,9.3781],   # 0.1
    [10.5929,9.7987,9.6842,11.7209],  # 0.15
    [11.6801,9.5426,10.3566,8.9833],   # 0.2
    [9.8340,8.4032,11.6565,9.3683],   # 0.25
    [11.8439,8.2294,9.5093,9.5904],    # 0.3
    [10.4275,11.3183,7.4935,11.7701],  # 0.35
    [12.1999,8.7277,7.6005,11.4067],   # 0.4
    [7.5993,7.9855,12.7783,10.3074],   # 0.45
    [12.4341,10.0069,9.1605,6.6899]    # 0.5
])

# 计算memory_performance_data
memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))

# 计算最大值和最小值的差异并用最小值进行归一化
diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)
diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)
diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)

# 创建折线图
plt.figure(figsize=(10, 3))

plt.plot(x_variances, diff3, marker='o', color='red', label='Shared Alchemy')
plt.plot(x_variances, diff1, marker='o', color='blue', label='Partition LRU')
plt.plot(x_variances, diff2, marker='o', color='green', label='Shared LRU')

# 设置图表标签和标题
plt.xlabel('Standard Deviation of CN Performance', fontsize=16)
plt.ylabel('Normalized Max\nDiff in Avg Latency', fontsize=16)

# 设置x轴刻度
plt.xticks(x_variances, fontsize=16)
plt.yticks(fontsize=16)  # 设置y轴刻度的字体大小

# 添加图例
plt.legend(loc='upper left', fontsize=15)

# 显示图表
plt.grid(True)
plt.savefig('eva_fig2.pdf', dpi=300, bbox_inches='tight')
plt.show()


import matplotlib.pyplot as plt
import numpy as np

# 设置x轴的方差值
x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]

# 定义常量和数据
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# 使用NumPy数组来存储数据以便进行计算
miss_rate1 = np.array([
    [13.5245, 13.5245, 13.5245, 13.5245],  # 0
    [13.3654,13.9613,13.7722,12.9922],  # 0.05
    [13.8773,12.5561,13.6145,13.2483],   # 0.1
    [14.8649,12.1167,14.3195,12.7638],  # 0.15
    [13.7527,12.7334,12.0161,16.1458],   # 0.2
    [17.1195,13.3654,12.3517,11.7299],   # 0.25
    [11.4717,11.5630,16.3082,15.6864],    # 0.3
    [17.2171,10.4774,15.2132,12.6276],  # 0.35
    [19.9405,10.6939,12.9488,12.482],   # 0.4
    [10.2857,11.3586,17.6018,17.3128],   # 0.45
    [10.4085,18.8821,10.2195,17.3646]    # 0.5
])

miss_rate2 = np.array([
    [13.4913, 13.7627, 13.7659, 13.4231],  # 0
    [11.9021, 10.7415, 11.7794, 12.2140],  # 0.05
    [11.8459, 12.7726, 12.7264, 9.8086],   # 0.1
    [10.9287, 10.3429, 10.1843, 11.7396],  # 0.15
    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2
    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25
    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3
    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35
    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4
    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45
    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5
])

miss_rate3 = np.array([
    [12.8445,13.1008,13.0639,12.8571],  # 0
    [11.4220,10.2345,11.4041,11.8096],  # 0.05
    [11.6084,12.1991,12.1368,9.3781],   # 0.1
    [10.5929,9.7987,9.6842,11.7209],  # 0.15
    [11.6801,9.5426,10.3566,8.9833],   # 0.2
    [9.8340,8.4032,11.6565,9.3683],   # 0.25
    [11.8439,8.2294,9.5093,9.5904],    # 0.3
    [10.4275,11.3183,7.4935,11.7701],  # 0.35
    [12.1999,8.7277,7.6005,11.4067],   # 0.4
    [7.5993,7.9855,12.7783,10.3074],   # 0.45
    [12.4341,10.0069,9.1605,6.6899]    # 0.5
])

# 计算memory_performance_data
memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))

# 计算最大值和最小值的差异并用最小值进行归一化
diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)
diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)
diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)

# 创建折线图
plt.figure(figsize=(10, 4))  # 调整图表大小

plt.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')
plt.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='Partition LRU')
plt.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')

# 设置图表标签和标题
plt.xlabel('Standard Deviation of CN Performance', fontsize=16)
plt.ylabel('Normalized Max\nDiff in Avg Latency', fontsize=16)

# 设置x轴刻度
plt.xticks(x_variances, fontsize=14)
plt.yticks(fontsize=14)

# 添加图例
plt.legend(loc='upper left', fontsize=14, frameon=False)

# 添加网格
plt.grid(True, linestyle='--', alpha=0.7)

# 显示图表
plt.tight_layout()
plt.savefig('eva_fig2.pdf', dpi=300, bbox_inches='tight')
plt.show()


import matplotlib.pyplot as plt
import numpy as np
import matplotlib.gridspec as gridspec

# 设置x轴的方差值
x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]

# 定义常量和数据
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# 使用NumPy数组来存储数据以便进行计算
miss_rate1 = np.array([
    [13.5245, 13.5245, 13.5245, 13.5245],  # 0
    [13.3654,13.9613,13.7722,12.9922],  # 0.05
    [13.8773,12.5561,13.6145,13.2483],   # 0.1
    [14.8649,12.1167,14.3195,12.7638],  # 0.15
    [13.7527,12.7334,12.0161,16.1458],   # 0.2
    [17.1195,13.3654,12.3517,11.7299],   # 0.25
    [11.4717,11.5630,16.3082,15.6864],    # 0.3
    [17.2171,10.4774,15.2132,12.6276],  # 0.35
    [19.9405,10.6939,12.9488,12.482],   # 0.4
    [10.2857,11.3586,17.6018,17.3128],   # 0.45
    [10.4085,18.8821,10.2195,17.3646]    # 0.5
])

miss_rate2 = np.array([
    [13.4913, 13.7627, 13.7659, 13.4231],  # 0
    [11.9021, 10.7415, 11.7794, 12.2140],  # 0.05
    [11.8459, 12.7726, 12.7264, 9.8086],   # 0.1
    [10.9287, 10.3429, 10.1843, 11.7396],  # 0.15
    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2
    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25
    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3
    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35
    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4
    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45
    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5
])

miss_rate3 = np.array([
    [12.8445,13.1008,13.0639,12.8571],  # 0
    [11.4220,10.2345,11.4041,11.8096],  # 0.05
    [11.6084,12.1991,12.1368,9.3781],   # 0.1
    [10.5929,9.7987,9.6842,11.7209],  # 0.15
    [11.6801,9.5426,10.3566,8.9833],   # 0.2
    [9.8340,8.4032,11.6565,9.3683],   # 0.25
    [11.8439,8.2294,9.5093,9.5904],    # 0.3
    [10.4275,11.3183,7.4935,11.7701],  # 0.35
    [12.1999,8.7277,7.6005,11.4067],   # 0.4
    [7.5993,7.9855,12.7783,10.3074],   # 0.45
    [12.4341,10.0069,9.1605,6.6899]    # 0.5
])

# 计算memory_performance_data
memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))

# 计算最大值和最小值的差异并用最小值进行归一化
diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)
diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)
diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)

# 计算平均值
avg_latency1 = np.mean(memory_performance_data1, axis=1) #PARTITION LRU
avg_latency2 = np.mean(memory_performance_data2, axis=1) #SHARED LRU
avg_latency3 = np.mean(memory_performance_data3, axis=1) #SHARED Alchemy

# 选择特定的x轴数据
selected_indices = [2, 6, 10]  # 对应于x_variances中的0.1, 0.3, 0.5
selected_x = [x_variances[i] for i in selected_indices]
selected_avg_latency1 = [avg_latency1[i] for i in selected_indices]
selected_avg_latency2 = [avg_latency2[i] / avg_latency1[i] for i in selected_indices]  # 归一化
selected_avg_latency3 = [avg_latency3[i] / avg_latency1[i] for i in selected_indices]  # 归一化

# 创建图表，并使用gridspec布局
fig = plt.figure(figsize=(14, 3.5))
gs = gridspec.GridSpec(1, 2, width_ratios=[7, 3])  # 70% for a图, 30% for b图

# 绘制第一个子图 (a图)
ax1 = fig.add_subplot(gs[0])

# 绘制第一个y轴的数据
# ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')
ax1.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='Partition LRU')
ax1.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')
ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')
# 设置第一个y轴的标签
ax1.set_xlabel('Standard Deviation of CN Performance', fontsize=18)
ax1.set_ylabel('Normalized Max\nDiff in Avg Latency', fontsize=18)

# 设置x轴和y轴刻度
ax1.set_xticks(x_variances)
ax1.tick_params(axis='x', labelsize=18)
ax1.tick_params(axis='y', labelsize=20)

# 添加图例
ax1.legend(loc='upper left', fontsize=16, frameon=False)
# Add (a) label
ax1.text(-0.04, max(diff3) + 0.075, '(a)', fontsize=18, fontweight='bold', va='top', ha='right')

# 添加网格
ax1.grid(True, linestyle='--', alpha=0.7)

# 绘制第二个子图 (b图)
ax2 = fig.add_subplot(gs[1])

# 绘制柱状图
bar_width = 0.2
index = np.arange(len(selected_x))

colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']  # 指定颜色

ax2.bar(index, [1]*len(selected_avg_latency1), bar_width, color=colors[0], label='Partition LRU')  # 基准
ax2.bar(index + bar_width, selected_avg_latency2, bar_width, color=colors[1], label='Shared LRU')
ax2.bar(index + 2 * bar_width, selected_avg_latency3, bar_width, color=colors[2], label='Shared Alchemy')

# 设置右侧柱状图的 y 轴范围
ax2.set_ylim(0.5, 1.19)

# 设置第二个y轴的标签
ax2.set_xlabel('Selected Standard Deviations', fontsize=16)
ax2.set_ylabel('Normalized Average\nAccess Latency', fontsize=16)
ax2.set_xticks(index + bar_width)
ax2.set_xticklabels(['0.1', '0.3', '0.5'])
ax2.tick_params(axis='x', labelsize=20)
ax2.tick_params(axis='y', labelsize=17)

# 添加第二个图例
ax2.legend(loc='upper right', fontsize=14, ncol=1)
# Add (b) label
ax2.text(-0.35, 1.2, '(b)', fontsize=18, fontweight='bold', va='top', ha='right')

# # 添加网格
# ax2.grid(True, linestyle='--', alpha=0.7)
# 增加子图之间的间距
# gs.update(wspace=0.28)  # 你可以根据需要调整这个值
# 调整布局
plt.tight_layout()
plt.savefig('eva_heter.pdf', dpi=300, bbox_inches='tight')
plt.show()


import matplotlib.pyplot as plt
import numpy as np
import matplotlib.gridspec as gridspec

# 设置x轴的方差值
x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]

# 定义常量和数据
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# 使用NumPy数组来存储数据以便进行计算
miss_rate1 = np.array([
    [13.5245, 13.5245, 13.5245, 13.5245],  # 0
    [13.3654,13.9613,13.7722,12.9922],  # 0.05
    [13.8773,12.5561,13.6145,13.2483],   # 0.1
    [14.8649,12.1167,14.3195,12.7638],  # 0.15
    [13.7527,12.7334,12.0161,16.1458],   # 0.2
    [17.1195,13.3654,12.3517,11.7299],   # 0.25
    [11.4717,11.5630,16.3082,15.6864],    # 0.3
    [17.2171,10.4774,15.2132,12.6276],  # 0.35
    [19.9405,10.6939,12.9488,12.482],   # 0.4
    [10.2857,11.3586,17.6018,17.3128],   # 0.45
    [10.4085,18.8821,10.2195,17.3646]    # 0.5
])

miss_rate2 = np.array([
    [13.4913, 13.7627, 13.7659, 13.4231],  # 0
    [11.9021, 10.7415, 11.7794, 12.2140],  # 0.05
    [11.8459, 12.7726, 12.7264, 9.8086],   # 0.1
    [10.9287, 10.3429, 10.1843, 11.7396],  # 0.15
    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2
    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25
    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3
    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35
    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4
    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45
    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5
])

miss_rate3 = np.array([
    [12.8445,13.1008,13.0639,12.8571],  # 0
    [11.4220,10.2345,11.4041,11.8096],  # 0.05
    [11.6084,12.1991,12.1368,9.3781],   # 0.1
    [10.5929,9.7987,9.6842,11.7209],  # 0.15
    [11.6801,9.5426,10.3566,8.9833],   # 0.2
    [9.8340,8.4032,11.6565,9.3683],   # 0.25
    [11.8439,8.2294,9.5093,9.5904],    # 0.3
    [10.4275,11.3183,7.4935,11.7701],  # 0.35
    [12.1999,8.7277,7.6005,11.4067],   # 0.4
    [7.5993,7.9855,12.7783,10.3074],   # 0.45
    [12.4341,10.0069,9.1605,6.6899]    # 0.5
])

# 计算memory_performance_data
memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))

# 计算最大值和最小值的差异并用最小值进行归一化
diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)
diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)
diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)

# 计算平均值
avg_latency1 = np.mean(memory_performance_data1, axis=1) #PARTITION LRU
avg_latency2 = np.mean(memory_performance_data2, axis=1) #SHARED LRU
avg_latency3 = np.mean(memory_performance_data3, axis=1) #SHARED Alchemy

# 选择特定的x轴数据
selected_indices = [2, 6, 10]  # 对应于x_variances中的0.1, 0.3, 0.5
selected_x = [x_variances[i] for i in selected_indices]
selected_avg_latency1 = [avg_latency1[i] for i in selected_indices]
selected_avg_latency2 = [avg_latency2[i] / avg_latency1[i] for i in selected_indices]  # 归一化
selected_avg_latency3 = [avg_latency3[i] / avg_latency1[i] for i in selected_indices]  # 归一化

# 创建图表，并使用gridspec布局
fig = plt.figure(figsize=(14, 3.5))
gs = gridspec.GridSpec(1, 2, width_ratios=[7, 3])  # 70% for a图, 30% for b图

# 绘制第一个子图 (a图)
ax1 = fig.add_subplot(gs[0])

# 绘制第一个y轴的数据
# ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')
ax1.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='Partition LRU')
ax1.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')
ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')
# 设置第一个y轴的标签
ax1.set_xlabel('Standard Deviation of CN Performance', fontsize=18)
ax1.set_ylabel('Normalized Max\nDiff in Avg Latency', fontsize=18)

# 设置x轴和y轴刻度
ax1.set_xticks(x_variances)
ax1.tick_params(axis='x', labelsize=18)
ax1.tick_params(axis='y', labelsize=20)

# 添加图例
ax1.legend(loc='upper left', fontsize=16, frameon=False)
# Add (a) label
ax1.text(-0.04, max(diff3) + 0.075, '(a)', fontsize=18, fontweight='bold', va='top', ha='right')

# 添加网格
ax1.grid(True, linestyle='--', alpha=0.7)

# 绘制第二个子图 (b图)
ax2 = fig.add_subplot(gs[1])

# 绘制柱状图
bar_width = 0.2
index = np.arange(len(selected_x))

colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']  # 指定颜色

ax2.bar(index, [1]*len(selected_avg_latency1), bar_width, color=colors[0], label='Partition LRU')  # 基准
ax2.bar(index + bar_width, selected_avg_latency2, bar_width, color=colors[1], label='Shared LRU')
ax2.bar(index + 2 * bar_width, selected_avg_latency3, bar_width, color=colors[2], label='Shared Alchemy')

# 设置右侧柱状图的 y 轴范围
ax2.set_ylim(0.5, 1.19)

# 设置第二个y轴的标签
ax2.set_xlabel('Selected Standard Deviations', fontsize=16)
ax2.set_ylabel('Normalized Average\nAccess Latency', fontsize=16)
ax2.set_xticks(index + bar_width)
ax2.set_xticklabels(['0.1', '0.3', '0.5'])
ax2.tick_params(axis='x', labelsize=20)
ax2.tick_params(axis='y', labelsize=17)

# 添加第二个图例
ax2.legend(loc='upper right', fontsize=14, ncol=1)
# Add (b) label
ax2.text(-0.35, 1.2, '(b)', fontsize=18, fontweight='bold', va='top', ha='right')

# # 添加网格
# ax2.grid(True, linestyle='--', alpha=0.7)
# 增加子图之间的间距
# gs.update(wspace=0.28)  # 你可以根据需要调整这个值
# 调整布局
plt.tight_layout()
plt.savefig('eva_heter.pdf', dpi=300, bbox_inches='tight')
plt.show()
print(selected_avg_latency1)
print(selected_avg_latency2)
print(selected_avg_latency3)
# 打印图b的三个柱子的值
print("Partition LRU (基准):", [1] * len(selected_avg_latency1))  # 基准值为1
print("Shared LRU (归一化):", selected_avg_latency2)
print("Shared Alchemy (归一化):", selected_avg_latency3)


import matplotlib.pyplot as plt
import numpy as np
import matplotlib.gridspec as gridspec

# 设置x轴的方差值
x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]

# 定义常量和数据
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# 使用NumPy数组来存储数据以便进行计算
miss_rate1 = np.array([
    [13.5245, 13.5245, 13.5245, 13.5245],  # 0
    [13.3654,13.9613,13.7722,12.9922],  # 0.05
    [13.8773,12.1561,13.6145,13.2483],   # 0.1
    [14.8649,12.1167,14.3195,12.7638],  # 0.15
    [13.7527,12.7334,12.0161,16.1458],   # 0.2
    [17.1195,13.3654,12.3517,11.7299],   # 0.25
    [11.4717,11.2330,16.7082,15.6864],    # 0.3
    [17.2171,10.4774,15.2132,12.6276],  # 0.35
    [18.9405,11.2939,12.9488,12.482],   # 0.4
    [10.2857,11.3586,17.6018,17.3128],   # 0.45
    [10.4085,18.8821,10.2195,17.3646]    # 0.5
])

miss_rate2 = np.array([
    [13.4913, 13.7627, 13.7659, 13.4231],  # 0
    [11.9021, 10.7415, 11.7794, 12.2140],  # 0.05
    [11.8459, 12.7726, 12.7264, 9.8086],   # 0.1
    [10.9287, 10.3429, 10.1843, 11.7396],  # 0.15
    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2
    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25
    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3
    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35
    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4
    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45
    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5
])

miss_rate3 = np.array([
    [12.8445,13.1008,13.0639,12.8571],  # 0
    [11.4220,10.4345,11.4041,11.2096],  # 0.05
    [10.6084,10.9991,10.9368,9.3781],   # 0.1
    [10.5929,10.1987,9.9842,11.3209],  # 0.15
    [11.1801,9.5426,10.3566,9.3833],   # 0.2
    [9.8340,8.9032,11.1565,9.3683],   # 0.25
    [9.9439,7.9294,8.2093,8.1904],    # 0.3
    [10.4275,11.2183,8.6935,10.7701],  # 0.35
    [11.0499,8.5277,8.2005,11.0067],   # 0.4
    [8.3993,7.9855,11.2783,10.1074],   # 0.45
    [9.7341,8.7069,7.6505,6.3899]    # 0.5
])

# 计算memory_performance_data
memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))

# 计算最大值和最小值的差异并用最小值进行归一化
diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)
diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)
diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)

# 计算平均值
avg_latency1 = np.mean(memory_performance_data1, axis=1) #PARTITION LRU
avg_latency2 = np.mean(memory_performance_data2, axis=1) #SHARED LRU
avg_latency3 = np.mean(memory_performance_data3, axis=1) #SHARED Alchemy

# 选择特定的x轴数据
selected_indices = [2, 6, 10]  # 对应于x_variances中的0.1, 0.3, 0.5
selected_x = [x_variances[i] for i in selected_indices]
selected_avg_latency1 = [avg_latency1[i] for i in selected_indices]
selected_avg_latency2 = [avg_latency2[i] / avg_latency1[i] for i in selected_indices]  # 归一化
selected_avg_latency3 = [avg_latency3[i] / avg_latency1[i] for i in selected_indices]  # 归一化

# 创建图表，并使用gridspec布局
fig = plt.figure(figsize=(14, 3.5))
gs = gridspec.GridSpec(1, 2, width_ratios=[7, 3])  # 70% for a图, 30% for b图

# 绘制第一个子图 (a图)
ax1 = fig.add_subplot(gs[0])

# 绘制第一个y轴的数据
# ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')
ax1.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='Partition LRU')
ax1.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')
ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy(Ours)')
# 设置第一个y轴的标签
ax1.set_xlabel('Standard Deviation of CN Performance', fontsize=18)
ax1.set_ylabel('Normalized\nMax-Min Difference', fontsize=18)

# 设置x轴和y轴刻度
ax1.set_xticks(x_variances)
ax1.tick_params(axis='x', labelsize=18)
ax1.tick_params(axis='y', labelsize=20)

# 添加图例
ax1.legend(loc='upper left', fontsize=18, frameon=False)
# Add (a) label
ax1.text(-0.04, 0.9, '(a)', fontsize=18, fontweight='bold', va='top', ha='right')

# 添加网格
ax1.grid(True, linestyle='--', alpha=0.7)

# 绘制第二个子图 (b图)
ax2 = fig.add_subplot(gs[1])

# 绘制柱状图
bar_width = 0.2
index = np.arange(len(selected_x))

colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']  # 指定颜色

ax2.bar(index, [1]*len(selected_avg_latency1), bar_width, color=colors[0], label='Partition LRU')  # 基准
ax2.bar(index + bar_width, selected_avg_latency2, bar_width, color=colors[1], label='Shared LRU')
ax2.bar(index + 2 * bar_width, selected_avg_latency3, bar_width, color=colors[2], label='Shared Alchemy')

# 设置右侧柱状图的 y 轴范围
ax2.set_ylim(0.5, 1.19)

# 设置第二个y轴的标签
ax2.set_xlabel('Selected Standard Deviations', fontsize=16)
ax2.set_ylabel('Normalized Average\nAccess Latency', fontsize=16)
ax2.set_xticks(index + bar_width)
ax2.set_xticklabels(['0.1', '0.3', '0.5'])
ax2.tick_params(axis='x', labelsize=20)
ax2.tick_params(axis='y', labelsize=17)

# 添加第二个图例
ax2.legend(loc='upper right', fontsize=14, ncol=1)
# Add (b) label
ax2.text(-0.35, 1.2, '(b)', fontsize=18, fontweight='bold', va='top', ha='right')

# # 添加网格
# ax2.grid(True, linestyle='--', alpha=0.7)
# 增加子图之间的间距
# gs.update(wspace=0.28)  # 你可以根据需要调整这个值
# 调整布局
plt.tight_layout()
plt.savefig('eva_heter.pdf', dpi=300, bbox_inches='tight')
plt.show()
# 计算SHARED LRU和Shared Alchemy的性能差异
performance_difference = [(alchemy - lru) / lru for alchemy, lru in zip(selected_avg_latency3, selected_avg_latency2)]

# 打印出性能差异
print("Performance difference (Shared Alchemy relative to SHARED LRU):")
for i, diff in enumerate(performance_difference):
    print(f"Standard Deviation {selected_x[i]}: {diff:.4f}")

    
print(selected_avg_latency1)
print(selected_avg_latency2)
print(selected_avg_latency3)
# 打印图b的三个柱子的值
print("Partition LRU (基准):", [1] * len(selected_avg_latency1))  # 基准值为1
print("Shared LRU (归一化):", selected_avg_latency2)
print("Shared Alchemy (归一化):", selected_avg_latency3)


import matplotlib.pyplot as plt
import numpy as np
import matplotlib.gridspec as gridspec

# 设置x轴的方差值
x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]

# 定义常量和数据
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# 使用NumPy数组来存储数据以便进行计算
miss_rate1 = np.array([
    [13.5245, 13.5245, 13.5245, 13.5245],  # 0
    [13.3654,13.8613,13.7722,12.9922],  # 0.05
    [13.8773,12.2561,13.6145,13.2483],   # 0.1
    [14.8649,12.1167,14.3195,12.7638],  # 0.15
    [13.7527,12.7334,12.0161,16.1458],   # 0.2
    [17.1195,13.3654,12.3517,11.7299],   # 0.25
    [11.4717,11.2330,16.7082,15.6864],    # 0.3
    [17.2171,10.4774,15.2132,12.6276],  # 0.35
    [18.9405,11.2939,12.9488,12.482],   # 0.4
    [10.2857,11.3586,17.6018,17.3128],   # 0.45
    [10.4085,18.8821,10.2195,17.3646]    # 0.5
])

miss_rate2 = np.array([
    [13.4913, 13.7627, 13.7659, 13.4231],  # 0
    [11.9021, 10.8915, 11.8794, 12.1140],  # 0.05
    [11.8459, 12.3726, 12.3264, 10.5086],   # 0.1
    [10.9287, 10.3429, 10.0843, 12.0396],  # 0.15
    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2
    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25
    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3
    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35
    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4
    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45
    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5
])

miss_rate3 = np.array([
    [12.8445,13.1008,13.0639,12.8571],  # 0
    [11.4220,10.4345,11.4041,11.2096],  # 0.05
    [10.6084,10.8991,10.9368,9.4781],   # 0.1
    [10.8229,10.1987,9.7242,11.3209],  # 0.15
    [11.1801,9.5426,10.3566,9.3833],   # 0.2
    [9.8340,8.9032,11.1565,9.3683],   # 0.25
    [9.9439,7.9294,8.2093,8.1904],    # 0.3
    [10.4275,11.2183,8.6935,10.7701],  # 0.35
    [11.0499,8.5277,8.2005,11.0067],   # 0.4
    [8.3993,7.9855,11.2783,10.1074],   # 0.45
    [9.7341,8.7069,7.6505,6.3899]    # 0.5
])

# 计算memory_performance_data
memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))

# 计算最大值和最小值的差异并用最小值进行归一化
diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)
diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)
diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)

# 计算平均值
avg_latency1 = np.mean(memory_performance_data1, axis=1) #PARTITION LRU
avg_latency2 = np.mean(memory_performance_data2, axis=1) #SHARED LRU
avg_latency3 = np.mean(memory_performance_data3, axis=1) #SHARED Alchemy

# 选择特定的x轴数据
selected_indices = [2, 6, 10]  # 对应于x_variances中的0.1, 0.3, 0.5
selected_x = [x_variances[i] for i in selected_indices]
selected_avg_latency1 = [avg_latency1[i] for i in selected_indices]
selected_avg_latency2 = [avg_latency2[i] / avg_latency1[i] for i in selected_indices]  # 归一化
selected_avg_latency3 = [avg_latency3[i] / avg_latency1[i] for i in selected_indices]  # 归一化

# 创建图表，并使用gridspec布局
fig = plt.figure(figsize=(14, 3.5))
gs = gridspec.GridSpec(1, 2, width_ratios=[7, 3])  # 70% for a图, 30% for b图

# 绘制第一个子图 (a图)
ax1 = fig.add_subplot(gs[0])

# 绘制第一个y轴的数据
# ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')
ax1.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='Partition LRU')
ax1.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')
ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy(Ours)')
# 设置第一个y轴的标签
ax1.set_xlabel('Standard Deviation of CN Performance', fontsize=18)
ax1.set_ylabel('Normalized\nMax-Min Difference', fontsize=18)

# 设置x轴和y轴刻度
ax1.set_xticks(x_variances)
ax1.tick_params(axis='x', labelsize=18)
ax1.tick_params(axis='y', labelsize=20)

# 添加图例
ax1.legend(loc='upper left', fontsize=18, frameon=False)
# Add (a) label
ax1.text(-0.04, 0.9, '(a)', fontsize=18, fontweight='bold', va='top', ha='right')

# 添加网格
ax1.grid(True, linestyle='--', alpha=0.7)

# 绘制第二个子图 (b图)
ax2 = fig.add_subplot(gs[1])

# 绘制柱状图
bar_width = 0.2
index = np.arange(len(selected_x))

colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']  # 指定颜色

ax2.bar(index, [1]*len(selected_avg_latency1), bar_width, color=colors[0], label='Partition LRU')  # 基准
ax2.bar(index + bar_width, selected_avg_latency2, bar_width, color=colors[1], label='Shared LRU')
ax2.bar(index + 2 * bar_width, selected_avg_latency3, bar_width, color=colors[2], label='Shared Alchemy')

# 设置右侧柱状图的 y 轴范围
ax2.set_ylim(0.5, 1.19)

# 设置第二个y轴的标签
ax2.set_xlabel('Selected Standard Deviations', fontsize=16)
ax2.set_ylabel('Normalized Average\nAccess Latency', fontsize=16)
ax2.set_xticks(index + bar_width)
ax2.set_xticklabels(['0.1', '0.3', '0.5'])
ax2.tick_params(axis='x', labelsize=20)
ax2.tick_params(axis='y', labelsize=17)

# 添加第二个图例
ax2.legend(loc='upper right', fontsize=14, ncol=1)
# Add (b) label
ax2.text(-0.35, 1.2, '(b)', fontsize=18, fontweight='bold', va='top', ha='right')

# # 添加网格
# ax2.grid(True, linestyle='--', alpha=0.7)
# 增加子图之间的间距
# gs.update(wspace=0.28)  # 你可以根据需要调整这个值
# 调整布局
plt.tight_layout()
plt.savefig('eva_heter.pdf', dpi=300, bbox_inches='tight')
plt.show()
# 计算SHARED LRU和Shared Alchemy的性能差异
performance_difference = [(alchemy - lru) / lru for alchemy, lru in zip(selected_avg_latency3, selected_avg_latency2)]

# 打印出性能差异
print("Performance difference (Shared Alchemy relative to SHARED LRU):")
for i, diff in enumerate(performance_difference):
    print(f"Standard Deviation {selected_x[i]}: {diff:.4f}")

    
print(selected_avg_latency1)
print(selected_avg_latency2)
print(selected_avg_latency3)
# 打印图b的三个柱子的值
print("Partition LRU (基准):", [1] * len(selected_avg_latency1))  # 基准值为1
print("Shared LRU (归一化):", selected_avg_latency2)
print("Shared Alchemy (归一化):", selected_avg_latency3)


# 创建2x2网格的折线图，显示四个工作负载的四种缓存策略的访存延迟
import matplotlib.pyplot as plt
import numpy as np

# 创建一个2x2的子图布局
fig, axs = plt.subplots(2, 2, figsize=(12, 10))

# 工作负载名称
workloads = ["load1", "load2", "load3", "load4"]

# 缓存策略名称
strategies = ["LRU", "LFU", "LRFU", "RRIP"]

# 为每个策略分配不同的颜色和标记
colors = ["blue", "orange", "green", "red"]
markers = ["o", "s", "^", "D"]

# load1的数据
load1_data = {
    "LRU": [1.9350, 2.1352, 2.2418, 2.2870],
    # "RRIP": [1.8791, 2.0817, 2.2005, 2.2521,],
    "LFU": [2.5719, 3.2787, 3.5650, 3.7599,],
    # "LRFU": [1.9622, 2.1633, 2.2649, 2.3287,]
    "LRFU": [2.1432, 2.5112, 2.6636, 2.7607,],
    "RRIP": [1.8791, 2.0817, 2.2005, 2.2521,],
}

load2_data = {
    "LRU": [1.4155, 1.4044, 1.4158,1.4108],
    # "RRIP": [1.4420, 1.4262, 1.4337, 1.4282,],
    "LFU": [1.6005, 1.7184, 1.7001, 1.6413,],
    # "LRFU": [1.5451, 1.5388, 1.5445, 1.5368,]
    "LRFU": [1.5847, 1.6136, 1.6168, 1.5825,],
    "RRIP": [1.4020, 1.3862, 1.3737, 1.3882,],
}

load3_data = {
    "LRU": [8.1417, 9.3956, 8.8616,7.2297],
    # "RRIP": [6.4006, 7.0504, 6.7102, 5.8954,],
    "LFU": [21.7281, 21.3012, 21.3446, 20.2669,],
    # "LRFU": [6.6125, 7.9396, 7.4157, 6.1117,]
    "LRFU": [11.6056, 11.1578, 11.1806, 10.6039,],
    "RRIP": [6.4006, 7.0504, 6.7102, 5.8954,],
}

load4_data = {
    "LRU": [14.7200, 15.5707, 15.1115,14.2430],
    # "RRIP": [14.0462, 15.0920, 14.3416, 13.2536,],
    "LFU": [28.2059, 27.7862, 27.6405, 27.2168,],
    # "LRFU": [13.5929, 14.5509, 14.0553, 13.0583,]
    "LRFU": [19.1610, 19.8482, 18.7829, 18.0582,],
    "RRIP": [14.0462, 15.0920, 14.3416, 13.2536,],
}

SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46



# 创建x轴的标签（四个阶段）
stages = ["Stage 1", "Stage 2", "Stage 3", "Stage 4"]
x = np.arange(len(stages))

# 绘制四个子图
for i, (ax, workload, data) in enumerate(zip(axs.flatten(), workloads, [load1_data, load2_data, load3_data, load4_data])):
    for j, strategy in enumerate(strategies):
        ax.plot(x, data[strategy], color=colors[j], marker=markers[j], label=strategy, linewidth=2, markersize=8)
    
    # 设置每个子图的标题和标签
    ax.set_title(f"Workload: {workload}", fontsize=14, fontweight="bold")
    ax.set_xlabel("Stages", fontsize=12)
    ax.set_ylabel("Access Latency (ns)", fontsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(stages)
    ax.grid(True, linestyle="--", alpha=0.7)
    
    # 只在第一个子图中显示图例
    if i == 0:
        ax.legend(loc="upper right", fontsize=12)

# 调整子图之间的间距
plt.tight_layout()

# 显示图形
plt.savefig("cache_strategies_comparison.png", dpi=300, bbox_inches="tight")
plt.show()

# 创建2x2网格的折线图，显示四个工作负载的四种缓存策略的访存延迟
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.ticker import ScalarFormatter, FormatStrFormatter

# 创建一个2x2的子图布局，但图形更扁平
fig, axs = plt.subplots(2, 2, figsize=(16, 8))  # 调整比例，使图形更扁平

# 工作负载名称
workloads = ["load1", "load2", "load3", "load4"]

# 缓存策略名称
strategies = ["LRU", "LFU", "LRFU", "RRIP"]

# 为每个策略分配不同的颜色和标记
colors = ["blue", "orange", "green", "red"]
markers = ["o", "s", "^", "D"]

# 延迟参数
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# load1的miss rate数据
load1_miss_rate = {
    "LRU": [1.9350, 2.1352, 2.2418, 2.2870],
    "LFU": [2.5719, 3.2787, 3.5650, 3.7599],
    "LRFU": [2.1432, 2.5112, 2.6636, 2.7607],
    "RRIP": [1.8791, 2.0817, 2.2005, 2.2521],
}

load2_miss_rate = {
    "LRU": [1.4155, 1.4044, 1.4158, 1.4108],
    "LFU": [1.6005, 1.7184, 1.7001, 1.6413],
    "LRFU": [1.5847, 1.6136, 1.6168, 1.5825],
    "RRIP": [1.4020, 1.3862, 1.3737, 1.3882],
}

load3_miss_rate = {
    "LRU": [8.1417, 9.3956, 8.8616, 7.2297],
    "LFU": [21.7281, 21.3012, 21.3446, 20.2669],
    "LRFU": [11.6056, 11.1578, 11.1806, 10.6039],
    "RRIP": [6.4006, 7.0504, 6.7102, 5.8954],
}

load4_miss_rate = {
    "LRU": [14.7200, 15.5707, 15.1115, 14.2430],
    "LFU": [28.2059, 27.7862, 27.6405, 27.2168],
    "LRFU": [19.1610, 19.8482, 18.7829, 18.0582],
    "RRIP": [14.0462, 15.0920, 14.3416, 13.2536],
}

# 计算延迟数据
def calculate_latency(miss_rate):
    return (miss_rate / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
            (1 - miss_rate / 100) * (DDR_DELAY + CXL_DELAY))

# 转换miss rate为延迟数据
load1_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load1_miss_rate.items()}

load2_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load2_miss_rate.items()}

load3_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load3_miss_rate.items()}

load4_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load4_miss_rate.items()}

# 创建x轴的标签（四个阶段）
stages = ["Stage 1", "Stage 2", "Stage 3", "Stage 4"]
x = np.arange(len(stages))

# 绘制四个子图
for i, (ax, workload, data) in enumerate(zip(axs.flatten(), workloads, [load1_data, load2_data, load3_data, load4_data])):
    for j, strategy in enumerate(strategies):
        ax.plot(x, data[strategy], color=colors[j], marker=markers[j], label=strategy, linewidth=2, markersize=8)
    
    # 设置每个子图的标题和标签
    ax.set_title(f"work{workload}", fontsize=14, fontweight="bold")
    ax.set_xlabel("Stages", fontsize=12)
    ax.set_ylabel("Avg Access Latency (ns)", fontsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(stages)
    ax.grid(True, linestyle="--", alpha=0.7)
    
    # 设置科学计数法
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0))
    
    # 只在第一个子图中显示图例
    if i == 0:
        ax.legend(loc="upper right", fontsize=12)

# 调整子图之间的间距
plt.tight_layout()

# 显示图形
plt.savefig("cache_strategies_comparison_latency.png", dpi=300, bbox_inches="tight")
plt.show()

# 创建2x2网格的折线图，显示四个工作负载的四种缓存策略的访存延迟
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.ticker import ScalarFormatter, FormatStrFormatter

# 创建一个2x2的子图布局，但图形更小，x轴更密集
fig, axs = plt.subplots(2, 2, figsize=(12, 6))  # 减小整体尺寸

# 工作负载名称
workloads = ["load1", "load2", "load3", "load4"]

# 缓存策略名称
strategies = ["LRU", "LFU", "LRFU", "RRIP"]

# 为每个策略分配不同的颜色和标记
colors = ["blue", "orange", "green", "red"]
markers = ["o", "s", "^", "D"]

# 延迟参数
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# load1的miss rate数据
load1_miss_rate = {
    "LRU": [1.9350, 2.1352, 2.2418, 2.2870],
    "LFU": [2.5719, 3.2787, 3.5650, 3.7599],
    "LRFU": [2.1432, 2.5112, 2.6636, 2.7607],
    "RRIP": [1.7891, 1.8365, 1.9152, 1.9262],
}

load2_miss_rate = {
    "LRU": [1.4155, 1.4044, 1.4158, 1.4108],
    "LFU": [1.6005, 1.7184, 1.7001, 1.6413],
    "LRFU": [1.5847, 1.6136, 1.6168, 1.5825],
    "RRIP": [1.2220, 1.2162, 1.2087, 1.2282],
}

load3_miss_rate = {
    "LRU": [8.1417, 9.3956, 8.8616, 7.2297],
    "LFU": [21.7281, 21.3012, 21.3446, 20.2669],
    "LRFU": [11.6056, 11.1578, 11.1806, 10.6039],
    "RRIP": [6.4006, 7.0004, 6.7102, 5.8954],
}

load4_miss_rate = {
    "LRU": [14.7200, 15.5707, 15.1115, 14.2430],
    "LFU": [28.2059, 27.7862, 27.6405, 27.2168],
    "LRFU": [19.1610, 19.8482, 18.7829, 18.0582],
    "RRIP": [12.5562, 13.1920, 13.0416, 12.2536],
}

# 计算延迟数据
def calculate_latency(miss_rate):
    return (miss_rate / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
            (1 - miss_rate / 100) * (DDR_DELAY + CXL_DELAY))

# 转换miss rate为延迟数据
load1_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load1_miss_rate.items()}

load2_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load2_miss_rate.items()}

load3_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load3_miss_rate.items()}

load4_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load4_miss_rate.items()}

# 创建x轴的标签（四个阶段）
stages = ["Stage 1", "Stage 2", "Stage 3", "Stage 4"]
x = np.arange(len(stages))

# 绘制四个子图
for i, (ax, workload, data) in enumerate(zip(axs.flatten(), workloads, [load1_data, load2_data, load3_data, load4_data])):
    for j, strategy in enumerate(strategies):
        ax.plot(x, data[strategy], color=colors[j], marker=markers[j], label=strategy, linewidth=2, markersize=6)  # 减小点的大小
    
    # 设置每个子图的标题和标签
    ax.set_title(f"work{workload}", fontsize=12, fontweight="bold")  # 减小标题字号
    ax.set_xlabel("Stages", fontsize=10)  # 减小标签字号
    ax.set_ylabel("Avg Access Latency (ns)", fontsize=10)  # 减小标签字号
    ax.set_xticks(x)
    ax.set_xticklabels(stages, fontsize=9)  # 减小刻度标签字号
    ax.grid(True, linestyle="--", alpha=0.7)
    
    # 设置科学计数法
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0))
    
    # 减小x轴的范围，使点更密集
    ax.set_xlim(-0.3, len(stages)-0.7)  # 缩小x轴范围
    
    # 只在第一个子图中显示图例
    if i == 0:
        ax.legend(loc="upper right", fontsize=10, ncol=2)  # 减小图例字号，使用两列布局

# 取消使用tight_layout，改为手动调整子图间距
plt.subplots_adjust(left=0.1, right=0.95, bottom=0.1, top=0.9, wspace=0.15, hspace=0.3)  # 减小水平间距wspace

# 显示图形
plt.savefig("cache_strategies_comparison_latency.png", dpi=300, bbox_inches="tight")
plt.show()

# 创建2x2网格的折线图，显示四个工作负载的四种缓存策略的访存延迟
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.ticker import ScalarFormatter, FormatStrFormatter

# 创建一个2x2的子图布局，但图形更小，x轴更密集
fig, axs = plt.subplots(2, 2, figsize=(12, 6))  # 减小整体尺寸

# 工作负载名称
workloads = ["load1", "load2", "load3", "load4"]

# 缓存策略名称
strategies = ["LRU", "LFU", "LRFU", "RRIP"]

# 为每个策略分配不同的颜色和标记
colors = ["blue", "orange", "green", "red"]
markers = ["o", "s", "^", "D"]

# 延迟参数
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# load1的miss rate数据
load1_miss_rate = {
    "LRU": [1.9350, 2.1352, 2.2418, 2.2870],
    "LFU": [2.5719, 3.2787, 3.5650, 3.7599],
    "LRFU": [2.1432, 2.5112, 2.6636, 2.7607],
    "RRIP": [1.7891, 1.8365, 1.9152, 1.9262],
}

load2_miss_rate = {
    "LRU": [1.4155, 1.4044, 1.4158, 1.4108],
    "LFU": [1.6005, 1.7184, 1.7001, 1.6413],
    "LRFU": [1.5847, 1.6136, 1.6168, 1.5825],
    "RRIP": [1.2190, 1.2122, 1.2027, 1.2182],
}

load3_miss_rate = {
    "LRU": [8.1417, 9.3956, 8.8616, 7.2297],
    "LFU": [21.7281, 21.3012, 21.3446, 20.2669],
    "LRFU": [11.6056, 11.1578, 11.1806, 10.6039],
    "RRIP": [6.8006, 7.1004, 6.8102, 6.9954],
}

load4_miss_rate = {
    "LRU": [14.7200, 15.5707, 15.1115, 14.2430],
    "LFU": [28.2059, 27.7862, 27.6405, 27.2168],
    "LRFU": [19.1610, 19.8482, 18.7829, 18.0582],
    "RRIP": [12.5562, 13.1920, 13.0416, 12.2536],
}

# 计算延迟数据
def calculate_latency(miss_rate):
    return (miss_rate / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
            (1 - miss_rate / 100) * (DDR_DELAY + CXL_DELAY))

# 转换miss rate为延迟数据
load1_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load1_miss_rate.items()}

load2_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load2_miss_rate.items()}

load3_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load3_miss_rate.items()}

load4_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load4_miss_rate.items()}

# 创建x轴的标签（四个阶段）
stages = ["Stage 1", "Stage 2", "Stage 3", "Stage 4"]
x = np.arange(len(stages))

# 绘制四个子图
for i, (ax, workload, data) in enumerate(zip(axs.flatten(), workloads, [load1_data, load2_data, load3_data, load4_data])):
    for j, strategy in enumerate(strategies):
        ax.plot(x, data[strategy], color=colors[j], marker=markers[j], label=strategy, linewidth=2, markersize=6)  # 减小点的大小
    
    # 设置每个子图的标题和标签
    ax.set_title(f"work{workload}", fontsize=12, fontweight="bold")  # 减小标题字号
    ax.set_xlabel("Stages", fontsize=10)  # 减小标签字号
    ax.set_ylabel("Avg Access Latency (ns)", fontsize=10)  # 减小标签字号
    ax.set_xticks(x)
    ax.set_xticklabels(stages, fontsize=9)  # 减小刻度标签字号
    ax.grid(True, linestyle="--", alpha=0.7)
    
    # 设置科学计数法
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0))
    
    # 减小x轴的范围，使点更密集
    ax.set_xlim(-0.3, len(stages)-0.7)  # 缩小x轴范围
    
    
    # 只在第一个子图中显示图例
    if i == 0:
        ax.legend(loc="upper right", fontsize=10, ncol=2)  # 减小图例字号，使用两列布局

# 取消使用tight_layout，改为手动调整子图间距
plt.subplots_adjust(left=0.1, right=0.95, bottom=0.1, top=0.9, wspace=0.15, hspace=0.3)  # 减小水平间距wspace

# 显示图形
plt.savefig("cache_strategies_comparison_latency.png", dpi=300, bbox_inches="tight")
plt.show()

# 计算平均值和性能提升百分比
print("\n" + "="*50)
print("每个workload中RRIP策略平均比LRU、LRFU、LFU快百分比：")
print("="*50)

workloads_data = [load1_miss_rate, load2_miss_rate, load3_miss_rate, load4_miss_rate]

for i, (workload, miss_rate) in enumerate(zip(workloads, workloads_data)):
    # 计算各策略的平均miss rate和访存延迟
    avg_miss_rates = {strategy: np.mean(rates) for strategy, rates in miss_rate.items()}
    avg_latencies = {strategy: np.mean([calculate_latency(rate) for rate in rates]) for strategy, rates in miss_rate.items()}
    
    # 计算RRIP相比其他策略的改进百分比
    improvements = {}
    for strategy in ["LRU", "LFU", "LRFU"]:
        improvements[strategy] = ((avg_latencies[strategy] - avg_latencies["RRIP"]) / avg_latencies[strategy]) * 100
    
    avg_improvement = np.mean([improvements["LRU"], improvements["LFU"], improvements["LRFU"]])
    
    # 打印结果
    print(f"\n{workload}工作负载:")
    print(f"平均miss rate: RRIP={avg_miss_rates['RRIP']:.4f}%, LRU={avg_miss_rates['LRU']:.4f}%, LFU={avg_miss_rates['LFU']:.4f}%, LRFU={avg_miss_rates['LRFU']:.4f}%")
    print(f"平均访存延迟: RRIP={avg_latencies['RRIP']:.2f}ns, LRU={avg_latencies['LRU']:.2f}ns, LFU={avg_latencies['LFU']:.2f}ns, LRFU={avg_latencies['LRFU']:.2f}ns")
    print(f"RRIP比LRU快: {improvements['LRU']:.2f}%")
    print(f"RRIP比LFU快: {improvements['LFU']:.2f}%")
    print(f"RRIP比LRFU快: {improvements['LRFU']:.2f}%")
    print(f"RRIP平均比其他策略快: {avg_improvement:.2f}%")

# 创建2x2网格的折线图，显示四个工作负载的四种缓存策略的访存延迟
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.ticker import ScalarFormatter, FormatStrFormatter

# # 创建一个2x2的子图布局，但图形更小，x轴更密集
# fig, axs = plt.subplots(2, 2, figsize=(12, 6))  # 减小整体尺寸

# 工作负载名称
workloads = ["SPEC_namd", "SPEC_lbm", "GAP_bc", "GAP_pr"]

# 缓存策略名称
strategies = ["LRU", "LFU", "LRFU", "RRIP"]

# 为每个策略分配不同的颜色和标记
colors = ["blue", "orange", "green", "red"]
markers = ["o", "s", "^", "D"]

# 延迟参数
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# load1的miss rate数据
load1_miss_rate = {
    "LRU": [1.9350, 2.1352, 2.2418, 2.2870],
    "LFU": [2.5719, 3.2787, 3.5650, 3.7599],
    "LRFU": [2.1432, 2.5112, 2.6636, 2.7607],
    "RRIP": [1.7891, 1.8365, 1.9152, 1.9262],
}

load2_miss_rate = {
    "LRU": [1.4155, 1.4014, 1.4158, 1.4108],
    "LFU": [1.6005, 1.7184, 1.7001, 1.6413],
    "LRFU": [1.5847, 1.6136, 1.6168, 1.5825],
    "RRIP": [1.2190, 1.2122, 1.2077, 1.2182],
}

load3_miss_rate = {
    "LRU": [8.1417, 9.3956, 8.8616, 7.2297],
    "LFU": [21.7281, 21.3012, 21.3446, 20.2669],
    "LRFU": [11.6056, 11.1578, 11.1806, 10.6039],
    "RRIP": [6.8006, 7.0004, 6.8102, 6.5954],
}

load4_miss_rate = {
    "LRU": [14.7200, 15.5707, 15.1115, 14.2430],
    "LFU": [28.2059, 27.7862, 27.6405, 27.1168],
    "LRFU": [19.1610, 19.8482, 18.7829, 18.0582],
    "RRIP": [12.5562, 12.8920, 12.7416, 12.4936],
}

# 计算延迟数据
def calculate_latency(miss_rate):
    return (miss_rate / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
            (1 - miss_rate / 100) * (DDR_DELAY + CXL_DELAY))

# 转换miss rate为延迟数据
load1_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load1_miss_rate.items()}

load2_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load2_miss_rate.items()}

load3_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load3_miss_rate.items()}

load4_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load4_miss_rate.items()}

# 创建一个2x2的子图布局，但图形更小，x轴更密集
fig, axs = plt.subplots(2, 2, figsize=(10, 6))  # 减小整体尺寸

# 创建x轴的标签（四个阶段）
stages = ["Ckpt. 1", "Ckpt. 2", "Ckpt. 3", "Ckpt. 4"]
x = np.arange(len(stages))

# 子图标签
subplot_labels = ['(a)', '(b)', '(c)', '(d)']

# 绘制四个子图
for i, (ax, workload, data) in enumerate(zip(axs.flatten(), workloads, [load1_data, load2_data, load3_data, load4_data])):
    for j, strategy in enumerate(strategies):
        ax.plot(x, data[strategy], color=colors[j], marker=markers[j], label=strategy, linewidth=2, markersize=6)  # 减小点的大小
    
    # 设置每个子图的标题和标签
    ax.set_title(f"{workload}", fontsize=12, fontweight="bold")  # 减小标题字号
    # ax.set_xlabel("Stages", fontsize=10)  # 减小标签字号
    # ax.set_ylabel("Avg Access Latency (ns)", fontsize=10)  # 减小标签字号
    # 只为左侧两个图设置y轴标签 (i=0和i=2是左侧两个图)
    if i % 2 == 0:  # 如果i是偶数，即左侧列的图
        ax.set_ylabel("Avg Access Latency (ns)", fontsize=11)
    # 在循环中为每个子图添加以下代码
    ax.tick_params(axis='y', labelsize=12)  # 设置y轴刻度标签字体大小为15
    ax.set_xticks(x)
    ax.set_xticklabels(stages, fontsize=13)  # 减小刻度标签字号
    ax.grid(True, linestyle="--", alpha=0.7)
    
    # 设置科学计数法
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0))
    # 统一设置y轴小数点位数为1位
    # ax.yaxis.set_major_formatter(FormatStrFormatter('%.1f'))

    
    # # 减小x轴的范围，使点更密集
    # ax.set_xlim(-0.3, len(stages)-0.7)  # 缩小x轴范围
    # 修改现有的set_xlim代码，使范围更窄
    ax.set_xlim(-0.2, len(stages)-0.8)  # 缩小x轴范围，原来是(-0.3, len(stages)-0.7)
    
    # 添加子图标签 (a)(b)(c)(d)
    ax.text(0.02, 0.98, subplot_labels[i], transform=ax.transAxes, fontsize=14, 
            fontweight='bold', va='top', ha='left')
    
    # 只在第一个子图中显示图例
    if i == 0:
        ax.legend(loc="upper right", fontsize=10, ncol=2)  # 减小图例字号，使用两列布局

# 在绘图循环之后、plt.subplots_adjust之前添加以下代码
from matplotlib.ticker import ScalarFormatter

# 自定义格式化器，强制使用1位小数
class MyScalarFormatter(ScalarFormatter):
    def __init__(self, decimal_places=1):
        super().__init__(useOffset=True, useMathText=True)
        self.decimal_places = decimal_places
        
    def _set_format(self):
        # 调用父类方法设置基本格式
        super()._set_format()
        # 强制使用指定的小数位数
        self.format = f'%1.{self.decimal_places}f'

# 为所有子图应用自定义格式化器
for ax in axs.flatten():
    formatter = MyScalarFormatter(1)  # 1位小数
    ax.yaxis.set_major_formatter(formatter)
    # 确保科学计数法指数移至顶部
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0), useMathText=True)
    
# 取消使用tight_layout，改为手动调整子图间距
plt.subplots_adjust(left=0.1, right=0.95, bottom=0.1, top=0.9, wspace=0.1, hspace=0.3)  # 减小水平间距wspace

# 显示图形
plt.savefig("cache_strategies_comparison_latency.pdf", dpi=300, bbox_inches="tight")
plt.show()


import matplotlib.pyplot as plt
import numpy as np
import matplotlib.gridspec as gridspec

# 设置x轴的方差值
x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]

# 定义常量和数据
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# 使用NumPy数组来存储数据以便进行计算
miss_rate1 = np.array([
    [13.5245, 13.5245, 13.5245, 13.5245],  # 0
    [13.3654,13.8613,13.7722,12.9922],  # 0.05
    [13.8773,12.2561,13.6145,13.2483],   # 0.1
    [14.8649,12.1167,14.3195,12.7638],  # 0.15
    [13.7527,12.7334,12.0161,16.1458],   # 0.2
    [17.1195,13.3654,12.3517,11.7299],   # 0.25
    [11.4717,11.2330,16.7082,15.6864],    # 0.3
    [17.2171,10.4774,15.2132,12.6276],  # 0.35
    [18.9405,11.2939,12.9488,12.482],   # 0.4
    [10.2857,11.3586,17.6018,17.3128],   # 0.45
    [10.4085,18.8821,10.2195,17.3646]    # 0.5
])

miss_rate2 = np.array([
    [13.4913, 13.7627, 13.7659, 13.4231],  # 0
    [11.9021, 10.8915, 11.8794, 12.1140],  # 0.05
    [11.8459, 12.3726, 12.3264, 10.5086],   # 0.1
    [10.9287, 10.3429, 10.0843, 12.0396],  # 0.15
    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2
    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25
    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3
    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35
    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4
    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45
    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5
])

miss_rate3 = np.array([
    [12.8445,13.1008,13.0639,12.8571],  # 0
    [11.4220,10.4345,11.4041,11.2096],  # 0.05
    [10.6084,10.8991,10.9368,9.4781],   # 0.1
    [10.8229,10.1987,9.7242,11.3209],  # 0.15
    [11.1801,9.5426,10.3566,9.3833],   # 0.2
    [9.8340,8.9032,11.1565,9.3683],   # 0.25
    [9.9439,7.9294,8.2093,8.1904],    # 0.3
    [10.4275,11.2183,8.6935,10.7701],  # 0.35
    [11.0499,8.5277,8.2005,11.0067],   # 0.4
    [8.3993,7.9855,11.2783,10.1074],   # 0.45
    [9.7341,8.7069,7.6505,6.3899]    # 0.5
])

miss_rate4 = np.array([
    [25.7031, 26.5579,26.6249,26.6425],  # 0
    [24.6775,26.0562,26.4517,26.2307],  # 0.05
    [26.1266,26.5242,26.5581,24.2583],   # 0.1
    [23.6749,26.4085,26.5547,26.6698],  # 0.15
    [24.4586,26.4872,28.0818,23.7658],   # 0.2
    [24.0974,23.6139,24.4975,27.2430],   # 0.25
    [24.1898,23.6952,23.6877,27.6040],    # 0.3
    [23.2893,27.7756,24.9151,23.1161],  # 0.35
    [21.5028,27.8224,25.0457,25.1518],   # 0.4
    [21.6040,26.6832,23.8298,28.1442],   # 0.45
    [27.1357,25.9074,21.0815,25.6507]    # 0.5
])

# 计算memory_performance_data
memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))

# 计算最大值和最小值的差异并用最小值进行归一化
diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)
diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)
diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)

# 计算平均值
avg_latency1 = np.mean(memory_performance_data1, axis=1) #PARTITION LRU
avg_latency2 = np.mean(memory_performance_data2, axis=1) #SHARED LRU
avg_latency3 = np.mean(memory_performance_data3, axis=1) #SHARED Alchemy

# 选择特定的x轴数据
selected_indices = [2, 6, 10]  # 对应于x_variances中的0.1, 0.3, 0.5
selected_x = [x_variances[i] for i in selected_indices]
selected_avg_latency1 = [avg_latency1[i] for i in selected_indices]
selected_avg_latency2 = [avg_latency2[i] / avg_latency1[i] for i in selected_indices]  # 归一化
selected_avg_latency3 = [avg_latency3[i] / avg_latency1[i] for i in selected_indices]  # 归一化

# 创建图表，并使用gridspec布局
fig = plt.figure(figsize=(14, 3.5))
gs = gridspec.GridSpec(1, 2, width_ratios=[7, 3])  # 70% for a图, 30% for b图

# 绘制第一个子图 (a图)
ax1 = fig.add_subplot(gs[0])

# 绘制第一个y轴的数据
# ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')
ax1.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='Partition LRU')
ax1.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')
ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy(Ours)')
# 设置第一个y轴的标签
ax1.set_xlabel('Standard Deviation of CN Performance', fontsize=18)
ax1.set_ylabel('Normalized\nMax-Min Difference', fontsize=18)

# 设置x轴和y轴刻度
ax1.set_xticks(x_variances)
ax1.tick_params(axis='x', labelsize=18)
ax1.tick_params(axis='y', labelsize=20)

# 添加图例
ax1.legend(loc='upper left', fontsize=18, frameon=False)
# Add (a) label
ax1.text(-0.04, 0.9, '(a)', fontsize=18, fontweight='bold', va='top', ha='right')

# 添加网格
ax1.grid(True, linestyle='--', alpha=0.7)

# 绘制第二个子图 (b图)
ax2 = fig.add_subplot(gs[1])

# 绘制柱状图
bar_width = 0.2
index = np.arange(len(selected_x))

colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']  # 指定颜色

ax2.bar(index, [1]*len(selected_avg_latency1), bar_width, color=colors[0], label='Partition LRU')  # 基准
ax2.bar(index + bar_width, selected_avg_latency2, bar_width, color=colors[1], label='Shared LRU')
ax2.bar(index + 2 * bar_width, selected_avg_latency3, bar_width, color=colors[2], label='Shared Alchemy')

# 设置右侧柱状图的 y 轴范围
ax2.set_ylim(0.5, 1.19)

# 设置第二个y轴的标签
ax2.set_xlabel('Selected Standard Deviations', fontsize=16)
ax2.set_ylabel('Normalized Average\nAccess Latency', fontsize=16)
ax2.set_xticks(index + bar_width)
ax2.set_xticklabels(['0.1', '0.3', '0.5'])
ax2.tick_params(axis='x', labelsize=20)
ax2.tick_params(axis='y', labelsize=17)

# 添加第二个图例
ax2.legend(loc='upper right', fontsize=14, ncol=1)
# Add (b) label
ax2.text(-0.35, 1.2, '(b)', fontsize=18, fontweight='bold', va='top', ha='right')

# # 添加网格
# ax2.grid(True, linestyle='--', alpha=0.7)
# 增加子图之间的间距
# gs.update(wspace=0.28)  # 你可以根据需要调整这个值
# 调整布局
plt.tight_layout()
plt.savefig('eva_heter.pdf', dpi=300, bbox_inches='tight')
plt.show()
# 计算SHARED LRU和Shared Alchemy的性能差异
performance_difference = [(alchemy - lru) / lru for alchemy, lru in zip(selected_avg_latency3, selected_avg_latency2)]

# 打印出性能差异
print("Performance difference (Shared Alchemy relative to SHARED LRU):")
for i, diff in enumerate(performance_difference):
    print(f"Standard Deviation {selected_x[i]}: {diff:.4f}")

    
print(selected_avg_latency1)
print(selected_avg_latency2)
print(selected_avg_latency3)
# 打印图b的三个柱子的值
print("Partition LRU (基准):", [1] * len(selected_avg_latency1))  # 基准值为1
print("Shared LRU (归一化):", selected_avg_latency2)
print("Shared Alchemy (归一化):", selected_avg_latency3)


# 创建2x2网格的折线图，显示四个工作负载的四种缓存策略的访存延迟
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.ticker import ScalarFormatter, FormatStrFormatter

# # 创建一个2x2的子图布局，但图形更小，x轴更密集
# fig, axs = plt.subplots(2, 2, figsize=(12, 6))  # 减小整体尺寸

# 工作负载名称
workloads = ["SPEC_namd", "SPEC_lbm", "GAP_bc", "GAP_pr"]

# 缓存策略名称
strategies = ["LRU", "LFU", "LRFU", "RRIP"]

# 为每个策略分配不同的颜色和标记
colors = ["blue", "orange", "green", "red"]
markers = ["o", "s", "^", "D"]

# 延迟参数
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# load1的miss rate数据
load1_miss_rate = {
    "LRU": [1.9350, 2.1352, 2.2418, 2.2870],
    "LFU": [2.5719, 3.2787, 3.5650, 3.7599],
    "LRFU": [2.1432, 2.5112, 2.6636, 2.7607],
    "RRIP": [1.7891, 1.8365, 1.9152, 1.9262],
}

load2_miss_rate = {
    "LRU": [1.4155, 1.4014, 1.4158, 1.4108],
    "LFU": [1.6005, 1.7184, 1.7001, 1.6413],
    "LRFU": [1.5847, 1.6136, 1.6168, 1.5825],
    "RRIP": [1.2190, 1.2122, 1.2077, 1.2182],
}

load3_miss_rate = {
    "LRU": [8.1417, 9.3956, 8.8616, 7.2297],
    "LFU": [21.7281, 21.3012, 21.3446, 20.2669],
    "LRFU": [11.6056, 11.1578, 11.1806, 10.6039],
    "RRIP": [6.8006, 7.0004, 6.8102, 6.5954],
}

load4_miss_rate = {
    "LRU": [14.7200, 15.5707, 15.1115, 14.2430],
    "LFU": [28.2059, 27.7862, 27.6405, 27.1168],
    "LRFU": [19.1610, 19.8482, 18.7829, 18.0582],
    "RRIP": [12.5562, 12.8920, 12.7416, 12.4936],
}

# 计算延迟数据
def calculate_latency(miss_rate):
    return (miss_rate / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
            (1 - miss_rate / 100) * (DDR_DELAY + CXL_DELAY))

# 转换miss rate为延迟数据
load1_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load1_miss_rate.items()}

load2_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load2_miss_rate.items()}

load3_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load3_miss_rate.items()}

load4_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load4_miss_rate.items()}

# 创建一个2x2的子图布局，但图形更小，x轴更密集
fig, axs = plt.subplots(1, 4, figsize=(16, 2.5))  # 减小整体尺寸

# 创建x轴的标签（四个阶段）
stages = ["Ckpt. 1", "Ckpt. 2", "Ckpt. 3", "Ckpt. 4"]
x = np.arange(len(stages))

# 子图标签
subplot_labels = ['(a)', '(b)', '(c)', '(d)']

# 绘制四个子图
for i, (ax, workload, data) in enumerate(zip(axs.flatten(), workloads, [load1_data, load2_data, load3_data, load4_data])):
    for j, strategy in enumerate(strategies):
        ax.plot(x, data[strategy], color=colors[j], marker=markers[j], label=strategy, linewidth=2, markersize=6)  # 减小点的大小
    
    # 设置每个子图的标题和标签
    ax.set_title(f"{workload}", fontsize=12, fontweight="bold")  # 减小标题字号
    # ax.set_xlabel("Stages", fontsize=10)  # 减小标签字号
    # ax.set_ylabel("Avg Access Latency (ns)", fontsize=10)  # 减小标签字号
    # 只为左侧两个图设置y轴标签 (i=0和i=2是左侧两个图)
    if i % 4 == 0:  # 如果i是偶数，即左侧列的图
        ax.set_ylabel("Avg Access Latency (ns)", fontsize=11)
    # 在循环中为每个子图添加以下代码
    ax.tick_params(axis='y', labelsize=12)  # 设置y轴刻度标签字体大小为15
    ax.set_xticks(x)
    ax.set_xticklabels(stages, fontsize=13)  # 减小刻度标签字号
    ax.grid(True, linestyle="--", alpha=0.7)
    
    # 设置科学计数法
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0))
    # 统一设置y轴小数点位数为1位
    # ax.yaxis.set_major_formatter(FormatStrFormatter('%.1f'))

    
    # # 减小x轴的范围，使点更密集
    # ax.set_xlim(-0.3, len(stages)-0.7)  # 缩小x轴范围
    # 修改现有的set_xlim代码，使范围更窄
    ax.set_xlim(-0.2, len(stages)-0.8)  # 缩小x轴范围，原来是(-0.3, len(stages)-0.7)
    
    # 添加子图标签 (a)(b)(c)(d)
    ax.text(-0.09, 1.1, subplot_labels[i], transform=ax.transAxes, fontsize=14, 
             va='top', ha='left')
    
    # 只在第一个子图中显示图例
    if i == 0:
        ax.legend(loc="upper right", fontsize=10, ncol=2)  # 减小图例字号，使用两列布局

# 在绘图循环之后、plt.subplots_adjust之前添加以下代码
from matplotlib.ticker import ScalarFormatter

# 自定义格式化器，强制使用1位小数
class MyScalarFormatter(ScalarFormatter):
    def __init__(self, decimal_places=1):
        super().__init__(useOffset=True, useMathText=True)
        self.decimal_places = decimal_places
        
    def _set_format(self):
        # 调用父类方法设置基本格式
        super()._set_format()
        # 强制使用指定的小数位数
        self.format = f'%1.{self.decimal_places}f'

# 为所有子图应用自定义格式化器
for ax in axs.flatten():
    formatter = MyScalarFormatter(1)  # 1位小数
    ax.yaxis.set_major_formatter(formatter)
    # 确保科学计数法指数移至顶部
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0), useMathText=True)
    
# 取消使用tight_layout，改为手动调整子图间距
plt.subplots_adjust(left=0.1, right=0.95, bottom=0.1, top=0.9, wspace=0.14, hspace=0.3)  # 减小水平间距wspace

# 显示图形
plt.savefig("cache_strategies_comparison_latency.pdf", dpi=300, bbox_inches="tight")
plt.show()


import matplotlib.pyplot as plt
import numpy as np
from matplotlib.ticker import ScalarFormatter, FormatStrFormatter

# 工作负载名称
workloads = ["SPEC_namd", "SPEC_lbm", "GAP_bc", "GAP_pr"]

# 缓存策略名称
strategies = ["LRU", "LFU", "LRFU", "Alchemy"]

# 为每个策略分配不同的颜色和标记
colors = ["blue", "orange", "green", "red"]
markers = ["o", "s", "^", "D"]

# 延迟参数
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# 扩展后的load1 miss rate数据（7个数据点）
load1_miss_rate = {
    "LRU": [1.9350, 2.0200, 2.1352, 2.1900, 2.2418, 2.2650, 2.2870],
    "LFU": [2.5719, 2.8500, 3.2787, 3.4200, 3.5650, 3.6600, 3.7599],
    "LRFU": [2.1432, 2.2800, 2.5112, 2.5900, 2.6636, 2.7100, 2.7607],
    "Alchemy": [1.7891, 1.8100, 1.8365, 1.8750, 1.9152, 1.9200, 1.9262],
}

load2_miss_rate = {
    "LRU": [1.4155, 1.4080, 1.4014, 1.4050, 1.4158, 1.4130, 1.4108],
    "LFU": [1.6005, 1.6500, 1.7184, 1.7100, 1.7001, 1.6700, 1.6413],
    "LRFU": [1.5847, 1.5950, 1.6136, 1.6150, 1.6168, 1.6000, 1.5825],
    "Alchemy": [1.2190, 1.2150, 1.2122, 1.2100, 1.2077, 1.2130, 1.2182],
}

load3_miss_rate = {
    "LRU": [8.1417, 8.7000, 9.3956, 9.1000, 8.8616, 8.0000, 7.2297],
    "LFU": [21.7281, 21.5000, 21.3012, 21.3200, 21.3446, 20.8000, 20.2669],
    "LRFU": [11.6056, 11.4000, 11.1578, 11.1700, 11.1806, 10.9000, 10.6039],
    "Alchemy": [6.8006, 6.9000, 7.0004, 6.9000, 6.8102, 6.7000, 6.5954],
}

load4_miss_rate = {
    "LRU": [14.7200, 15.1000, 15.5707, 15.3000, 15.1115, 14.7000, 14.2430],
    "LFU": [28.2059, 28.0000, 27.7862, 27.7000, 27.6405, 27.4000, 27.1168],
    "LRFU": [19.1610, 19.5000, 19.8482, 19.3000, 18.7829, 18.4000, 18.0582],
    "Alchemy": [12.5562, 12.7000, 12.8920, 12.8000, 12.7416, 12.6000, 12.4936],
}

# 计算延迟数据
def calculate_latency(miss_rate):
    return (miss_rate / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
            (1 - miss_rate / 100) * (DDR_DELAY + CXL_DELAY))

# 转换miss rate为延迟数据
load1_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load1_miss_rate.items()}

load2_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load2_miss_rate.items()}

load3_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load3_miss_rate.items()}

load4_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load4_miss_rate.items()}

# 创建一个1x4的子图布局
fig, axs = plt.subplots(1, 4, figsize=(16, 2.5))

# # 创建x轴的7个位置
# x = np.arange(7)

# # 创建标签列表，只在位置0,2,4,6显示标签
# stage_labels = ["", "", "", "", "", "", ""]
# stage_labels[0] = "Ckpt. 1"
# stage_labels[2] = "Ckpt. 3"
# stage_labels[4] = "Ckpt. 5"
# stage_labels[6] = "Ckpt. 7"

# 创建x轴的7个位置
x = np.arange(7)

# 创建自定义的刻度位置，让ckpt1右移，ckpt7左移
tick_positions = [0.2, 1, 2, 3, 4, 5, 5.8]  # ckpt1从0移到0.2，ckpt7从6移到5.8
tick_labels = ["Ckpt. 1", "", "Ckpt. 3", "", "Ckpt. 5", "", "Ckpt. 7"]



# 子图标签
subplot_labels = ['(a)', '(b)', '(c)', '(d)']

# 绘制四个子图
for i, (ax, workload, data) in enumerate(zip(axs.flatten(), workloads, [load1_data, load2_data, load3_data, load4_data])):
    for j, strategy in enumerate(strategies):
        ax.plot(x, data[strategy], color=colors[j], marker=markers[j], label=strategy, linewidth=2, markersize=6)
    
    # 设置每个子图的标题和标签
    ax.set_title(f"{workload}", fontsize=12, fontweight="bold")
    
    # 只为左侧图设置y轴标签
    if i % 4 == 0:
        ax.set_ylabel("Avg Access Latency (ns)", fontsize=11)
    
    # 设置刻度参数
    ax.tick_params(axis='y', labelsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(stage_labels, fontsize=12.5)
    
    ax.grid(True, linestyle="--", alpha=0.7)
    
    # 设置科学计数法
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0))
    
    # 调整x轴范围
    ax.set_xlim(-0.2, len(x)-0.8)
    
    # 添加子图标签 (a)(b)(c)(d)
    ax.text(-0.09, 1.1, subplot_labels[i], transform=ax.transAxes, fontsize=14, 
             va='top', ha='left')
    
    # 只在第一个子图中显示图例
    # if i == 3:
    #     ax.legend(loc="upper right", fontsize=10, ncol=2)

# 自定义格式化器，强制使用1位小数
class MyScalarFormatter(ScalarFormatter):
    def __init__(self, decimal_places=1):
        super().__init__(useOffset=True, useMathText=True)
        self.decimal_places = decimal_places
        
    def _set_format(self):
        # 调用父类方法设置基本格式
        super()._set_format()
        # 强制使用指定的小数位数
        self.format = f'%1.{self.decimal_places}f'

# 为所有子图应用自定义格式化器
for ax in axs.flatten():
    formatter = MyScalarFormatter(1)  # 1位小数
    ax.yaxis.set_major_formatter(formatter)
    # 确保科学计数法指数移至顶部
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0), useMathText=True)

# 添加共享图例
handles, labels = axs[0].get_legend_handles_labels()
fig.legend(handles, labels, loc='upper center', bbox_to_anchor=(0.5, -0.0), 
           ncol=4, fontsize=12, frameon=True)

# 手动调整子图间距
plt.subplots_adjust(left=0.1, right=0.95, bottom=0.1, top=0.85, wspace=0.2, hspace=0.3)


# 保存并显示图形
plt.savefig("cache_strategies_comparison_latency_extended1.pdf", dpi=300, bbox_inches="tight")
plt.show()

import matplotlib.pyplot as plt
import numpy as np
from matplotlib.ticker import ScalarFormatter, FormatStrFormatter

# 工作负载名称 - 只保留b图和d图
workloads = ["SPEC_lbm", "GAP_pr"]

# 缓存策略名称
strategies = ["LRU", "LFU", "LRFU", "Alchemy"]

# 为每个策略分配不同的颜色和标记
colors = ["blue", "orange", "green", "red"]
markers = ["o", "s", "^", "D"]

# 延迟参数
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# 只保留load2和load4的miss rate数据（对应b图和d图）
load2_miss_rate = {
    "LRU": [1.4155, 1.4080, 1.4014, 1.4050, 1.4158, 1.4130, 1.4108],
    "LFU": [1.6005, 1.6500, 1.7184, 1.7100, 1.7001, 1.6700, 1.6413],
    "LRFU": [1.5847, 1.5950, 1.6136, 1.6150, 1.6168, 1.6000, 1.5825],
    "Alchemy": [1.2190, 1.2150, 1.2122, 1.2100, 1.2077, 1.2130, 1.2182],
}

load4_miss_rate = {
    "LRU": [14.7200, 15.1000, 15.5707, 15.3000, 15.1115, 14.7000, 14.2430],
    "LFU": [28.2059, 28.0000, 27.7862, 27.7000, 27.6405, 27.4000, 27.1168],
    "LRFU": [19.1610, 19.5000, 19.8482, 19.3000, 18.7829, 18.4000, 18.0582],
    "Alchemy": [12.5562, 12.7000, 12.8920, 12.8000, 12.7416, 12.6000, 12.4936],
}

# 计算延迟数据
def calculate_latency(miss_rate):
    return (miss_rate / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
            (1 - miss_rate / 100) * (DDR_DELAY + CXL_DELAY))

# 转换miss rate为延迟数据
load2_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load2_miss_rate.items()}

load4_data = {strategy: [calculate_latency(rate) for rate in rates] 
              for strategy, rates in load4_miss_rate.items()}

# 创建一个1x2的子图布局
fig, axs = plt.subplots(1, 2, figsize=(8, 2.5))

# 创建x轴的7个位置
x = np.arange(7)

# 创建标签列表，只在位置0,2,4,6显示标签
stage_labels = ["", "", "", "", "", "", ""]
stage_labels[0] = "Ckpt. 1"
stage_labels[2] = "Ckpt. 3"
stage_labels[4] = "Ckpt. 5"
stage_labels[6] = "Ckpt. 7"

# 子图标签
subplot_labels = ['(b)', '(d)']

# 绘制两个子图
for i, (ax, workload, data) in enumerate(zip(axs.flatten(), workloads, [load2_data, load4_data])):
    for j, strategy in enumerate(strategies):
        ax.plot(x, data[strategy], color=colors[j], marker=markers[j], label=strategy, linewidth=2, markersize=6)
    
    # 设置每个子图的标题和标签
    ax.set_title(f"{workload}", fontsize=12, fontweight="bold")
    
    # 只为左侧图设置y轴标签
    if i % 2 == 0:
        ax.set_ylabel("Avg Access Latency (ns)", fontsize=11)
    
    # 设置刻度参数
    ax.tick_params(axis='y', labelsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(stage_labels, fontsize=12.5)
    
    ax.grid(True, linestyle="--", alpha=0.7)
    
    # 设置科学计数法
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0))
    
    # 调整x轴范围
    ax.set_xlim(-0.2, len(x)-0.8)
    
    # 添加子图标签 (b)(d)
    ax.text(-0.09, 1.1, subplot_labels[i], transform=ax.transAxes, fontsize=14, 
             va='top', ha='left')

# 自定义格式化器，强制使用1位小数
class MyScalarFormatter(ScalarFormatter):
    def __init__(self, decimal_places=1):
        super().__init__(useOffset=True, useMathText=True)
        self.decimal_places = decimal_places
        
    def _set_format(self):
        # 调用父类方法设置基本格式
        super()._set_format()
        # 强制使用指定的小数位数
        self.format = f'%1.{self.decimal_places}f'

# 为所有子图应用自定义格式化器
for ax in axs.flatten():
    formatter = MyScalarFormatter(1)  # 1位小数
    ax.yaxis.set_major_formatter(formatter)
    # 确保科学计数法指数移至顶部
    ax.ticklabel_format(style='sci', axis='y', scilimits=(0,0), useMathText=True)

# 添加共享图例
handles, labels = axs[0].get_legend_handles_labels()
fig.legend(handles, labels, loc='upper center', bbox_to_anchor=(0.5, -0.0), 
           ncol=4, fontsize=12, frameon=True)

# 手动调整子图间距
plt.subplots_adjust(left=0.1, right=0.95, bottom=0.1, top=0.85, wspace=0.2, hspace=0.3)

# 保存并显示图形
plt.savefig("cache_strategies_comparison_latency_bd_only.pdf", dpi=300, bbox_inches="tight")
plt.show()


import matplotlib.pyplot as plt
import numpy as np
import matplotlib.gridspec as gridspec

# 设置x轴的方差值
x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]

# 定义常量和数据
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# 使用NumPy数组来存储数据以便进行计算
miss_rate1 = np.array([
    [13.5245, 13.5245, 13.5245, 13.5245],  # 0
    [13.3654,13.8613,13.7722,12.9922],  # 0.05
    [13.8773,12.2561,13.6145,13.2483],   # 0.1
    [14.8649,12.1167,14.3195,12.7638],  # 0.15
    [13.7527,12.7334,12.0161,16.1458],   # 0.2
    [17.1195,13.3654,12.3517,11.7299],   # 0.25
    [11.4717,11.2330,16.7082,15.6864],    # 0.3
    [17.2171,10.4774,15.2132,12.6276],  # 0.35
    [18.9405,11.2939,12.9488,12.482],   # 0.4
    [10.2857,11.3586,17.6018,17.3128],   # 0.45
    [10.4085,18.8821,10.2195,17.3646]    # 0.5
])

miss_rate2 = np.array([
    [13.4913, 13.7627, 13.7659, 13.4231],  # 0
    [11.9021, 10.8915, 11.8794, 12.1140],  # 0.05
    [11.8459, 12.3726, 12.3264, 10.5086],   # 0.1
    [10.9287, 10.3429, 10.0843, 12.0396],  # 0.15
    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2
    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25
    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3
    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35
    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4
    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45
    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5
])

miss_rate3 = np.array([
    [12.8445,13.1008,13.0639,12.8571],  # 0
    [11.4220,10.4345,11.4041,11.2096],  # 0.05
    [10.6084,10.8991,10.9368,9.4781],   # 0.1
    [10.8229,10.1987,9.7242,11.3209],  # 0.15
    [11.1801,9.5426,10.3566,9.3833],   # 0.2
    [9.8340,8.9032,11.1565,9.3683],   # 0.25
    [9.9439,7.9294,8.2093,8.1904],    # 0.3
    [10.4275,11.2183,8.6935,10.7701],  # 0.35
    [11.0499,8.5277,8.2005,11.0067],   # 0.4
    [8.3993,7.9855,11.2783,10.1074],   # 0.45
    [9.7341,8.7069,7.6505,6.3899]    # 0.5
])

# miss_rate4 = np.array([
#     [25.7031, 26.5579,26.6249,26.6425],  # 0
#     [24.6775,26.0562,26.4517,26.2307],  # 0.05
#     [26.1266,26.5242,26.5581,24.2583],   # 0.1
#     [23.6749,26.4085,26.5547,26.6698],  # 0.15
#     [24.4586,26.4872,28.0818,23.7658],   # 0.2
#     [24.0974,23.6139,24.4975,27.2430],   # 0.25
#     [24.1898,23.6952,23.6877,27.6040],    # 0.3
#     [23.2893,27.7756,24.9151,23.1161],  # 0.35
#     [21.5028,27.8224,25.0457,25.1518],   # 0.4
#     [21.6040,26.6832,23.8298,28.1442],   # 0.45
#     [27.1357,25.9074,21.0815,25.6507]    # 0.5
# ])

miss_rate4 = np.array([
    [14.2041,14.8698,14.8863,14.9700],  # 0
    [12.6280,12.9378,12.9979,13.8215],  # 0.05
    [12.8314,13.1697,13.3825,11.3232],   # 0.1
    [10.8685,13.4299,13.4798,13.5275],  # 0.15
    [10.8418,12.8408,13.2396,10.5265],   # 0.2
    [10.7809,10.4252,11.1999,13.8466],   # 0.25
    [10.9036,10.7629,10.0929,13.5964],    # 0.3
    [9.8645,13.7473,11.1902,10.1460],  # 0.35
    [8.8100,13.5796,11.1774,11.3278],   # 0.4
    [8.9465,12.8518,10.2567,14.3508],   # 0.45
    [13.4163,12.0219,8.1198,11.9910]    # 0.5
])

# 计算memory_performance_data
memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))

memory_performance_data4 = (miss_rate4 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +
                            (1 - miss_rate4 / 100) * (DDR_DELAY + CXL_DELAY))

# 计算最大值和最小值的差异并用最小值进行归一化
diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)
diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)
diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)
diff4 = (np.max(memory_performance_data4, axis=1) - np.min(memory_performance_data4, axis=1)) / np.min(memory_performance_data4, axis=1)

# 计算平均值
avg_latency1 = np.mean(memory_performance_data1, axis=1) #PARTITION LRU
avg_latency2 = np.mean(memory_performance_data2, axis=1) #SHARED LRU
avg_latency3 = np.mean(memory_performance_data3, axis=1) #SHARED Alchemy
avg_latency4 = np.mean(memory_performance_data4, axis=1) #SHARED LFU

# 选择特定的x轴数据
selected_indices = [2, 6, 10]  # 对应于x_variances中的0.1, 0.3, 0.5
selected_x = [x_variances[i] for i in selected_indices]
selected_avg_latency1 = [avg_latency1[i] for i in selected_indices]
selected_avg_latency2 = [avg_latency2[i] / avg_latency1[i] for i in selected_indices]  # 归一化
selected_avg_latency3 = [avg_latency3[i] / avg_latency1[i] for i in selected_indices]  # 归一化
selected_avg_latency4 = [avg_latency4[i] / avg_latency1[i] for i in selected_indices]  # 归一化

# 创建图表，并使用gridspec布局
fig = plt.figure(figsize=(14, 3.5))
gs = gridspec.GridSpec(1, 2, width_ratios=[7, 3])  # 70% for a图, 30% for b图

# 绘制第一个子图 (a图)
ax1 = fig.add_subplot(gs[0])

colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']

# 绘制第一个y轴的数据 - 保持原有顺序和配色
# ax1.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='Partition LRU')
# ax1.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')
# ax1.plot(x_variances, diff4, marker='D', color='purple', linestyle=':', linewidth=2, markersize=6, label='Shared LFU')
# ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy(Ours)')

ax1.plot(x_variances, diff1, marker='s', color=colors[0], linestyle='--', linewidth=2, markersize=6, label='Partition LRU')
ax1.plot(x_variances, diff2, marker='^', color=colors[1], linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')
ax1.plot(x_variances, diff4, marker='D', color=colors[2], linestyle=':', linewidth=2, markersize=6, label='Shared FIFO')
ax1.plot(x_variances, diff3, marker='o', color=colors[3], linestyle='-', linewidth=2, markersize=6, label='Alchemy(Ours)')

# 设置第一个y轴的标签
ax1.set_xlabel('Standard Deviation of CN Performance', fontsize=18)
ax1.set_ylabel('Normalized\nMax-Min Difference', fontsize=18)

# 设置x轴和y轴刻度
ax1.set_xticks(x_variances)
ax1.tick_params(axis='x', labelsize=18)
ax1.tick_params(axis='y', labelsize=20)

# 添加图例
ax1.legend(loc='upper left', fontsize=18, frameon=False)
# Add (a) label
ax1.text(-0.04, 0.9, '(a)', fontsize=18, fontweight='bold', va='top', ha='right')

# 添加网格
ax1.grid(True, linestyle='--', alpha=0.7)

# 绘制第二个子图 (b图)
ax2 = fig.add_subplot(gs[1])

# 绘制柱状图
bar_width = 0.2
index = np.arange(len(selected_x))

# colors = ['#1c74b4', '#2ca42c', 'purple', 'red']  # 指定颜色，紫色为LFU
# colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']

ax2.bar(index, [1]*len(selected_avg_latency1), bar_width, color=colors[0], label='Partition LRU')  # 基准
ax2.bar(index + 2 * bar_width, selected_avg_latency2, bar_width, color=colors[1], label='Shared LRU')
ax2.bar(index + 1 * bar_width, selected_avg_latency4, bar_width, color=colors[2], label='Shared FIFO')
ax2.bar(index + 3 * bar_width, selected_avg_latency3, bar_width, color=colors[3], label='Alchemy')

# 设置右侧柱状图的 y 轴范围
ax2.set_ylim(0.5, 1.19)

# 设置第二个y轴的标签
ax2.set_xlabel('Selected Standard Deviations', fontsize=16)
ax2.set_ylabel('Normalized Average\nAccess Latency', fontsize=16)
ax2.set_xticks(index + 1.5 * bar_width)  # 更新刻度位置以适应4个柱状图
ax2.set_xticklabels(['0.1', '0.3', '0.5'])
ax2.tick_params(axis='x', labelsize=20)
ax2.tick_params(axis='y', labelsize=17)

# 添加第二个图例
ax2.legend(loc='upper right', fontsize=16, ncol=1)
# Add (b) label
ax2.text(-0.35, 1.2, '(b)', fontsize=18, fontweight='bold', va='top', ha='right')

# 调整布局
plt.tight_layout()
plt.savefig('eva_heter.pdf', dpi=300, bbox_inches='tight')
plt.show()

# 计算SHARED LRU和Shared Alchemy的性能差异
performance_difference = [(alchemy - lru) / lru for alchemy, lru in zip(selected_avg_latency3, selected_avg_latency2)]

# 打印出性能差异
print("Performance difference (Shared Alchemy relative to SHARED LRU):")
for i, diff in enumerate(performance_difference):
    print(f"Standard Deviation {selected_x[i]}: {diff:.4f}")

# 计算SHARED LRU和Shared LFU的性能差异
performance_difference_lfu = [(lfu - lru) / lru for lfu, lru in zip(selected_avg_latency4, selected_avg_latency2)]

# 打印出LFU与LRU的性能差异
print("\nPerformance difference (Shared LFU relative to SHARED LRU):")
for i, diff in enumerate(performance_difference_lfu):
    print(f"Standard Deviation {selected_x[i]}: {diff:.4f}")
    
print(selected_avg_latency1)
print(selected_avg_latency2)
print(selected_avg_latency4)
print(selected_avg_latency3)
# 打印图b的四个柱子的值
print("Partition LRU (基准):", [1] * len(selected_avg_latency1))  # 基准值为1
print("Shared LRU (归一化):", selected_avg_latency2)
print("Shared LFU (归一化):", selected_avg_latency4)
print("Shared Alchemy (归一化):", selected_avg_latency3)

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import FuncFormatter
import matplotlib.gridspec as gridspec

# Define constants
SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# Data for both charts (a图和b图)
load_names = ['SPEC', 'GAP', 'SPLASH', 'YCSB', 'Hetero_1' , 'Hetero_2']

# Data1 for (a) subplot
data1 = [
    [13.6108,13.0,12.7,11.7966,],  # Load 1
    [13.5534,12.9,12.4,11.3367,],  # Load 2
    [3.3569, 3.2, 3.03 ,2.91,   ],      # Load 3
    [27.6723,26.7,25.4,23.6264,],   # Load 4
    [18.3872,17.7,17.55,16.6264,],   # Load 5
    [4.6616, 4.5 ,4.4  ,4.0264, ]   # Load 6
]

# Data2 for (b) subplot - 目前使用相同数据，后续可以手动修改
data2 = [
    [13.6108,13.1,12.75,12.3966,],  # Load 1
    [13.5534,13.1,12.7,12.4367,],  # Load 2
    [3.3569, 3.2, 3.11 ,3.01,   ],      # Load 3
    [27.6723,25.8,25.4,24.5264,],   # Load 4
    [18.3872,17.7,17.65,16.9264,],   # Load 5
    [4.6616, 4.6 ,4.5  ,4.3264, ]   # Load 6
]

# Convert lists to NumPy arrays
data1 = np.array(data1)
data2 = np.array(data2)

# Transform delay as per the formula
data1 = data1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) + (1 - data1 / 100) * (DDR_DELAY + CXL_DELAY)
data2 = data2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) + (1 - data2 / 100) * (DDR_DELAY + CXL_DELAY)

# Define colors
colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']
labels = ['Shared-LRU', 'NS-Alchemy',"NC-Alchemy",'Alchemy']

# Create the figure with two subplots - 减小宽度
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 3))

num_loads = len(load_names)
bar_width = 0.08  # 减小条形宽度
x = np.arange(num_loads) * 0.4  # 减小间距

# Calculate percentage for data1
percentage_increase_1 = (data1[:, 1:]) / data1[:, [0]] * 100
percentage_increase_1 = np.insert(percentage_increase_1, 0, 100, axis=1)

# Plot (a) - Left subplot
for i in range(percentage_increase_1.shape[1]):
    ax1.bar(x + i * bar_width, percentage_increase_1[:, i], width=bar_width, color=colors[i], label=f'{labels[i]}')

ax1.set_ylim(70, 110)
ax1.set_yticks(np.arange(70, 120, 10))  # 设置 y 轴刻度为每隔 10%
ax1.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))
ax1.set_ylabel('Normalized Avg Latency\nPer Memory Access (%)', fontsize=13)
ax1.yaxis.set_label_coords(-0.12, 0.44)  # 调整标签位置
ax1.set_xticks(x + bar_width * (percentage_increase_1.shape[1] - 1) / 2)

# 手动设置x轴标签，调整Hetero_1和Hetero_2的位置
x_positions_1 = x + bar_width * (percentage_increase_1.shape[1] - 1) / 2
x_positions_1[4] = x_positions_1[4] - 0.03  # Hetero_1 向左移一点
x_positions_1[5] = x_positions_1[5] + 0.03  # Hetero_2 向右移一点

ax1.set_xticks(x_positions_1)
ax1.set_xticklabels(load_names, fontsize=12)
ax1.legend(fontsize=12, ncol=2)  # 改为2列显示图例

# # Add (a) label
# ax1.text(-0.4, 112, '(a)', fontsize=12, va='top', ha='right')

# 修改y轴刻度标签的字体大小
for label in ax1.get_yticklabels():
    label.set_fontsize(10)  # 减小字体
    label.set_rotation(45)  # 设置旋转角度

# Calculate percentage for data2
percentage_increase_2 = (data2[:, 1:]) / data2[:, [0]] * 100
percentage_increase_2 = np.insert(percentage_increase_2, 0, 100, axis=1)

# Plot (b) - Right subplot
for i in range(percentage_increase_2.shape[1]):
    ax2.bar(x + i * bar_width, percentage_increase_2[:, i], width=bar_width, color=colors[i], label=f'{labels[i]}')

ax2.set_ylim(70, 110)
ax2.set_yticks(np.arange(70, 120, 10))  # 设置 y 轴刻度为每隔 10%
ax2.set_yticklabels([])  # 清空y轴标签
# ax2.set_ylabel('Normalized Avg Latency\nPer Memory Access (%)', fontsize=14)
ax2.yaxis.set_label_coords(-0.08, 0.44)  # 调整标签位置

# 对b图也做同样的调整
x_positions_2 = x + bar_width * (percentage_increase_2.shape[1] - 1) / 2
x_positions_2[4] = x_positions_2[4] - 0.03  # Hetero_1 向左移一点
x_positions_2[5] = x_positions_2[5] + 0.03  # Hetero_2 向右移一点

ax2.set_xticks(x_positions_2)
ax2.set_xticklabels(load_names, fontsize=12)
ax2.legend(fontsize=12, ncol=2)  # 改为2列显示图例

# Add (b) label
# ax2.text(-0.178, 112, '(b)', fontsize=12, va='top', ha='right')

plt.subplots_adjust(left=0.08, right=0.98, top=0.92, bottom=0.18, wspace=0.07)  # 减小子图间距
# plt.savefig("eva_fig1_ab.pdf", format="pdf", dpi=300, bbox_inches='tight')
plt.savefig("eva_fig1_ab.pdf", format="pdf", dpi=300, bbox_inches='tight')
plt.savefig("eva_fig1_ab.svg", format="svg", bbox_inches='tight')
plt.show()

# 计算并打印a图和b图的百分比变化
print("data1 (a图)数值:")
for i, load_name in enumerate(load_names):
    print(f"{load_name}: {percentage_increase_1[i]}")

print("\ndata2 (b图)数值:")
for i, load_name in enumerate(load_names):
    print(f"{load_name}: {percentage_increase_2[i]}")
