{"cells": [{"cell_type": "code", "execution_count": 22, "metadata": {"scrolled": false}, "outputs": [], "source": ["import os\n", "import time\n", "from itertools import count\n", "import numpy  as np\n", "import pandas as pd\n", "\n", "import gymnasium as gym\n", "\n", "\n", "class SARSA(object):\n", "    def __init__(self, env, env_play) -> None:\n", "        # == epsilon\n", "        self.max_epsilon = 1      # 探索系数最大值\n", "        self.min_epsilon = 0.05   # 探索系数最小值\n", "        self.epsilon = self.max_epsilon\n", "\n", "        # == env\n", "        self.episodes = 10000    # 游戏局数\n", "        self.env = env\n", "        self.env_play = env_play\n", "\n", "        # == Q-learning\n", "        self.alpha    = 0.5        #学习率  \n", "        self.gamma    = 0.95    #折扣因子\n", "        self.q_table_csv = './q_table_{}x{}.csv'.format(self.env.observation_space.n, self.env.action_space.n)\n", "\n", "        self.q_table = pd.DataFrame(\n", "            np.zeros((self.env.observation_space.n, self.env.action_space.n)),  \n", "            index=range(0, self.env.observation_space.n),     # 对应 环境的维度，observation （0，15）， 也就是position\n", "            columns=range(0, self.env.action_space.n)         # 对应 动作空间维度，action （0，3） 。1表示下，2表示右\n", "            )\n", "            # 整个q_table 表示 当处于 position 位置时，选择哪个方向最可能到达终点\n", "\n", "        # == train\n", "        self.best_ave_reward = -1\n", "        self.best_q_table = None\n", "\n", "        print(' -------- init. qtable： \\n', self.q_table)\n", "\n", "    def epsilon_decay(self, episode): \n", "        a = 7.5  # 比例系数\n", "        epsilon = self.min_epsilon + (self.max_epsilon - self.min_epsilon) * np.exp(-a * episode/ self.episodes)   # 指数衰减\n", "        return epsilon\n", "\n", "    def select_action(self, state, greedy=False):\n", "        e = np.random.uniform()\n", "        action = None\n", "        if (e < self.epsilon or (self.q_table.iloc[state] == 0).all()) and not greedy:   \n", "            action = self.env.action_space.sample()\n", "        else:\n", "            action = self.q_table.iloc[state].idxmax()\n", "        return action\n", "\n", "    def update_q_table(self, state, action, reward, next_state, next_action):    # 计算 state (s), action (a) --》 next_state (s'), next_state (a') 时的动作价值函数 \n", "        # Q_{t+1}(s, a) = Q_t(s, a) + \\alpha \\cdot (R(s, a) + \\gamma \\cdot \\max_{a'} Q_t(s', a') - Q_t(s, a))\n", "        q = self.q_table.iloc[state][action]       # Q(s_t, a_t)\n", "        #q_new = q + self.alpha * (reward + self.gamma * self.q_table.iloc[next_state].max() - q)    #  Q-learning\n", "        q_new = q + self.alpha * (reward + self.gamma * self.q_table.iloc[next_state, next_action] - q)  # SARSA\n", "        self.q_table.iloc[state][action] = q_new\n", "\n", "    def train(self):\n", "        for episode in range(self.episodes):\n", "            rewards = []\n", "            successes = []\n", "\n", "            observation, info = self.env.reset()\n", "            #observation, info = self.env.reset(seed=42)  # 固定种子\n", "\n", "            action = self.select_action(observation)\n", "            # ======== For each step\n", "            for step_num in range(100):\n", "\n", "                observation_new, reward, terminated, truncated, info = self.env.step(action)\n", "                action_new = self.select_action(observation_new)\n", "                #print(' -- episode, step_num, action, observation_new, reward, terminated, truncated, info: ', episode, step_num, action, observation_new, reward, terminated, truncated, info)\n", "\n", "                # Truncated在官方定义中用于处理比如超时等特殊结束的情况。\n", "                '''\n", "                observation (ObsType) : 环境观察空间的一个元素，作为代理动作的下一个观察结果\n", "                reward (SupportsFloat) : 采取行动的结果的奖励。 成功到达目的地会得到奖励 1，否则奖励为 0\n", "                terminated (bool) : 代理是否达到最终状态，可以是正数或负数。\n", "                truncated (bool) : 是否满足MDP范围外的截断条件。 通常，这是一个时间限制，但也可用于指示代理实际越界。 可用于在达到最终状态之前提前结束情节。\n", "                info (dict) : 包含辅助诊断信息（有助于调试、学习和记录）。\n", "                '''\n", "                success = reward\n", "                done = terminated or truncated\n", "                if done and reward == 0:  # 调入冰窟 给负分\n", "                    reward = -1\n", "\n", "\n", "\n", "                self.update_q_table(observation, action, reward, observation_new, action_new)\n", "                observation = observation_new\n", "                action = action_new\n", "\n", "                successes.append(success)\n", "                rewards.append(reward)\n", "\n", "                if done:\n", "                    self.epsilon = self.epsilon_decay(episode)\n", "                    break\n", "\n", "            ave_reward = sum(rewards)/len(rewards)\n", "            ave_successes = sum(successes)/len(successes)\n", "\n", "            if ave_reward > self.best_ave_reward:\n", "                self.best_ave_reward = ave_reward\n", "                self.best_q_table = self.q_table\n", "\n", "\n", "            if episode%1000 == 0: print(' Train ---- episode={}, epsilon={:.3f}, ave_successes={:.3f} ave_reward={:.3f} '.format(episode, self.epsilon, ave_successes, ave_reward))\n", "            #print(' -- q_table:\\n',qlearn.q_table)\n", "\n", "        # save csv\n", "        self.best_q_table.to_csv(self.q_table_csv, index=False)\n", "\n", "    def test(self):\n", "        #self.env.render(render_mode='human')  # 在这里指定渲染模式\n", "        #self.env.render(mode='human')\n", "\n", "        if os.path.exists(self.q_table_csv):\n", "            dtype = dict(zip(np.array([str(x) for x in np.arange(0,self.env_play.action_space.n)]), np.array(['float64'] * self.env_play.action_space.n)))\n", "            self.q_table = pd.read_csv(self.q_table_csv, header=0, dtype=dtype)\n", "            print(' ---- read q_table: \\n', self.q_table)\n", "\n", "            observation, info = self.env_play.reset()\n", "            #observation, info = self.env_play.reset(seed=42)   # 固定种子\n", "\n", "            time.sleep(2)\n", "\n", "            step_num = -1\n", "            done = False\n", "            while not done:\n", "                step_num += 1\n", "\n", "                action = self.select_action(observation, True)\n", "                observation_new, reward, terminated, truncated, info = self.env_play.step(int(action))\n", "                done = terminated or truncated\n", "                observation = observation_new\n", "\n", "                print(' Test ---- step_num, action, reward, observation: ', step_num, action, reward, observation)\n", "\n", "                time.sleep(0.1)\n", "            \n", "            time.sleep(2)\n", "            env_play.close()\n", "\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" -------- init. qtable： \n", "       0    1    2    3\n", "0   0.0  0.0  0.0  0.0\n", "1   0.0  0.0  0.0  0.0\n", "2   0.0  0.0  0.0  0.0\n", "3   0.0  0.0  0.0  0.0\n", "4   0.0  0.0  0.0  0.0\n", "5   0.0  0.0  0.0  0.0\n", "6   0.0  0.0  0.0  0.0\n", "7   0.0  0.0  0.0  0.0\n", "8   0.0  0.0  0.0  0.0\n", "9   0.0  0.0  0.0  0.0\n", "10  0.0  0.0  0.0  0.0\n", "11  0.0  0.0  0.0  0.0\n", "12  0.0  0.0  0.0  0.0\n", "13  0.0  0.0  0.0  0.0\n", "14  0.0  0.0  0.0  0.0\n", "15  0.0  0.0  0.0  0.0\n", " Train ---- episode=0, epsilon=1.000, ave_successes=0.000 ave_reward=-0.083 \n", " Train ---- episode=1000, epsilon=0.499, ave_successes=0.000 ave_reward=-0.077 \n", " Train ---- episode=2000, epsilon=0.262, ave_successes=0.000 ave_reward=-0.500 \n", " Train ---- episode=3000, epsilon=0.150, ave_successes=0.056 ave_reward=0.056 \n", " Train ---- episode=4000, epsilon=0.097, ave_successes=0.012 ave_reward=0.012 \n", " Train ---- episode=5000, epsilon=0.072, ave_successes=0.167 ave_reward=0.167 \n", " Train ---- episode=6000, epsilon=0.061, ave_successes=0.167 ave_reward=0.167 \n", " Train ---- episode=7000, epsilon=0.055, ave_successes=0.000 ave_reward=-0.500 \n", " Train ---- episode=8000, epsilon=0.052, ave_successes=0.167 ave_reward=0.167 \n", " Train ---- episode=9000, epsilon=0.051, ave_successes=0.167 ave_reward=0.167 \n"]}], "source": ["\n", "# ==== 创建环境\n", "env = gym.make('FrozenLake-v1', desc=None, map_name='4x4', is_slippery=False)    # 无滑动\n", "env_play = gym.make('FrozenLake-v1', desc=None, map_name=\"4x4\", is_slippery=False, render_mode='human')\n", "\n", "#env = gym.make('FrozenLake-v1', desc=None, map_name='4x4', is_slippery=True)  # 有滑动\n", "#env_play = gym.make('FrozenLake-v1', desc=None, map_name=\"4x4\", is_slippery=True, render_mode='human')\n", "\n", "'''\n", "'FrozenLake-v1'：这是环境的名称，FrozenLake 是一个经典的强化学习问题，代表一个冰冻湖面上的智能体需要在湖面上移动，避开洞口，到达目标位置。这个环境有一个简单的离散状态空间和动作空间。\n", "desc=None：desc 参数用于指定环境的地图描述。如果设置为 None，则使用 map_name 参数指定的地图。如果提供了一个地图描述，那么将使用这个描述创建环境。\n", "map_name=\"4x4\"：这个参数指定了环境的地图大小。默认情况下，FrozenLake 环境是一个 4x4 的地图，这里我们将其扩展到 4x4 的大小。\n", "is_slippery=True：这个参数表示地面是否有滑动效果。如果设置为 True，智能体在执行动作时有一定的概率滑动到非预期的相邻格子。这会使问题更具挑战性，因为智能体需要学会在不确定性条件下做出决策。\n", "render_mode='human'：这个参数指定了环境的渲染模式。'human' 表示环境将以人类可读的方式呈现，通常是通过图形界面或者命令行输出。这有助于我们观察智能体在环境中的表现。\n", "'''\n", "\n", "# ==== 初始化算法\n", "s = SARSA(env, env_play)\n", "\n", "\n", "# ==== train\n", "s.train()\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" ---- read q_table: \n", "            0         1         2         3\n", "0   0.575330  0.770544  0.308722  0.521164\n", "1  -0.070635 -1.000000  0.646659 -0.395065\n", "2   0.065328 -0.077599 -0.271411 -0.240513\n", "3  -0.065764 -1.000000 -0.219803 -0.559932\n", "4   0.097909  0.814137 -1.000000 -0.567106\n", "5   0.000000  0.000000  0.000000  0.000000\n", "6  -1.000000  0.901587 -1.000000 -0.065219\n", "7   0.000000  0.000000  0.000000  0.000000\n", "8  -0.335055 -1.000000  0.857371  0.362595\n", "9   0.291692  0.902500 -0.023768 -1.000000\n", "10  0.598367  0.949999 -1.000000  0.468983\n", "11  0.000000  0.000000  0.000000  0.000000\n", "12  0.000000  0.000000  0.000000  0.000000\n", "13 -1.000000  0.343213  0.950000  0.576586\n", "14  0.674993  0.950000  1.000000  0.857098\n", "15  0.000000  0.000000  0.000000  0.000000\n"]}, {"ename": "error", "evalue": "display Surface quit", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31<PERSON><PERSON>r\u001b[0m                                     <PERSON><PERSON> (most recent call last)", "\u001b[1;32m<ipython-input-21-188db587a917>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[1;31m# ==== test\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 2\u001b[1;33m \u001b[0ms\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtest\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32m<ipython-input-18-22a670e65e58>\u001b[0m in \u001b[0;36mtest\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    124\u001b[0m             \u001b[0mprint\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m' ---- read q_table: \\n'\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mq_table\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    125\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 126\u001b[1;33m             \u001b[0mobservation\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0minfo\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0menv_play\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mreset\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    127\u001b[0m             \u001b[1;31m#observation, info = self.env_play.reset(seed=42)   # 固定种子\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    128\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mE:\\Learning_Software\\Anaconda3\\lib\\site-packages\\gymnasium\\wrappers\\common.py\u001b[0m in \u001b[0;36mreset\u001b[1;34m(self, seed, options)\u001b[0m\n\u001b[0;32m    144\u001b[0m         \"\"\"\n\u001b[0;32m    145\u001b[0m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_elapsed_steps\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;36m0\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 146\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0msuper\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mreset\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mseed\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mseed\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0moptions\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0moptions\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    147\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    148\u001b[0m     \u001b[1;33m@\u001b[0m\u001b[0mproperty\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mE:\\Learning_Software\\Anaconda3\\lib\\site-packages\\gymnasium\\core.py\u001b[0m in \u001b[0;36mreset\u001b[1;34m(self, seed, options)\u001b[0m\n\u001b[0;32m    326\u001b[0m     ) -> tuple[WrapperObsType, dict[str, Any]]:\n\u001b[0;32m    327\u001b[0m         \u001b[1;34m\"\"\"Uses the :meth:`reset` of the :attr:`env` that can be overwritten to change the returned data.\"\"\"\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 328\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0menv\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mreset\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mseed\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mseed\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0moptions\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0moptions\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    329\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    330\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mrender\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[0mRenderFrame\u001b[0m \u001b[1;33m|\u001b[0m \u001b[0mlist\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mRenderFrame\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m|\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mE:\\Learning_Software\\Anaconda3\\lib\\site-packages\\gymnasium\\wrappers\\common.py\u001b[0m in \u001b[0;36mreset\u001b[1;34m(self, seed, options)\u001b[0m\n\u001b[0;32m    398\u001b[0m         \u001b[1;34m\"\"\"Resets the environment with `kwargs`.\"\"\"\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    399\u001b[0m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_has_reset\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;32mTrue\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 400\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0msuper\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mreset\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mseed\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mseed\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0moptions\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0moptions\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    401\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    402\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mrender\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[0mRenderFrame\u001b[0m \u001b[1;33m|\u001b[0m \u001b[0mlist\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mRenderFrame\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m|\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mE:\\Learning_Software\\Anaconda3\\lib\\site-packages\\gymnasium\\core.py\u001b[0m in \u001b[0;36mreset\u001b[1;34m(self, seed, options)\u001b[0m\n\u001b[0;32m    326\u001b[0m     ) -> tuple[WrapperObsType, dict[str, Any]]:\n\u001b[0;32m    327\u001b[0m         \u001b[1;34m\"\"\"Uses the :meth:`reset` of the :attr:`env` that can be overwritten to change the returned data.\"\"\"\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 328\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0menv\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mreset\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mseed\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mseed\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0moptions\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0moptions\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    329\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    330\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mrender\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[0mRenderFrame\u001b[0m \u001b[1;33m|\u001b[0m \u001b[0mlist\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mRenderFrame\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m|\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mE:\\Learning_Software\\Anaconda3\\lib\\site-packages\\gymnasium\\wrappers\\common.py\u001b[0m in \u001b[0;36mreset\u001b[1;34m(self, seed, options)\u001b[0m\n\u001b[0;32m    293\u001b[0m             \u001b[1;32mreturn\u001b[0m \u001b[0menv_reset_passive_checker\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0menv\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mseed\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mseed\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0moptions\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0moptions\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    294\u001b[0m         \u001b[1;32melse\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 295\u001b[1;33m             \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0menv\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mreset\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mseed\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mseed\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0moptions\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0moptions\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    296\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    297\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mrender\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[0mRenderFrame\u001b[0m \u001b[1;33m|\u001b[0m \u001b[0mlist\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mRenderFrame\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m|\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mE:\\Learning_Software\\Anaconda3\\lib\\site-packages\\gymnasium\\envs\\toy_text\\frozen_lake.py\u001b[0m in \u001b[0;36mreset\u001b[1;34m(self, seed, options)\u001b[0m\n\u001b[0;32m    321\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    322\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mrender_mode\u001b[0m \u001b[1;33m==\u001b[0m \u001b[1;34m\"human\"\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 323\u001b[1;33m             \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mrender\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    324\u001b[0m         \u001b[1;32mreturn\u001b[0m \u001b[0mint\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0ms\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m{\u001b[0m\u001b[1;34m\"prob\"\u001b[0m\u001b[1;33m:\u001b[0m \u001b[1;36m1\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    325\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mE:\\Learning_Software\\Anaconda3\\lib\\site-packages\\gymnasium\\envs\\toy_text\\frozen_lake.py\u001b[0m in \u001b[0;36mrender\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    337\u001b[0m             \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_render_text\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    338\u001b[0m         \u001b[1;32melse\u001b[0m\u001b[1;33m:\u001b[0m  \u001b[1;31m# self.render_mode in {\"human\", \"rgb_array\"}:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 339\u001b[1;33m             \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_render_gui\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mrender_mode\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    340\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    341\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0m_render_gui\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mmode\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mE:\\Learning_Software\\Anaconda3\\lib\\site-packages\\gymnasium\\envs\\toy_text\\frozen_lake.py\u001b[0m in \u001b[0;36m_render_gui\u001b[1;34m(self, mode)\u001b[0m\n\u001b[0;32m    407\u001b[0m                 \u001b[0mrect\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;33m(\u001b[0m\u001b[1;33m*\u001b[0m\u001b[0mpos\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m*\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcell_size\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    408\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 409\u001b[1;33m                 \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mwindow_surface\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mblit\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mice_img\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mpos\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    410\u001b[0m                 \u001b[1;32mif\u001b[0m \u001b[0mdesc\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0my\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mx\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m==\u001b[0m \u001b[1;34mb\"H\"\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    411\u001b[0m                     \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mwindow_surface\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mblit\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mhole_img\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mpos\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31merror\u001b[0m: display Surface quit"]}], "source": ["# ==== 创建环境\n", "env = gym.make('FrozenLake-v1', desc=None, map_name='4x4', is_slippery=False)    # 无滑动\n", "env_play = gym.make('FrozenLake-v1', desc=None, map_name=\"4x4\", is_slippery=False, render_mode='human')\n", "\n", "\n", "# ==== test\n", "s.test()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 4}