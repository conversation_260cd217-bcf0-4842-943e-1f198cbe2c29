{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.ticker import FuncFormatter\n", "\n", "# Define constants\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# Data for the chart\n", "labels = ['LRU', 'NS-Alchemy', 'Alchemy']\n", "data = [13.5534,13.1247 ,12.7967]  # Example data for LRU, NS-Alchemy, Alchemy\n", "\n", "# Convert list to NumPy array\n", "data = np.array(data)\n", "\n", "# Transform delay as per the formula\n", "data = data / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) + (1 - data / 100) * (DDR_DELAY + CXL_DELAY)\n", "\n", "# Calculate percentage increase\n", "baseline = data[0]  # Assuming LRU is the baseline\n", "percentage_increase = data / baseline * 100\n", "\n", "# Define colors\n", "colors = ['#1c74b4', '#2ca42c', '#fc7c0c']\n", "\n", "# Create the figure\n", "fig, ax = plt.subplots(figsize=(6, 3))\n", "\n", "# Plot the chart\n", "bar_width = 0.3\n", "x = np.arange(len(labels))\n", "\n", "ax.bar(x, percentage_increase, width=bar_width, color=colors)\n", "\n", "ax.set_ylim(70, 115)\n", "ax.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax.set_ylabel('Normalized\\nAccess Latency (%)', fontsize=14)\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(labels, fontsize=15)\n", "\n", "# Modify y-axis tick labels' font size and rotation\n", "for label in ax.get_yticklabels():\n", "    label.set_fontsize(10)\n", "    label.set_rotation(45)\n", "\n", "plt.subplots_adjust(left=0.15, right=0.95, top=0.95, bottom=0.18)\n", "plt.savefig(\"eva_fig1_a.pdf\", format=\"pdf\", dpi=300)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.ticker import FuncFormatter\n", "\n", "# Define constants\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# Data for the chart\n", "labels = ['NS-Alchemy', 'Alchemy']\n", "data = [13.5534, 13.1247, 12.7967]  # Example data for LRU, NS-Alchemy, Alchemy\n", "\n", "# Convert list to NumPy array\n", "data = np.array(data)\n", "\n", "# Transform delay as per the formula\n", "transformed_data = data / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) + (1 - data / 100) * (DDR_DELAY + CXL_DELAY)\n", "\n", "# Calculate percentage decrease compared to LRU\n", "baseline = transformed_data[0]  # LRU is the baseline\n", "percentage_decrease = (baseline - transformed_data[1:]) / baseline * 100\n", "\n", "# Define colors\n", "colors = ['#2ca42c', '#fc7c0c']  # Colors for NS-Alchemy and Alchemy\n", "\n", "# Create the figure\n", "fig, ax = plt.subplots(figsize=(6, 3))\n", "\n", "# Plot the chart\n", "bar_width = 0.3  # Increased bar width to reduce space between bars\n", "x = np.arange(len(labels)) * 0.4  # Adjust x position to bring bars closer\n", "\n", "ax.bar(x, percentage_decrease, width=bar_width, color=colors)\n", "\n", "ax.set_ylim(0, 10)\n", "ax.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax.set_ylabel('Latency Reduction\\nCompared to LRU (%)', fontsize=14)\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(labels, fontsize=15)\n", "\n", "# Modify y-axis tick labels' font size and rotation\n", "for label in ax.get_yticklabels():\n", "    label.set_fontsize(10)\n", "    label.set_rotation(45)\n", "\n", "plt.subplots_adjust(left=0.15, right=0.95, top=0.95, bottom=0.18)\n", "plt.savefig(\"eva_fig1_a_reduction.pdf\", format=\"pdf\", dpi=300)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x216 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.ticker import FuncFormatter\n", "\n", "# Define constants\n", "SSD_DELAY = 75000\n", "SSD_Write = 900000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "r_radio = 169586045 \n", "w_radio = 58719492\n", "\n", "# Data for the a图\n", "labels_a = ['NS-Alchemy', 'Alchemy']\n", "data_a = [13.5534, 13.1247, 12.7967]  # Example data for LRU, NS-Alchemy, Alchemy\n", "\n", "# Convert list to NumPy array\n", "data_a = np.array(data_a)\n", "\n", "# Transform delay as per the formula\n", "transformed_data_a = data_a / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) * (r_radio / (r_radio+w_radio)) +data_a / 100 * (SSD_Write + CXL_DELAY + DDR_DELAY) * (w_radio / (r_radio+w_radio)) + (1 - data_a / 100) * (DDR_DELAY + CXL_DELAY)\n", "\n", "# Calculate percentage decrease compared to LRU\n", "baseline_a = transformed_data_a[0]  # LRU is the baseline\n", "percentage_decrease_a = (baseline_a - transformed_data_a[1:]) / baseline_a * 100\n", "\n", "# Data for the b图\n", "labels_b = ['NS-Alchemy', 'Alchemy']\n", "data_b = [37, 63]  # Efficiency data\n", "\n", "# Define colors\n", "colors_a = ['#2ca42c', '#ff7f0e']  # Colors for a图\n", "colors_b = ['#2ca42c', '#ff7f0e']  # Colors for b图\n", "\n", "# Create the figure with two subplots\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(8, 3), gridspec_kw={'width_ratios': [1, 1]})\n", "\n", "# Plot the a图\n", "bar_width_a = 0.3\n", "x_a = np.arange(len(labels_a)) * 0.4\n", "\n", "ax1.bar(x_a, percentage_decrease_a, width=bar_width_a, color=colors_a)\n", "\n", "ax1.set_ylim(0, 10)\n", "ax1.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax1.set_ylabel('Latency Reduction\\nCompared to LRU (%)', fontsize=14)\n", "ax1.set_xticks(x_a)\n", "ax1.set_xticklabels(labels_a, fontsize=15)\n", "\n", "# Modify y-axis tick labels' font size and rotation for a图\n", "for label in ax1.get_yticklabels():\n", "    label.set_fontsize(10)\n", "    label.set_rotation(45)\n", "\n", "# Plot the b图\n", "bar_width_b = 0.3\n", "x_b = np.arange(len(labels_b)) * 0.4\n", "\n", "ax2.bar(x_b, data_b, width=bar_width_b, color=colors_b)\n", "\n", "ax2.set_ylim(0, 100)\n", "ax2.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax2.set_ylabel('Efficiency of Bypass (%)', fontsize=14)\n", "ax2.set_xticks(x_b)\n", "ax2.set_xticklabels(labels_b, fontsize=15)\n", "\n", "# Modify y-axis tick labels' font size and rotation for b图\n", "for label in ax2.get_yticklabels():\n", "    label.set_fontsize(10)\n", "    label.set_rotation(45)\n", "\n", "# Adjust layout for tight spacing\n", "plt.subplots_adjust(left=0.1, right=0.95, top=0.95, bottom=0.18, wspace=0.3)\n", "\n", "plt.savefig(\"eva_fig1_combined.pdf\", format=\"pdf\", dpi=300)\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 4}