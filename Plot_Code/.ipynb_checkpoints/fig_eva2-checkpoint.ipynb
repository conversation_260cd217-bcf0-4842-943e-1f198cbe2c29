{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 设置x轴的方差值\n", "x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "miss_rate1 = np.array([\n", "    [13.5245, 13.5245, 13.5245, 13.5245],  # 0\n", "    [13.3654,13.9613,13.7722,12.9922],  # 0.05\n", "    [13.8773,12.5561,13.6145,13.2483],   # 0.1\n", "    [14.8649,12.1167,14.3195,12.7638],  # 0.15\n", "    [13.7527,12.7334,12.0161,16.1458],   # 0.2\n", "    [17.1195,13.3654,12.3517,11.7299],   # 0.25\n", "    [11.4717,11.5630,16.3082,15.6864],    # 0.3\n", "    [17.2171,10.4774,15.2132,12.6276],  # 0.35\n", "    [19.9405,10.6939,12.9488,12.482],   # 0.4\n", "    [10.2857,11.3586,17.6018,17.3128],   # 0.45\n", "    [10.4085,18.8821,10.2195,17.3646]    # 0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # 0\n", "    [11.9021, 10.7415, 11.7794, 12.2140],  # 0.05\n", "    [11.8459, 12.7726, 12.7264, 9.8086],   # 0.1\n", "    [10.9287, 10.3429, 10.1843, 11.7396],  # 0.15\n", "    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2\n", "    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25\n", "    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3\n", "    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35\n", "    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4\n", "    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45\n", "    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [12.8445,13.1008,13.0639,12.8571],  # 0\n", "    [11.4220,10.2345,11.4041,11.8096],  # 0.05\n", "    [11.6084,12.1991,12.1368,9.3781],   # 0.1\n", "    [10.5929,9.7987,9.6842,11.7209],  # 0.15\n", "    [11.6801,9.5426,10.3566,8.9833],   # 0.2\n", "    [9.8340,8.4032,11.6565,9.3683],   # 0.25\n", "    [11.8439,8.2294,9.5093,9.5904],    # 0.3\n", "    [10.4275,11.3183,7.4935,11.7701],  # 0.35\n", "    [12.1999,8.7277,7.6005,11.4067],   # 0.4\n", "    [7.5993,7.9855,12.7783,10.3074],   # 0.45\n", "    [12.4341,10.0069,9.1605,6.6899]    # 0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 计算最大值和最小值的差异并用最小值进行归一化\n", "diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)\n", "diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)\n", "diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)\n", "\n", "# 创建折线图\n", "plt.figure(figsize=(10, 3))\n", "\n", "plt.plot(x_variances, diff3, marker='o', color='red', label='Shared Alchemy')\n", "plt.plot(x_variances, diff1, marker='o', color='blue', label='Partition LRU')\n", "plt.plot(x_variances, diff2, marker='o', color='green', label='Shared LRU')\n", "\n", "# 设置图表标签和标题\n", "plt.xlabel('Standard Deviation of CN Performance', fontsize=16)\n", "plt.ylabel('Normalized Max\\nDiff in Avg Latency', fontsize=16)\n", "\n", "# 设置x轴刻度\n", "plt.xticks(x_variances, fontsize=16)\n", "plt.yticks(fontsize=16)  # 设置y轴刻度的字体大小\n", "\n", "# 添加图例\n", "plt.legend(loc='upper left', fontsize=15)\n", "\n", "# 显示图表\n", "plt.grid(True)\n", "plt.savefig('eva_fig2.pdf', dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 设置x轴的方差值\n", "x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "miss_rate1 = np.array([\n", "    [13.5245, 13.5245, 13.5245, 13.5245],  # 0\n", "    [13.3654,13.9613,13.7722,12.9922],  # 0.05\n", "    [13.8773,12.5561,13.6145,13.2483],   # 0.1\n", "    [14.8649,12.1167,14.3195,12.7638],  # 0.15\n", "    [13.7527,12.7334,12.0161,16.1458],   # 0.2\n", "    [17.1195,13.3654,12.3517,11.7299],   # 0.25\n", "    [11.4717,11.5630,16.3082,15.6864],    # 0.3\n", "    [17.2171,10.4774,15.2132,12.6276],  # 0.35\n", "    [19.9405,10.6939,12.9488,12.482],   # 0.4\n", "    [10.2857,11.3586,17.6018,17.3128],   # 0.45\n", "    [10.4085,18.8821,10.2195,17.3646]    # 0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # 0\n", "    [11.9021, 10.7415, 11.7794, 12.2140],  # 0.05\n", "    [11.8459, 12.7726, 12.7264, 9.8086],   # 0.1\n", "    [10.9287, 10.3429, 10.1843, 11.7396],  # 0.15\n", "    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2\n", "    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25\n", "    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3\n", "    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35\n", "    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4\n", "    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45\n", "    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [12.8445,13.1008,13.0639,12.8571],  # 0\n", "    [11.4220,10.2345,11.4041,11.8096],  # 0.05\n", "    [11.6084,12.1991,12.1368,9.3781],   # 0.1\n", "    [10.5929,9.7987,9.6842,11.7209],  # 0.15\n", "    [11.6801,9.5426,10.3566,8.9833],   # 0.2\n", "    [9.8340,8.4032,11.6565,9.3683],   # 0.25\n", "    [11.8439,8.2294,9.5093,9.5904],    # 0.3\n", "    [10.4275,11.3183,7.4935,11.7701],  # 0.35\n", "    [12.1999,8.7277,7.6005,11.4067],   # 0.4\n", "    [7.5993,7.9855,12.7783,10.3074],   # 0.45\n", "    [12.4341,10.0069,9.1605,6.6899]    # 0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 计算最大值和最小值的差异并用最小值进行归一化\n", "diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)\n", "diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)\n", "diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)\n", "\n", "# 创建折线图\n", "plt.figure(figsize=(10, 4))  # 调整图表大小\n", "\n", "plt.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')\n", "plt.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='Partition LRU')\n", "plt.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')\n", "\n", "# 设置图表标签和标题\n", "plt.xlabel('Standard Deviation of CN Performance', fontsize=16)\n", "plt.ylabel('Normalized Max\\nDiff in Avg Latency', fontsize=16)\n", "\n", "# 设置x轴刻度\n", "plt.xticks(x_variances, fontsize=14)\n", "plt.yticks(fontsize=14)\n", "\n", "# 添加图例\n", "plt.legend(loc='upper left', fontsize=14, frameon=False)\n", "\n", "# 添加网格\n", "plt.grid(True, linestyle='--', alpha=0.7)\n", "\n", "# 显示图表\n", "plt.tight_layout()\n", "plt.savefig('eva_fig2.pdf', dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1008x252 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import matplotlib.gridspec as gridspec\n", "\n", "# 设置x轴的方差值\n", "x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "miss_rate1 = np.array([\n", "    [13.5245, 13.5245, 13.5245, 13.5245],  # 0\n", "    [13.3654,13.9613,13.7722,12.9922],  # 0.05\n", "    [13.8773,12.5561,13.6145,13.2483],   # 0.1\n", "    [14.8649,12.1167,14.3195,12.7638],  # 0.15\n", "    [13.7527,12.7334,12.0161,16.1458],   # 0.2\n", "    [17.1195,13.3654,12.3517,11.7299],   # 0.25\n", "    [11.4717,11.5630,16.3082,15.6864],    # 0.3\n", "    [17.2171,10.4774,15.2132,12.6276],  # 0.35\n", "    [19.9405,10.6939,12.9488,12.482],   # 0.4\n", "    [10.2857,11.3586,17.6018,17.3128],   # 0.45\n", "    [10.4085,18.8821,10.2195,17.3646]    # 0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # 0\n", "    [11.9021, 10.7415, 11.7794, 12.2140],  # 0.05\n", "    [11.8459, 12.7726, 12.7264, 9.8086],   # 0.1\n", "    [10.9287, 10.3429, 10.1843, 11.7396],  # 0.15\n", "    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2\n", "    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25\n", "    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3\n", "    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35\n", "    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4\n", "    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45\n", "    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [12.8445,13.1008,13.0639,12.8571],  # 0\n", "    [11.4220,10.2345,11.4041,11.8096],  # 0.05\n", "    [11.6084,12.1991,12.1368,9.3781],   # 0.1\n", "    [10.5929,9.7987,9.6842,11.7209],  # 0.15\n", "    [11.6801,9.5426,10.3566,8.9833],   # 0.2\n", "    [9.8340,8.4032,11.6565,9.3683],   # 0.25\n", "    [11.8439,8.2294,9.5093,9.5904],    # 0.3\n", "    [10.4275,11.3183,7.4935,11.7701],  # 0.35\n", "    [12.1999,8.7277,7.6005,11.4067],   # 0.4\n", "    [7.5993,7.9855,12.7783,10.3074],   # 0.45\n", "    [12.4341,10.0069,9.1605,6.6899]    # 0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 计算最大值和最小值的差异并用最小值进行归一化\n", "diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)\n", "diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)\n", "diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)\n", "\n", "# 计算平均值\n", "avg_latency1 = np.mean(memory_performance_data1, axis=1) #PARTITION LRU\n", "avg_latency2 = np.mean(memory_performance_data2, axis=1) #SHARED LRU\n", "avg_latency3 = np.mean(memory_performance_data3, axis=1) #SHARED Alchemy\n", "\n", "# 选择特定的x轴数据\n", "selected_indices = [2, 6, 10]  # 对应于x_variances中的0.1, 0.3, 0.5\n", "selected_x = [x_variances[i] for i in selected_indices]\n", "selected_avg_latency1 = [avg_latency1[i] for i in selected_indices]\n", "selected_avg_latency2 = [avg_latency2[i] / avg_latency1[i] for i in selected_indices]  # 归一化\n", "selected_avg_latency3 = [avg_latency3[i] / avg_latency1[i] for i in selected_indices]  # 归一化\n", "\n", "# 创建图表，并使用gridspec布局\n", "fig = plt.figure(figsize=(14, 3.5))\n", "gs = gridspec.GridSpec(1, 2, width_ratios=[7, 3])  # 70% for a图, 30% for b图\n", "\n", "# 绘制第一个子图 (a图)\n", "ax1 = fig.add_subplot(gs[0])\n", "\n", "# 绘制第一个y轴的数据\n", "# ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')\n", "ax1.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='Partition LRU')\n", "ax1.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')\n", "ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')\n", "# 设置第一个y轴的标签\n", "ax1.set_xlabel('Standard Deviation of CN Performance', fontsize=18)\n", "ax1.set_ylabel('Normalized Max\\nDiff in Avg Latency', fontsize=18)\n", "\n", "# 设置x轴和y轴刻度\n", "ax1.set_xticks(x_variances)\n", "ax1.tick_params(axis='x', labelsize=18)\n", "ax1.tick_params(axis='y', labelsize=20)\n", "\n", "# 添加图例\n", "ax1.legend(loc='upper left', fontsize=16, frameon=False)\n", "# Add (a) label\n", "ax1.text(-0.04, max(diff3) + 0.075, '(a)', fontsize=18, fontweight='bold', va='top', ha='right')\n", "\n", "# 添加网格\n", "ax1.grid(True, linestyle='--', alpha=0.7)\n", "\n", "# 绘制第二个子图 (b图)\n", "ax2 = fig.add_subplot(gs[1])\n", "\n", "# 绘制柱状图\n", "bar_width = 0.2\n", "index = np.arange(len(selected_x))\n", "\n", "colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']  # 指定颜色\n", "\n", "ax2.bar(index, [1]*len(selected_avg_latency1), bar_width, color=colors[0], label='Partition LRU')  # 基准\n", "ax2.bar(index + bar_width, selected_avg_latency2, bar_width, color=colors[1], label='Shared LRU')\n", "ax2.bar(index + 2 * bar_width, selected_avg_latency3, bar_width, color=colors[2], label='Shared Alchemy')\n", "\n", "# 设置右侧柱状图的 y 轴范围\n", "ax2.set_ylim(0.5, 1.19)\n", "\n", "# 设置第二个y轴的标签\n", "ax2.set_xlabel('Selected Standard Deviations', fontsize=16)\n", "ax2.set_ylabel('Normalized Average\\nAccess Latency', fontsize=16)\n", "ax2.set_xticks(index + bar_width)\n", "ax2.set_xticklabels(['0.1', '0.3', '0.5'])\n", "ax2.tick_params(axis='x', labelsize=20)\n", "ax2.tick_params(axis='y', labelsize=17)\n", "\n", "# 添加第二个图例\n", "ax2.legend(loc='upper right', fontsize=14, ncol=1)\n", "# Add (b) label\n", "ax2.text(-0.35, 1.2, '(b)', fontsize=18, fontweight='bold', va='top', ha='right')\n", "\n", "# # 添加网格\n", "# ax2.grid(True, linestyle='--', alpha=0.7)\n", "# 增加子图之间的间距\n", "# gs.update(wspace=0.28)  # 你可以根据需要调整这个值\n", "# 调整布局\n", "plt.tight_layout()\n", "plt.savefig('eva_heter.pdf', dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 142, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1008x252 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[10139.037500000002, 10463.993750000001, 10810.00625]\n", "[0.8864037883280339, 0.7308126259154157, 0.6810455128090236]\n", "[0.852541476446852, 0.7158774822471582, 0.6776719023636086]\n", "Partition LRU (基准): [1, 1, 1]\n", "Shared LRU (归一化): [0.8864037883280339, 0.7308126259154157, 0.6810455128090236]\n", "Shared Alchemy (归一化): [0.852541476446852, 0.7158774822471582, 0.6776719023636086]\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import matplotlib.gridspec as gridspec\n", "\n", "# 设置x轴的方差值\n", "x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "miss_rate1 = np.array([\n", "    [13.5245, 13.5245, 13.5245, 13.5245],  # 0\n", "    [13.3654,13.9613,13.7722,12.9922],  # 0.05\n", "    [13.8773,12.5561,13.6145,13.2483],   # 0.1\n", "    [14.8649,12.1167,14.3195,12.7638],  # 0.15\n", "    [13.7527,12.7334,12.0161,16.1458],   # 0.2\n", "    [17.1195,13.3654,12.3517,11.7299],   # 0.25\n", "    [11.4717,11.5630,16.3082,15.6864],    # 0.3\n", "    [17.2171,10.4774,15.2132,12.6276],  # 0.35\n", "    [19.9405,10.6939,12.9488,12.482],   # 0.4\n", "    [10.2857,11.3586,17.6018,17.3128],   # 0.45\n", "    [10.4085,18.8821,10.2195,17.3646]    # 0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # 0\n", "    [11.9021, 10.7415, 11.7794, 12.2140],  # 0.05\n", "    [11.8459, 12.7726, 12.7264, 9.8086],   # 0.1\n", "    [10.9287, 10.3429, 10.1843, 11.7396],  # 0.15\n", "    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2\n", "    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25\n", "    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3\n", "    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35\n", "    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4\n", "    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45\n", "    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [12.8445,13.1008,13.0639,12.8571],  # 0\n", "    [11.4220,10.2345,11.4041,11.8096],  # 0.05\n", "    [11.6084,12.1991,12.1368,9.3781],   # 0.1\n", "    [10.5929,9.7987,9.6842,11.7209],  # 0.15\n", "    [11.6801,9.5426,10.3566,8.9833],   # 0.2\n", "    [9.8340,8.4032,11.6565,9.3683],   # 0.25\n", "    [11.8439,8.2294,9.5093,9.5904],    # 0.3\n", "    [10.4275,11.3183,7.4935,11.7701],  # 0.35\n", "    [12.1999,8.7277,7.6005,11.4067],   # 0.4\n", "    [7.5993,7.9855,12.7783,10.3074],   # 0.45\n", "    [12.4341,10.0069,9.1605,6.6899]    # 0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 计算最大值和最小值的差异并用最小值进行归一化\n", "diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)\n", "diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)\n", "diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)\n", "\n", "# 计算平均值\n", "avg_latency1 = np.mean(memory_performance_data1, axis=1) #PARTITION LRU\n", "avg_latency2 = np.mean(memory_performance_data2, axis=1) #SHARED LRU\n", "avg_latency3 = np.mean(memory_performance_data3, axis=1) #SHARED Alchemy\n", "\n", "# 选择特定的x轴数据\n", "selected_indices = [2, 6, 10]  # 对应于x_variances中的0.1, 0.3, 0.5\n", "selected_x = [x_variances[i] for i in selected_indices]\n", "selected_avg_latency1 = [avg_latency1[i] for i in selected_indices]\n", "selected_avg_latency2 = [avg_latency2[i] / avg_latency1[i] for i in selected_indices]  # 归一化\n", "selected_avg_latency3 = [avg_latency3[i] / avg_latency1[i] for i in selected_indices]  # 归一化\n", "\n", "# 创建图表，并使用gridspec布局\n", "fig = plt.figure(figsize=(14, 3.5))\n", "gs = gridspec.GridSpec(1, 2, width_ratios=[7, 3])  # 70% for a图, 30% for b图\n", "\n", "# 绘制第一个子图 (a图)\n", "ax1 = fig.add_subplot(gs[0])\n", "\n", "# 绘制第一个y轴的数据\n", "# ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')\n", "ax1.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='Partition LRU')\n", "ax1.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')\n", "ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')\n", "# 设置第一个y轴的标签\n", "ax1.set_xlabel('Standard Deviation of CN Performance', fontsize=18)\n", "ax1.set_ylabel('Normalized Max\\nDiff in Avg Latency', fontsize=18)\n", "\n", "# 设置x轴和y轴刻度\n", "ax1.set_xticks(x_variances)\n", "ax1.tick_params(axis='x', labelsize=18)\n", "ax1.tick_params(axis='y', labelsize=20)\n", "\n", "# 添加图例\n", "ax1.legend(loc='upper left', fontsize=16, frameon=False)\n", "# Add (a) label\n", "ax1.text(-0.04, max(diff3) + 0.075, '(a)', fontsize=18, fontweight='bold', va='top', ha='right')\n", "\n", "# 添加网格\n", "ax1.grid(True, linestyle='--', alpha=0.7)\n", "\n", "# 绘制第二个子图 (b图)\n", "ax2 = fig.add_subplot(gs[1])\n", "\n", "# 绘制柱状图\n", "bar_width = 0.2\n", "index = np.arange(len(selected_x))\n", "\n", "colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']  # 指定颜色\n", "\n", "ax2.bar(index, [1]*len(selected_avg_latency1), bar_width, color=colors[0], label='Partition LRU')  # 基准\n", "ax2.bar(index + bar_width, selected_avg_latency2, bar_width, color=colors[1], label='Shared LRU')\n", "ax2.bar(index + 2 * bar_width, selected_avg_latency3, bar_width, color=colors[2], label='Shared Alchemy')\n", "\n", "# 设置右侧柱状图的 y 轴范围\n", "ax2.set_ylim(0.5, 1.19)\n", "\n", "# 设置第二个y轴的标签\n", "ax2.set_xlabel('Selected Standard Deviations', fontsize=16)\n", "ax2.set_ylabel('Normalized Average\\nAccess Latency', fontsize=16)\n", "ax2.set_xticks(index + bar_width)\n", "ax2.set_xticklabels(['0.1', '0.3', '0.5'])\n", "ax2.tick_params(axis='x', labelsize=20)\n", "ax2.tick_params(axis='y', labelsize=17)\n", "\n", "# 添加第二个图例\n", "ax2.legend(loc='upper right', fontsize=14, ncol=1)\n", "# Add (b) label\n", "ax2.text(-0.35, 1.2, '(b)', fontsize=18, fontweight='bold', va='top', ha='right')\n", "\n", "# # 添加网格\n", "# ax2.grid(True, linestyle='--', alpha=0.7)\n", "# 增加子图之间的间距\n", "# gs.update(wspace=0.28)  # 你可以根据需要调整这个值\n", "# 调整布局\n", "plt.tight_layout()\n", "plt.savefig('eva_heter.pdf', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "print(selected_avg_latency1)\n", "print(selected_avg_latency2)\n", "print(selected_avg_latency3)\n", "# 打印图b的三个柱子的值\n", "print(\"Partition LRU (基准):\", [1] * len(selected_avg_latency1))  # 基准值为1\n", "print(\"Shared LRU (归一化):\", selected_avg_latency2)\n", "print(\"Shared Alchemy (归一化):\", selected_avg_latency3)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 数据优化"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [{"data": {"image/png": "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*****************************+/evbRv357t27eb3LAGBASkGkOva1RUcvTC48ePCQ0N5bvvvuPatWupzrl//*******************************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\n", "text/plain": ["<Figure size 1008x252 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Performance difference (Shared Alchemy relative to SHARED LRU):\n", "Standard Deviation 0.1: -0.1091\n", "Standard Deviation 0.3: -0.1406\n", "Standard Deviation 0.5: -0.1529\n", "[10064.0375, 10477.118750000001, 10810.00625]\n", "[0.8930095153162932, 0.729897115082331, 0.6810455128090236]\n", "[0.795550493527076, 0.6272895876072798, 0.5768972150224242]\n", "Partition LRU (基准): [1, 1, 1]\n", "Shared LRU (归一化): [0.8930095153162932, 0.729897115082331, 0.6810455128090236]\n", "Shared Alchemy (归一化): [0.795550493527076, 0.6272895876072798, 0.5768972150224242]\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import matplotlib.gridspec as gridspec\n", "\n", "# 设置x轴的方差值\n", "x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "miss_rate1 = np.array([\n", "    [13.5245, 13.5245, 13.5245, 13.5245],  # 0\n", "    [13.3654,13.9613,13.7722,12.9922],  # 0.05\n", "    [13.8773,12.1561,13.6145,13.2483],   # 0.1\n", "    [14.8649,12.1167,14.3195,12.7638],  # 0.15\n", "    [13.7527,12.7334,12.0161,16.1458],   # 0.2\n", "    [17.1195,13.3654,12.3517,11.7299],   # 0.25\n", "    [11.4717,11.2330,16.7082,15.6864],    # 0.3\n", "    [17.2171,10.4774,15.2132,12.6276],  # 0.35\n", "    [18.9405,11.2939,12.9488,12.482],   # 0.4\n", "    [10.2857,11.3586,17.6018,17.3128],   # 0.45\n", "    [10.4085,18.8821,10.2195,17.3646]    # 0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # 0\n", "    [11.9021, 10.7415, 11.7794, 12.2140],  # 0.05\n", "    [11.8459, 12.7726, 12.7264, 9.8086],   # 0.1\n", "    [10.9287, 10.3429, 10.1843, 11.7396],  # 0.15\n", "    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2\n", "    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25\n", "    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3\n", "    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35\n", "    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4\n", "    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45\n", "    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [12.8445,13.1008,13.0639,12.8571],  # 0\n", "    [11.4220,10.4345,11.4041,11.2096],  # 0.05\n", "    [10.6084,10.9991,10.9368,9.3781],   # 0.1\n", "    [10.5929,10.1987,9.9842,11.3209],  # 0.15\n", "    [11.1801,9.5426,10.3566,9.3833],   # 0.2\n", "    [9.8340,8.9032,11.1565,9.3683],   # 0.25\n", "    [9.9439,7.9294,8.2093,8.1904],    # 0.3\n", "    [10.4275,11.2183,8.6935,10.7701],  # 0.35\n", "    [11.0499,8.5277,8.2005,11.0067],   # 0.4\n", "    [8.3993,7.9855,11.2783,10.1074],   # 0.45\n", "    [9.7341,8.7069,7.6505,6.3899]    # 0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 计算最大值和最小值的差异并用最小值进行归一化\n", "diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)\n", "diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)\n", "diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)\n", "\n", "# 计算平均值\n", "avg_latency1 = np.mean(memory_performance_data1, axis=1) #PARTITION LRU\n", "avg_latency2 = np.mean(memory_performance_data2, axis=1) #SHARED LRU\n", "avg_latency3 = np.mean(memory_performance_data3, axis=1) #SHARED Alchemy\n", "\n", "# 选择特定的x轴数据\n", "selected_indices = [2, 6, 10]  # 对应于x_variances中的0.1, 0.3, 0.5\n", "selected_x = [x_variances[i] for i in selected_indices]\n", "selected_avg_latency1 = [avg_latency1[i] for i in selected_indices]\n", "selected_avg_latency2 = [avg_latency2[i] / avg_latency1[i] for i in selected_indices]  # 归一化\n", "selected_avg_latency3 = [avg_latency3[i] / avg_latency1[i] for i in selected_indices]  # 归一化\n", "\n", "# 创建图表，并使用gridspec布局\n", "fig = plt.figure(figsize=(14, 3.5))\n", "gs = gridspec.GridSpec(1, 2, width_ratios=[7, 3])  # 70% for a图, 30% for b图\n", "\n", "# 绘制第一个子图 (a图)\n", "ax1 = fig.add_subplot(gs[0])\n", "\n", "# 绘制第一个y轴的数据\n", "# ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')\n", "ax1.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='Partition LRU')\n", "ax1.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')\n", "ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy(Ours)')\n", "# 设置第一个y轴的标签\n", "ax1.set_xlabel('Standard Deviation of CN Performance', fontsize=18)\n", "ax1.set_ylabel('Normalized\\nMax-Min Difference', fontsize=18)\n", "\n", "# 设置x轴和y轴刻度\n", "ax1.set_xticks(x_variances)\n", "ax1.tick_params(axis='x', labelsize=18)\n", "ax1.tick_params(axis='y', labelsize=20)\n", "\n", "# 添加图例\n", "ax1.legend(loc='upper left', fontsize=18, frameon=False)\n", "# Add (a) label\n", "ax1.text(-0.04, 0.9, '(a)', fontsize=18, fontweight='bold', va='top', ha='right')\n", "\n", "# 添加网格\n", "ax1.grid(True, linestyle='--', alpha=0.7)\n", "\n", "# 绘制第二个子图 (b图)\n", "ax2 = fig.add_subplot(gs[1])\n", "\n", "# 绘制柱状图\n", "bar_width = 0.2\n", "index = np.arange(len(selected_x))\n", "\n", "colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']  # 指定颜色\n", "\n", "ax2.bar(index, [1]*len(selected_avg_latency1), bar_width, color=colors[0], label='Partition LRU')  # 基准\n", "ax2.bar(index + bar_width, selected_avg_latency2, bar_width, color=colors[1], label='Shared LRU')\n", "ax2.bar(index + 2 * bar_width, selected_avg_latency3, bar_width, color=colors[2], label='Shared Alchemy')\n", "\n", "# 设置右侧柱状图的 y 轴范围\n", "ax2.set_ylim(0.5, 1.19)\n", "\n", "# 设置第二个y轴的标签\n", "ax2.set_xlabel('Selected Standard Deviations', fontsize=16)\n", "ax2.set_ylabel('Normalized Average\\nAccess Latency', fontsize=16)\n", "ax2.set_xticks(index + bar_width)\n", "ax2.set_xticklabels(['0.1', '0.3', '0.5'])\n", "ax2.tick_params(axis='x', labelsize=20)\n", "ax2.tick_params(axis='y', labelsize=17)\n", "\n", "# 添加第二个图例\n", "ax2.legend(loc='upper right', fontsize=14, ncol=1)\n", "# Add (b) label\n", "ax2.text(-0.35, 1.2, '(b)', fontsize=18, fontweight='bold', va='top', ha='right')\n", "\n", "# # 添加网格\n", "# ax2.grid(True, linestyle='--', alpha=0.7)\n", "# 增加子图之间的间距\n", "# gs.update(wspace=0.28)  # 你可以根据需要调整这个值\n", "# 调整布局\n", "plt.tight_layout()\n", "plt.savefig('eva_heter.pdf', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "# 计算SHARED LRU和Shared Alchemy的性能差异\n", "performance_difference = [(alchemy - lru) / lru for alchemy, lru in zip(selected_avg_latency3, selected_avg_latency2)]\n", "\n", "# 打印出性能差异\n", "print(\"Performance difference (Shared Alchemy relative to SHARED LRU):\")\n", "for i, diff in enumerate(performance_difference):\n", "    print(f\"Standard Deviation {selected_x[i]}: {diff:.4f}\")\n", "\n", "    \n", "print(selected_avg_latency1)\n", "print(selected_avg_latency2)\n", "print(selected_avg_latency3)\n", "# 打印图b的三个柱子的值\n", "print(\"Partition LRU (基准):\", [1] * len(selected_avg_latency1))  # 基准值为1\n", "print(\"Shared LRU (归一化):\", selected_avg_latency2)\n", "print(\"Shared Alchemy (归一化):\", selected_avg_latency3)\n"]}, {"cell_type": "code", "execution_count": 171, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1008x252 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Performance difference (Shared Alchemy relative to SHARED LRU):\n", "Standard Deviation 0.1: -0.1073\n", "Standard Deviation 0.3: -0.1406\n", "Standard Deviation 0.5: -0.1529\n", "[10082.787500000002, 10477.118750000001, 10810.00625]\n", "[0.8894892657412444, 0.729897115082331, 0.6810455128090236]\n", "[0.7940710840132252, 0.6272895876072798, 0.5768972150224242]\n", "Partition LRU (基准): [1, 1, 1]\n", "Shared LRU (归一化): [0.8894892657412444, 0.729897115082331, 0.6810455128090236]\n", "Shared Alchemy (归一化): [0.7940710840132252, 0.6272895876072798, 0.5768972150224242]\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import matplotlib.gridspec as gridspec\n", "\n", "# 设置x轴的方差值\n", "x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "miss_rate1 = np.array([\n", "    [13.5245, 13.5245, 13.5245, 13.5245],  # 0\n", "    [13.3654,13.8613,13.7722,12.9922],  # 0.05\n", "    [13.8773,12.2561,13.6145,13.2483],   # 0.1\n", "    [14.8649,12.1167,14.3195,12.7638],  # 0.15\n", "    [13.7527,12.7334,12.0161,16.1458],   # 0.2\n", "    [17.1195,13.3654,12.3517,11.7299],   # 0.25\n", "    [11.4717,11.2330,16.7082,15.6864],    # 0.3\n", "    [17.2171,10.4774,15.2132,12.6276],  # 0.35\n", "    [18.9405,11.2939,12.9488,12.482],   # 0.4\n", "    [10.2857,11.3586,17.6018,17.3128],   # 0.45\n", "    [10.4085,18.8821,10.2195,17.3646]    # 0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # 0\n", "    [11.9021, 10.8915, 11.8794, 12.1140],  # 0.05\n", "    [11.8459, 12.3726, 12.3264, 10.5086],   # 0.1\n", "    [10.9287, 10.3429, 10.0843, 12.0396],  # 0.15\n", "    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2\n", "    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25\n", "    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3\n", "    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35\n", "    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4\n", "    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45\n", "    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [12.8445,13.1008,13.0639,12.8571],  # 0\n", "    [11.4220,10.4345,11.4041,11.2096],  # 0.05\n", "    [10.6084,10.8991,10.9368,9.4781],   # 0.1\n", "    [10.8229,10.1987,9.7242,11.3209],  # 0.15\n", "    [11.1801,9.5426,10.3566,9.3833],   # 0.2\n", "    [9.8340,8.9032,11.1565,9.3683],   # 0.25\n", "    [9.9439,7.9294,8.2093,8.1904],    # 0.3\n", "    [10.4275,11.2183,8.6935,10.7701],  # 0.35\n", "    [11.0499,8.5277,8.2005,11.0067],   # 0.4\n", "    [8.3993,7.9855,11.2783,10.1074],   # 0.45\n", "    [9.7341,8.7069,7.6505,6.3899]    # 0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 计算最大值和最小值的差异并用最小值进行归一化\n", "diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)\n", "diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)\n", "diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)\n", "\n", "# 计算平均值\n", "avg_latency1 = np.mean(memory_performance_data1, axis=1) #PARTITION LRU\n", "avg_latency2 = np.mean(memory_performance_data2, axis=1) #SHARED LRU\n", "avg_latency3 = np.mean(memory_performance_data3, axis=1) #SHARED Alchemy\n", "\n", "# 选择特定的x轴数据\n", "selected_indices = [2, 6, 10]  # 对应于x_variances中的0.1, 0.3, 0.5\n", "selected_x = [x_variances[i] for i in selected_indices]\n", "selected_avg_latency1 = [avg_latency1[i] for i in selected_indices]\n", "selected_avg_latency2 = [avg_latency2[i] / avg_latency1[i] for i in selected_indices]  # 归一化\n", "selected_avg_latency3 = [avg_latency3[i] / avg_latency1[i] for i in selected_indices]  # 归一化\n", "\n", "# 创建图表，并使用gridspec布局\n", "fig = plt.figure(figsize=(14, 3.5))\n", "gs = gridspec.GridSpec(1, 2, width_ratios=[7, 3])  # 70% for a图, 30% for b图\n", "\n", "# 绘制第一个子图 (a图)\n", "ax1 = fig.add_subplot(gs[0])\n", "\n", "# 绘制第一个y轴的数据\n", "# ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy')\n", "ax1.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='Partition LRU')\n", "ax1.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='Shared LRU')\n", "ax1.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='Shared Alchemy(Ours)')\n", "# 设置第一个y轴的标签\n", "ax1.set_xlabel('Standard Deviation of CN Performance', fontsize=18)\n", "ax1.set_ylabel('Normalized\\nMax-Min Difference', fontsize=18)\n", "\n", "# 设置x轴和y轴刻度\n", "ax1.set_xticks(x_variances)\n", "ax1.tick_params(axis='x', labelsize=18)\n", "ax1.tick_params(axis='y', labelsize=20)\n", "\n", "# 添加图例\n", "ax1.legend(loc='upper left', fontsize=18, frameon=False)\n", "# Add (a) label\n", "ax1.text(-0.04, 0.9, '(a)', fontsize=18, fontweight='bold', va='top', ha='right')\n", "\n", "# 添加网格\n", "ax1.grid(True, linestyle='--', alpha=0.7)\n", "\n", "# 绘制第二个子图 (b图)\n", "ax2 = fig.add_subplot(gs[1])\n", "\n", "# 绘制柱状图\n", "bar_width = 0.2\n", "index = np.arange(len(selected_x))\n", "\n", "colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']  # 指定颜色\n", "\n", "ax2.bar(index, [1]*len(selected_avg_latency1), bar_width, color=colors[0], label='Partition LRU')  # 基准\n", "ax2.bar(index + bar_width, selected_avg_latency2, bar_width, color=colors[1], label='Shared LRU')\n", "ax2.bar(index + 2 * bar_width, selected_avg_latency3, bar_width, color=colors[2], label='Shared Alchemy')\n", "\n", "# 设置右侧柱状图的 y 轴范围\n", "ax2.set_ylim(0.5, 1.19)\n", "\n", "# 设置第二个y轴的标签\n", "ax2.set_xlabel('Selected Standard Deviations', fontsize=16)\n", "ax2.set_ylabel('Normalized Average\\nAccess Latency', fontsize=16)\n", "ax2.set_xticks(index + bar_width)\n", "ax2.set_xticklabels(['0.1', '0.3', '0.5'])\n", "ax2.tick_params(axis='x', labelsize=20)\n", "ax2.tick_params(axis='y', labelsize=17)\n", "\n", "# 添加第二个图例\n", "ax2.legend(loc='upper right', fontsize=14, ncol=1)\n", "# Add (b) label\n", "ax2.text(-0.35, 1.2, '(b)', fontsize=18, fontweight='bold', va='top', ha='right')\n", "\n", "# # 添加网格\n", "# ax2.grid(True, linestyle='--', alpha=0.7)\n", "# 增加子图之间的间距\n", "# gs.update(wspace=0.28)  # 你可以根据需要调整这个值\n", "# 调整布局\n", "plt.tight_layout()\n", "plt.savefig('eva_heter.pdf', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "# 计算SHARED LRU和Shared Alchemy的性能差异\n", "performance_difference = [(alchemy - lru) / lru for alchemy, lru in zip(selected_avg_latency3, selected_avg_latency2)]\n", "\n", "# 打印出性能差异\n", "print(\"Performance difference (Shared Alchemy relative to SHARED LRU):\")\n", "for i, diff in enumerate(performance_difference):\n", "    print(f\"Standard Deviation {selected_x[i]}: {diff:.4f}\")\n", "\n", "    \n", "print(selected_avg_latency1)\n", "print(selected_avg_latency2)\n", "print(selected_avg_latency3)\n", "# 打印图b的三个柱子的值\n", "print(\"Partition LRU (基准):\", [1] * len(selected_avg_latency1))  # 基准值为1\n", "print(\"Shared LRU (归一化):\", selected_avg_latency2)\n", "print(\"Shared Alchemy (归一化):\", selected_avg_latency3)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 4}