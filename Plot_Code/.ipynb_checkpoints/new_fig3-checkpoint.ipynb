{"cells": [{"cell_type": "code", "execution_count": 57, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "from matplotlib.patches import Patch\n", "\n", "# 设置x轴的方差值\n", "x_variances = [0.1, 0.2, 0.3, 0.4, 0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "miss_rate1 = np.array([\n", "    [11.8459, 12.7726, 12.7264, 9.8086],  # 对应方差0.1\n", "    [11.7236, 9.9120, 10.6835, 9.3754],  # 对应方差0.2\n", "    [11.6728, 8.6818, 9.8781, 9.7738],  # 对应方差0.3\n", "    [11.9803, 9.0219, 8.0607, 11.3268],  # 对应方差0.4\n", "    [11.9295, 10.0929, 9.4055, 7.0580]   # 对应方差0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [17.5579, 17.5259, 17.7995, 15.9576, 17.2215, 16.2626, 15.8249, 17.9036],  # 对应方差0.1\n", "    [17.8930, 14.2678, 15.2018, 16.3045, 15.7211, 14.4207, 14.7119, 14.6715],  # 对应方差0.2\n", "    [16.1865, 14.8095, 18.4399, 12.7695, 13.9413, 14.2870, 17.9456, 14.3626],  # 对应方差0.3\n", "    [15.3610, 16.2109, 15.3014, 13.2520, 14.4190, 13.2229, 17.2911, 18.7687],  # 对应方差0.4\n", "    [18.7702, 15.9677, 19.7713, 12.5015, 11.2632, 15.6306, 12.1210, 15.4327]   # 对应方差0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [6.3414, 6.9206],  # 对应方差0.1\n", "    [6.6264, 5.5598],  # 对应方差0.2\n", "    [6.3439, 5.0775],  # 对应方差0.3\n", "    [4.9454, 6.5212],  # 对应方差0.4\n", "    [6.7635, 4.9765]   # 对应方差0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 创建箱线图\n", "plt.figure(figsize=(12, 6))\n", "\n", "# 绘制第一组箱线图\n", "plt.boxplot(memory_performance_data1.T, positions=np.arange(1, len(x_variances) + 1), widths=0.2, patch_artist=True,\n", "            boxprops=dict(facecolor='lightblue', color='blue'),\n", "            medianprops=dict(color='blue'),\n", "            showfliers=False, whis=[0, 100])\n", "\n", "# 绘制第二组箱线图\n", "plt.boxplot(memory_performance_data2.T, positions=np.arange(1, len(x_variances) + 1) + 0.2, widths=0.2, patch_artist=True,\n", "            boxprops=dict(facecolor='lightgreen', color='green'),\n", "            medianprops=dict(color='green'),\n", "            showfliers=False)\n", "\n", "# 绘制第三组箱线图\n", "plt.boxplot(memory_performance_data3.T, positions=np.arange(1, len(x_variances) + 1) - 0.2, widths=0.2, patch_artist=True,\n", "            boxprops=dict(facecolor='lightcoral', color='red'),\n", "            medianprops=dict(color='red'),\n", "            showfliers=False)\n", "\n", "# 设置图表标签和标题\n", "plt.xlabel('Standard Deviation')\n", "plt.ylabel('Lantency Per Mem Access')\n", "# plt.title('Boxplot of Memory variance vs <PERSON><PERSON><PERSON>')\n", "\n", "# 设置x轴刻度\n", "plt.xticks(range(1, len(x_variances) + 1), x_variances)\n", "\n", "# 创建图例句柄\n", "handles = [\n", "    Patch(facecolor='lightcoral', edgecolor='red', label='2 CNs'),\n", "    Patch(facecolor='lightblue', edgecolor='blue', label='4 CNs'),\n", "    Patch(facecolor='lightgreen', edgecolor='green', label='8 CNs')\n", "#     Patch(facecolor='lightcoral', edgecolor='red', label='Data Set 3')\n", "]\n", "\n", "# 添加图例\n", "plt.legend(handles=handles, loc='upper right')\n", "\n", "# 显示图表\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "from matplotlib.patches import Patch\n", "\n", "# 设置x轴的方差值，包括方差为0的情况\n", "x_variances = [0, 0.1, 0.2, 0.3, 0.4, 0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "# 添加方差为0的数据，假设miss rate为一个固定值\n", "miss_rate1 = np.array([\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # 对应方差0\n", "    [11.8459, 12.7726, 12.7264, 9.8086],  # 对应方差0.1\n", "    [11.7236, 9.9120, 10.6835, 9.3754],  # 对应方差0.2\n", "    [11.6728, 8.6818, 9.8781, 9.7738],  # 对应方差0.3\n", "    [11.9803, 9.0219, 8.0607, 11.3268],  # 对应方差0.4\n", "    [11.9295, 10.0929, 9.4055, 7.0580]   # 对应方差0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [15.0, 15.0, 15.0, 15.0, 15.0, 15.0, 15.0, 15.0],  # 对应方差0\n", "    [17.5579, 17.5259, 17.7995, 15.9576, 17.2215, 16.2626, 15.8249, 17.9036],  # 对应方差0.1\n", "    [17.8930, 14.2678, 15.2018, 16.3045, 15.7211, 14.4207, 14.7119, 14.6715],  # 对应方差0.2\n", "    [16.1865, 14.8095, 18.4399, 12.7695, 13.9413, 14.2870, 17.9456, 14.3626],  # 对应方差0.3\n", "    [15.3610, 16.2109, 15.3014, 13.2520, 14.4190, 13.2229, 17.2911, 18.7687],  # 对应方差0.4\n", "    [18.7702, 15.9677, 19.7713, 12.5015, 11.2632, 15.6306, 12.1210, 15.4327]   # 对应方差0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [5.5, 5.5],  # 对应方差0\n", "    [6.3414, 6.9206],  # 对应方差0.1\n", "    [6.6264, 5.5598],  # 对应方差0.2\n", "    [6.3439, 5.0775],  # 对应方差0.3\n", "    [4.9454, 6.5212],  # 对应方差0.4\n", "    [6.7635, 4.9765]   # 对应方差0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 归一化函数\n", "def normalize(data):\n", "    data_min = np.min(data)\n", "    data_max = np.max(data)\n", "    return (data - data_min) / (data_max - data_min)\n", "\n", "# 对数据进行归一化处理\n", "normalized_data1 = normalize(memory_performance_data1)\n", "normalized_data2 = normalize(memory_performance_data2)\n", "normalized_data3 = normalize(memory_performance_data3)\n", "\n", "# 创建箱线图\n", "plt.figure(figsize=(12, 6))\n", "\n", "# 绘制第一组箱线图\n", "plt.boxplot(normalized_data1.T, positions=np.arange(1, len(x_variances) + 1), widths=0.2, patch_artist=True,\n", "            boxprops=dict(facecolor='lightblue', color='blue'),\n", "            medianprops=dict(color='blue'),\n", "            showfliers=False, whis=[0, 100])\n", "\n", "# 绘制第二组箱线图\n", "plt.boxplot(normalized_data2.T, positions=np.arange(1, len(x_variances) + 1) + 0.2, widths=0.2, patch_artist=True,\n", "            boxprops=dict(facecolor='lightgreen', color='green'),\n", "            medianprops=dict(color='green'),\n", "            showfliers=False)\n", "\n", "# 绘制第三组箱线图\n", "plt.boxplot(normalized_data3.T, positions=np.arange(1, len(x_variances) + 1) - 0.2, widths=0.2, patch_artist=True,\n", "            boxprops=dict(facecolor='lightcoral', color='red'),\n", "            medianprops=dict(color='red'),\n", "            showfliers=False)\n", "\n", "# 设置图表标签和标题\n", "plt.xlabel('Standard Deviation')\n", "plt.ylabel('Normalized Latency Per Mem Access')\n", "plt.title('Normalized Boxplot of Memory Performance')\n", "\n", "# 设置x轴刻度\n", "plt.xticks(range(1, len(x_variances) + 1), x_variances)\n", "\n", "# 创建图例句柄\n", "handles = [\n", "    Patch(facecolor='lightcoral', edgecolor='red', label='2 CNs'),\n", "    Patch(facecolor='lightblue', edgecolor='blue', label='4 CNs'),\n", "    Patch(facecolor='lightgreen', edgecolor='green', label='8 CNs')\n", "]\n", "\n", "# 添加图例\n", "plt.legend(handles=handles, loc='upper right')\n", "\n", "# 显示图表\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "from matplotlib.patches import Patch\n", "\n", "# 设置x轴的方差值\n", "x_variances = [0.1, 0.2, 0.3, 0.4, 0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "miss_rate1 = np.array([\n", "    [11.8459, 12.7726, 12.7264, 9.8086],  # 对应方差0.1\n", "    [11.7236, 9.9120, 10.6835, 9.3754],  # 对应方差0.2\n", "    [11.6728, 8.6818, 9.8781, 9.7738],  # 对应方差0.3\n", "    [11.9803, 9.0219, 8.0607, 11.3268],  # 对应方差0.4\n", "    [11.9295, 10.0929, 9.4055, 7.0580]   # 对应方差0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [17.5579, 17.5259, 17.7995, 15.9576, 17.2215, 16.2626, 15.8249, 17.9036],  # 对应方差0.1\n", "    [17.8930, 14.2678, 15.2018, 16.3045, 15.7211, 14.4207, 14.7119, 14.6715],  # 对应方差0.2\n", "    [16.1865, 14.8095, 18.4399, 12.7695, 13.9413, 14.2870, 17.9456, 14.3626],  # 对应方差0.3\n", "    [15.3610, 16.2109, 15.3014, 13.2520, 14.4190, 13.2229, 17.2911, 18.7687],  # 对应方差0.4\n", "    [18.7702, 15.9677, 19.7713, 12.5015, 11.2632, 15.6306, 12.1210, 15.4327]   # 对应方差0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [6.3414, 6.9206],  # 对应方差0.1\n", "    [6.6264, 5.5598],  # 对应方差0.2\n", "    [6.3439, 5.0775],  # 对应方差0.3\n", "    [4.9454, 6.5212],  # 对应方差0.4\n", "    [6.7635, 4.9765]   # 对应方差0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 计算最大值和最小值的差异\n", "diff1 = np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)\n", "diff2 = np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)\n", "diff3 = np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)\n", "\n", "# 创建折线图\n", "plt.figure(figsize=(12, 6))\n", "\n", "plt.plot(x_variances, diff1, marker='o', color='blue', label='4 CNs')\n", "plt.plot(x_variances, diff2, marker='o', color='green', label='8 CNs')\n", "plt.plot(x_variances, diff3, marker='o', color='red', label='2 CNs')\n", "\n", "# 设置图表标签和标题\n", "plt.xlabel('Standard Deviation')\n", "plt.ylabel('Latency Difference')\n", "plt.title('Latency Difference vs Variance')\n", "\n", "# 设置x轴刻度\n", "plt.xticks(x_variances)\n", "\n", "# 添加图例\n", "plt.legend(loc='upper right')\n", "\n", "# 显示图表\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "from matplotlib.patches import Patch\n", "\n", "# 设置x轴的方差值\n", "x_variances = [0,0.05,0.1,0.15, 0.2,0.25, 0.3,0.35, 0.4, 0.45,0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "miss_rate1 = np.array([\n", "    [13.4913, 13.7627, 13.7659, 13.4231], # 0\n", "    [11.9021, 10.7415, 11.7794, 12.2140], # 0.05\n", "    [11.8459, 12.7726, 12.7264, 9.8086],  # 对应方差0.1\n", "    [10.9287, 10.3429, 10.1843, 11.7396],  # 对应方差0.15\n", "    [11.7236, 9.9120, 10.6835, 9.3754],  # 对应方差0.2\n", "    [10.0607, 8.8084, 11.5768, 9.6661],  # 对应方差0.25\n", "    [11.6728, 8.6818, 9.8781, 9.7738],  # 对应方差0.3\n", "    [10.8048, 11.5376, 7.9421, 11.8366],  # 对应方差0.35\n", "    [11.9803, 9.0219, 8.0607, 11.3268],  # 对应方差0.4\n", "    [7.9913, 8.2667, 12.2953, 10.2314],  # 对应方差0.45\n", "    [11.9295, 10.0929, 9.4055, 7.0580]   # 对应方差0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [19.1187, 18.9768, 18.9106, 18.8871, 18.9019, 18.8982, 19.0079, 19.1257],  # 对应方差0\n", "    [17.9947, 17.2717, 17.6305, 17.5491, 17.3076, 16.4905, 18.0446, 17.5474],  # 对应方差0.05\n", "    [17.5579, 17.5259, 17.7995, 15.9576, 17.2215, 16.2626, 15.8249, 17.9036],  # 对应方差0.1\n", "    [14.1713, 18.2865, 17.3710, 14.5536, 16.1206, 16.3012, 15.7976, 18.4688],  # 对应方差0.15\n", "    [17.8930, 14.2678, 15.2018, 16.3045, 15.7211, 14.4207, 14.7119, 14.6715],  # 对应方差0.2\n", "    [14.3178, 14.4832, 15.1547, 13.5613, 14.9768, 14.2836, 17.8918, 17.9221],  # 对应方差0.25\n", "    [16.1865, 14.8095, 18.4399, 12.7695, 13.9413, 14.2870, 17.9456, 14.3626],  # 对应方差0.3\n", "    [14.9759, 14.6699, 18.7310, 17.1253, 17.8372, 14.4660, 12.7300, 12.4321],  # 对应方差0.35\n", "    [15.3610, 16.2109, 15.3014, 13.2520, 14.4190, 13.2229, 17.2911, 18.7687],  # 对应方差0.4\n", "    [16.0963, 11.7326, 14.3040, 13.7140, 16.8469, 20.6696, 14.3301, 11.4264],  # 对应方差0.45\n", "    [18.7702, 15.9677, 19.7713, 12.5015, 11.2632, 15.6306, 12.1210, 15.4327]   # 对应方差0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [7.8420, 7.8260],#0\n", "    [6.6509, 6.6708],#0.05\n", "    [6.3414, 6.9206],  # 对应方差0.1\n", "    [6.0257, 6.8526],  # 对应方差0.15\n", "    [6.6264, 5.5598],  # 对应方差0.2\n", "    [6.9515, 5.5856],  # 对应方差0.25\n", "    [6.3439, 5.0775],  # 对应方差0.3\n", "    [6.7838, 5.1753],  # 对应方差0.35\n", "    [4.9454, 6.5212],  # 对应方差0.4\n", "    [6.5963, 4.8432],  # 对应方差0.45\n", "    [6.7635, 4.9765]   # 对应方差0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 计算最大值和最小值的差异\n", "diff1 = np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)\n", "diff2 = np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)\n", "diff3 = np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)\n", "\n", "# 归一化处理\n", "all_diffs = np.concatenate([diff1, diff2, diff3])\n", "min_diff = np.min(all_diffs)\n", "max_diff = np.max(all_diffs)\n", "\n", "normalized_diff1 = (diff1 - min_diff) / (max_diff - min_diff)\n", "normalized_diff2 = (diff2 - min_diff) / (max_diff - min_diff)\n", "normalized_diff3 = (diff3 - min_diff) / (max_diff - min_diff)\n", "\n", "# 创建折线图\n", "plt.figure(figsize=(10, 4))\n", "\n", "plt.plot(x_variances, normalized_diff1, marker='o', color='blue', label='4 CNs')\n", "plt.plot(x_variances, normalized_diff2, marker='o', color='green', label='8 CNs')\n", "plt.plot(x_variances, normalized_diff3, marker='o', color='red', label='2 CNs')\n", "\n", "# 设置图表标签和标题\n", "plt.xlabel('Standard Deviation')\n", "plt.ylabel('Normalized Latency Difference')\n", "plt.title('Latency Difference')\n", "\n", "# 设置x轴刻度\n", "plt.xticks(x_variances)\n", "\n", "# 添加图例\n", "plt.legend(loc='upper left')\n", "\n", "# 显示图表\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "from matplotlib.patches import Patch\n", "\n", "# 设置x轴的方差值\n", "x_variances = [0,0.05,0.1,0.15, 0.2,0.25, 0.3,0.35, 0.4, 0.45,0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "miss_rate1 = np.array([\n", "    [13.4913, 13.7627, 13.7659, 13.4231], # 0\n", "    [11.9021, 10.7415, 11.7794, 12.2140], # 0.05\n", "    [11.8459, 12.7726, 12.7264, 9.8086],  # 对应方差0.1\n", "    [10.9287, 10.3429, 10.1843, 11.7396],  # 对应方差0.15\n", "    [11.7236, 9.9120, 10.6835, 9.3754],  # 对应方差0.2\n", "    [10.0607, 8.8084, 11.5768, 9.6661],  # 对应方差0.25\n", "    [11.6728, 8.6818, 9.8781, 9.7738],  # 对应方差0.3\n", "    [10.8048, 11.5376, 7.9421, 11.8366],  # 对应方差0.35\n", "    [11.9803, 9.0219, 8.0607, 11.3268],  # 对应方差0.4\n", "    [7.9913, 8.2667, 12.2953, 10.2314],  # 对应方差0.45\n", "    [11.9295, 10.0929, 9.4055, 7.0580]   # 对应方差0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [19.1187, 18.9768, 18.9106, 18.8871, 18.9019, 18.8982, 19.0079, 19.1257],  # 对应方差0\n", "    [17.9947, 17.2717, 17.6305, 17.5491, 17.3076, 16.4905, 18.0446, 17.5474],  # 对应方差0.05\n", "    [17.5579, 17.5259, 17.7995, 15.9576, 17.2215, 16.2626, 15.8249, 17.9036],  # 对应方差0.1\n", "    [14.1713, 18.2865, 17.3710, 14.5536, 16.1206, 16.3012, 15.7976, 18.4688],  # 对应方差0.15\n", "    [17.8930, 14.2678, 15.2018, 16.3045, 15.7211, 14.4207, 14.7119, 14.6715],  # 对应方差0.2\n", "    [14.3178, 14.4832, 15.1547, 13.5613, 14.9768, 14.2836, 17.8918, 17.9221],  # 对应方差0.25\n", "    [16.1865, 14.8095, 18.4399, 12.7695, 13.9413, 14.2870, 17.9456, 14.3626],  # 对应方差0.3\n", "    [14.9759, 14.6699, 18.7310, 17.1253, 17.8372, 14.4660, 12.7300, 12.4321],  # 对应方差0.35\n", "    [15.3610, 16.2109, 15.3014, 13.2520, 14.4190, 13.2229, 17.2911, 18.7687],  # 对应方差0.4\n", "    [16.0963, 11.7326, 14.3040, 13.7140, 16.8469, 20.6696, 14.3301, 11.4264],  # 对应方差0.45\n", "    [18.7702, 15.9677, 19.7713, 12.5015, 11.2632, 15.6306, 12.1210, 15.4327]   # 对应方差0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [7.8420, 7.8260],#0\n", "    [6.6509, 6.6708],#0.05\n", "    [6.3414, 6.9206],  # 对应方差0.1\n", "    [6.0257, 6.8526],  # 对应方差0.15\n", "    [6.6264, 5.5598],  # 对应方差0.2\n", "    [6.9515, 5.5856],  # 对应方差0.25\n", "    [6.3439, 5.0775],  # 对应方差0.3\n", "    [6.7838, 5.1753],  # 对应方差0.35\n", "    [4.9454, 6.5212],  # 对应方差0.4\n", "    [6.5963, 4.8432],  # 对应方差0.45\n", "    [6.7635, 4.9765]   # 对应方差0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 计算最大值和最小值的差异\n", "diff1 = np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)\n", "diff2 = np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)\n", "diff3 = np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)\n", "\n", "# 归一化处理\n", "all_diffs = np.concatenate([diff1, diff2, diff3])\n", "min_diff = np.min(all_diffs)\n", "max_diff = np.max(all_diffs)\n", "\n", "normalized_diff1 = (diff1 - min_diff) / (max_diff - min_diff)\n", "normalized_diff2 = (diff2 - min_diff) / (max_diff - min_diff)\n", "normalized_diff3 = (diff3 - min_diff) / (max_diff - min_diff)\n", "\n", "# 创建折线图\n", "plt.figure(figsize=(10, 3))\n", "\n", "plt.plot(x_variances, normalized_diff3, marker='o', color='red', label='2 CNs')\n", "plt.plot(x_variances, normalized_diff1, marker='o', color='blue', label='4 CNs')\n", "plt.plot(x_variances, normalized_diff2, marker='o', color='green', label='8 CNs')\n", "\n", "\n", "# 设置图表标签和标题\n", "plt.xlabel('Standard Deviation of CN Performance', fontsize=16)\n", "plt.ylabel('Normalized Max-Min\\nDiff of Avg Lantency', fontsize=16)\n", "# plt.title('Latency Variations', fontsize=16)\n", "\n", "# 设置x轴刻度\n", "plt.xticks(x_variances, fontsize=16)\n", "plt.yticks(fontsize=16)  # 设置y轴刻度的字体大小\n", "# 添加图例\n", "plt.legend(loc='upper left', fontsize=15)\n", "\n", "# 显示图表\n", "plt.grid(True)\n", "# plt.subplots_adjust(left=0.065, right=1,top=0.975, bottom=0.135)\n", "# plt.subplots_adjust(left=0.0785, right=0.985, top=0.98, bottom=0.15, wspace=0.3)\n", "plt.savefig('latency_difference_plot.pdf', dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 设置x轴的方差值\n", "x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "miss_rate1 = np.array([\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # 0\n", "    [11.9021, 10.7415, 11.7794, 12.2140],  # 0.05\n", "    [11.8459, 12.7726, 12.7264, 9.8086],   # 0.1\n", "    [10.9287, 10.3429, 10.1843, 11.7396],  # 0.15\n", "    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2\n", "    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25\n", "    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3\n", "    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35\n", "    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4\n", "    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45\n", "    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [19.1187, 18.9768, 18.9106, 18.8871, 18.9019, 18.8982, 19.0079, 19.1257],  # 0\n", "    [17.9947, 17.2717, 17.6305, 17.5491, 17.3076, 16.4905, 18.0446, 17.5474],  # 0.05\n", "    [17.5579, 17.5259, 17.7995, 15.9576, 17.2215, 16.2626, 15.8249, 17.9036],  # 0.1\n", "    [14.1713, 18.2865, 17.3710, 14.5536, 16.1206, 16.3012, 15.7976, 18.4688],  # 0.15\n", "    [17.8930, 14.2678, 15.2018, 16.3045, 15.7211, 14.4207, 14.7119, 14.6715],  # 0.2\n", "    [14.3178, 14.4832, 15.1547, 13.5613, 14.9768, 14.2836, 17.8918, 17.9221],  # 0.25\n", "    [16.1865, 14.8095, 18.4399, 12.7695, 13.9413, 14.2870, 17.9456, 14.3626],  # 0.3\n", "    [14.9759, 14.6699, 18.7310, 17.1253, 17.8372, 14.4660, 12.7300, 12.4321],  # 0.35\n", "    [15.3610, 16.2109, 15.3014, 13.2520, 14.4190, 13.2229, 17.2911, 18.7687],  # 0.4\n", "    [16.0963, 11.7326, 14.3040, 13.7140, 16.8469, 20.6696, 14.3301, 11.4264],  # 0.45\n", "    [18.7702, 15.9677, 19.7713, 12.5015, 11.2632, 15.6306, 12.1210, 15.4327]   # 0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [7.8420, 7.8260],  # 0\n", "    [6.6509, 6.6708],  # 0.05\n", "    [6.3414, 6.9206],  # 0.1\n", "    [6.0257, 6.8526],  # 0.15\n", "    [6.6264, 5.5598],  # 0.2\n", "    [6.9515, 5.5856],  # 0.25\n", "    [6.3439, 5.0775],  # 0.3\n", "    [6.7838, 5.1753],  # 0.35\n", "    [4.9454, 6.5212],  # 0.4\n", "    [6.5963, 4.8432],  # 0.45\n", "    [6.7635, 4.9765]   # 0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 计算最大值和最小值的差异并用最小值进行归一化\n", "diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)\n", "diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)\n", "diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)\n", "\n", "# 创建折线图\n", "plt.figure(figsize=(10, 3))\n", "\n", "plt.plot(x_variances, diff3, marker='o', color='red', label='2 CNs')\n", "plt.plot(x_variances, diff1, marker='o', color='blue', label='4 CNs')\n", "plt.plot(x_variances, diff2, marker='o', color='green', label='8 CNs')\n", "\n", "# 设置图表标签和标题\n", "plt.xlabel('Standard Deviation of CN Performance', fontsize=16)\n", "plt.ylabel('Normalized Max\\nDiff in Avg Latency', fontsize=16)\n", "\n", "# 设置x轴刻度\n", "plt.xticks(x_variances, fontsize=16)\n", "plt.yticks(fontsize=16)  # 设置y轴刻度的字体大小\n", "\n", "# 添加图例\n", "plt.legend(loc='upper left', fontsize=15)\n", "\n", "# 显示图表\n", "plt.grid(True)\n", "plt.savefig('latency_difference_plot_normalized_by_min.pdf', dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 设置x轴的方差值\n", "x_variances = [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 使用NumPy数组来存储数据以便进行计算\n", "miss_rate1 = np.array([\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # 0\n", "    [11.9021, 10.7415, 11.7794, 12.2140],  # 0.05\n", "    [11.8459, 12.7726, 12.7264, 9.8086],   # 0.1\n", "    [10.9287, 10.3429, 10.1843, 11.7396],  # 0.15\n", "    [11.7236, 9.9120, 10.6835, 9.3754],   # 0.2\n", "    [10.0607, 8.8084, 11.5768, 9.6661],   # 0.25\n", "    [11.6728, 8.6818, 9.8781, 9.7738],    # 0.3\n", "    [10.8048, 11.5376, 7.9421, 11.8366],  # 0.35\n", "    [11.9803, 9.0219, 8.0607, 11.3268],   # 0.4\n", "    [7.9913, 8.2667, 12.2953, 10.2314],   # 0.45\n", "    [11.9295, 10.0929, 9.4055, 7.0580]    # 0.5\n", "])\n", "\n", "miss_rate2 = np.array([\n", "    [19.1187, 18.9768, 18.9106, 18.8871, 18.9019, 18.8982, 19.0079, 19.1257],  # 0\n", "    [17.9947, 17.2717, 17.6305, 17.5491, 17.3076, 16.4905, 18.0446, 17.5474],  # 0.05\n", "    [17.5579, 17.5259, 17.7995, 15.9576, 17.2215, 16.2626, 15.8249, 17.9036],  # 0.1\n", "    [14.1713, 18.2865, 17.3710, 14.5536, 16.1206, 16.3012, 15.7976, 18.4688],  # 0.15\n", "    [17.8930, 14.2678, 15.2018, 16.3045, 15.7211, 14.4207, 14.7119, 14.6715],  # 0.2\n", "    [14.3178, 14.4832, 15.1547, 13.5613, 14.9768, 14.2836, 17.8918, 17.9221],  # 0.25\n", "    [16.1865, 14.8095, 18.4399, 12.7695, 13.9413, 14.2870, 17.9456, 14.3626],  # 0.3\n", "    [14.9759, 14.6699, 18.7310, 17.1253, 17.8372, 14.4660, 12.7300, 12.4321],  # 0.35\n", "    [15.3610, 16.2109, 15.3014, 13.2520, 14.4190, 13.2229, 17.2911, 18.7687],  # 0.4\n", "    [16.0963, 11.7326, 14.3040, 13.7140, 16.8469, 20.6696, 14.3301, 11.4264],  # 0.45\n", "    [18.7702, 15.9677, 19.7713, 12.5015, 11.2632, 15.6306, 12.1210, 15.4327]   # 0.5\n", "])\n", "\n", "miss_rate3 = np.array([\n", "    [7.8420, 7.8260],  # 0\n", "    [6.6509, 6.6708],  # 0.05\n", "    [6.3414, 6.9206],  # 0.1\n", "    [6.0257, 6.8526],  # 0.15\n", "    [6.6264, 5.5598],  # 0.2\n", "    [6.9515, 5.5856],  # 0.25\n", "    [6.3439, 5.0775],  # 0.3\n", "    [6.7838, 5.1753],  # 0.35\n", "    [4.9454, 6.5212],  # 0.4\n", "    [6.5963, 4.8432],  # 0.45\n", "    [6.7635, 4.9765]   # 0.5\n", "])\n", "\n", "# 计算memory_performance_data\n", "memory_performance_data1 = (miss_rate1 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate1 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data2 = (miss_rate2 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate2 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "memory_performance_data3 = (miss_rate3 / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) +\n", "                            (1 - miss_rate3 / 100) * (DDR_DELAY + CXL_DELAY))\n", "\n", "# 计算最大值和最小值的差异并用最小值进行归一化\n", "diff1 = (np.max(memory_performance_data1, axis=1) - np.min(memory_performance_data1, axis=1)) / np.min(memory_performance_data1, axis=1)\n", "diff2 = (np.max(memory_performance_data2, axis=1) - np.min(memory_performance_data2, axis=1)) / np.min(memory_performance_data2, axis=1)\n", "diff3 = (np.max(memory_performance_data3, axis=1) - np.min(memory_performance_data3, axis=1)) / np.min(memory_performance_data3, axis=1)\n", "\n", "# 创建折线图\n", "plt.figure(figsize=(10, 3))  # 调整图表大小\n", "\n", "plt.plot(x_variances, diff3, marker='o', color='red', linestyle='-', linewidth=2, markersize=6, label='2 CNs')\n", "plt.plot(x_variances, diff1, marker='s', color='blue', linestyle='--', linewidth=2, markersize=6, label='4 CNs')\n", "plt.plot(x_variances, diff2, marker='^', color='green', linestyle='-.', linewidth=2, markersize=6, label='8 CNs')\n", "\n", "# 设置图表标签和标题\n", "plt.xlabel('Standard Deviation of CN Performance', fontsize=16)\n", "plt.ylabel('Normalized Max\\nDiff in Avg Latency', fontsize=16)\n", "\n", "# 设置x轴刻度\n", "plt.xticks(x_variances, fontsize=16)\n", "plt.yticks(fontsize=16)\n", "\n", "# 添加图例\n", "plt.legend(loc='upper left', fontsize=15, frameon=False)\n", "\n", "# 添加网格\n", "plt.grid(True, linestyle='--', alpha=0.7)\n", "\n", "# 显示图表\n", "plt.tight_layout()\n", "plt.savefig('latency_difference_plot_normalized_by_min.pdf', dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 4}