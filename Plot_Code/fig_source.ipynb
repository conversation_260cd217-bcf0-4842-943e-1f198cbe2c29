{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.ticker import FuncFormatter\n", "\n", "# Define constants\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# Data for the chart\n", "labels = ['LRU', 'NS-Alchemy', 'Alchemy']\n", "data = [13.5534,13.1247 ,12.7967]  # Example data for LRU, NS-Alchemy, Alchemy\n", "\n", "# Convert list to NumPy array\n", "data = np.array(data)\n", "\n", "# Transform delay as per the formula\n", "data = data / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) + (1 - data / 100) * (DDR_DELAY + CXL_DELAY)\n", "\n", "# Calculate percentage increase\n", "baseline = data[0]  # Assuming LRU is the baseline\n", "percentage_increase = data / baseline * 100\n", "\n", "# Define colors\n", "colors = ['#1c74b4', '#2ca42c', '#fc7c0c']\n", "\n", "# Create the figure\n", "fig, ax = plt.subplots(figsize=(6, 3))\n", "\n", "# Plot the chart\n", "bar_width = 0.3\n", "x = np.arange(len(labels))\n", "\n", "ax.bar(x, percentage_increase, width=bar_width, color=colors)\n", "\n", "ax.set_ylim(70, 115)\n", "ax.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax.set_ylabel('Normalized\\nAccess Latency (%)', fontsize=14)\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(labels, fontsize=15)\n", "\n", "# Modify y-axis tick labels' font size and rotation\n", "for label in ax.get_yticklabels():\n", "    label.set_fontsize(10)\n", "    label.set_rotation(45)\n", "\n", "plt.subplots_adjust(left=0.15, right=0.95, top=0.95, bottom=0.18)\n", "plt.savefig(\"eva_fig1_a.pdf\", format=\"pdf\", dpi=300)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.ticker import FuncFormatter\n", "\n", "# Define constants\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# Data for the chart\n", "labels = ['NS-Alchemy', 'Alchemy']\n", "data = [13.5534, 13.1247, 12.7967]  # Example data for LRU, NS-Alchemy, Alchemy\n", "\n", "# Convert list to NumPy array\n", "data = np.array(data)\n", "\n", "# Transform delay as per the formula\n", "transformed_data = data / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) + (1 - data / 100) * (DDR_DELAY + CXL_DELAY)\n", "\n", "# Calculate percentage decrease compared to LRU\n", "baseline = transformed_data[0]  # LRU is the baseline\n", "percentage_decrease = (baseline - transformed_data[1:]) / baseline * 100\n", "\n", "# Define colors\n", "colors = ['#2ca42c', '#fc7c0c']  # Colors for NS-Alchemy and Alchemy\n", "\n", "# Create the figure\n", "fig, ax = plt.subplots(figsize=(6, 3))\n", "\n", "# Plot the chart\n", "bar_width = 0.3  # Increased bar width to reduce space between bars\n", "x = np.arange(len(labels)) * 0.4  # Adjust x position to bring bars closer\n", "\n", "ax.bar(x, percentage_decrease, width=bar_width, color=colors)\n", "\n", "ax.set_ylim(0, 10)\n", "ax.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax.set_ylabel('Latency Reduction\\nCompared to LRU (%)', fontsize=14)\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(labels, fontsize=15)\n", "\n", "# Modify y-axis tick labels' font size and rotation\n", "for label in ax.get_yticklabels():\n", "    label.set_fontsize(10)\n", "    label.set_rotation(45)\n", "\n", "plt.subplots_adjust(left=0.15, right=0.95, top=0.95, bottom=0.18)\n", "plt.savefig(\"eva_fig1_a_reduction.pdf\", format=\"pdf\", dpi=300)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x216 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.ticker import FuncFormatter\n", "\n", "# Define constants\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# Data for the a图\n", "labels_a = ['NS-Alchemy', 'Alchemy']\n", "data_a = [13.5534, 13.1247, 12.7967]  # Example data for LRU, NS-Alchemy, Alchemy\n", "\n", "# Convert list to NumPy array\n", "data_a = np.array(data_a)\n", "\n", "# Transform delay as per the formula\n", "transformed_data_a = data_a / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) + (1 - data_a / 100) * (DDR_DELAY + CXL_DELAY)\n", "\n", "# Calculate percentage decrease compared to LRU\n", "baseline_a = transformed_data_a[0]  # LRU is the baseline\n", "percentage_decrease_a = (baseline_a - transformed_data_a[1:]) / baseline_a * 100\n", "\n", "# Data for the b图\n", "labels_b = ['NS-Alchemy', 'Alchemy']\n", "data_b = [37, 63]  # Efficiency data\n", "\n", "# Define colors\n", "colors_a = ['#2ca42c', '#ff7f0e']  # Colors for a图\n", "colors_b = ['#2ca42c', '#ff7f0e']  # Colors for b图\n", "\n", "# Create the figure with two subplots\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 3), gridspec_kw={'width_ratios': [1, 1]})\n", "\n", "# Plot the a图\n", "bar_width_a = 0.3\n", "x_a = np.arange(len(labels_a)) * 0.4\n", "\n", "ax1.bar(x_a, percentage_decrease_a, width=bar_width_a, color=colors_a)\n", "\n", "ax1.set_ylim(0, 10)\n", "ax1.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax1.set_ylabel('Latency Reduction\\nCompared to LRU (%)', fontsize=14)\n", "ax1.set_xticks(x_a)\n", "ax1.set_xticklabels(labels_a, fontsize=15)\n", "\n", "# Modify y-axis tick labels' font size and rotation for a图\n", "for label in ax1.get_yticklabels():\n", "    label.set_fontsize(10)\n", "    label.set_rotation(45)\n", "\n", "# Plot the b图\n", "bar_width_b = 0.3\n", "x_b = np.arange(len(labels_b)) * 0.4\n", "\n", "ax2.bar(x_b, data_b, width=bar_width_b, color=colors_b)\n", "\n", "ax2.set_ylim(0, 100)\n", "ax2.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax2.set_ylabel('Efficiency of Bypass (%)', fontsize=14)\n", "ax2.set_xticks(x_b)\n", "ax2.set_xticklabels(labels_b, fontsize=15)\n", "\n", "# Modify y-axis tick labels' font size and rotation for b图\n", "for label in ax2.get_yticklabels():\n", "    label.set_fontsize(10)\n", "    label.set_rotation(45)\n", "\n", "# Adjust layout for tight spacing\n", "plt.subplots_adjust(left=0.1, right=0.95, top=0.95, bottom=0.18, wspace=0.3)\n", "\n", "plt.savefig(\"eva_fig3.pdf\", format=\"pdf\", dpi=300)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0MAAADdCAYAAAB5RKlBAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAgAElEQVR4nOzdZ5hdVdnG8f+d0EIxdKUIoShKsWCw0kFRFFQQUYpgAQV5BRFQpDcFlK4ighRpCkqTZgEDSlEBpVfpIL2FEgLJ/X5Y68hhmEmm7DMzSe7fdc115py9z94rfJjFs9eznke2iYiIiIiImNGMGOoBREREREREDIUEQxERERERMUNKMBQRERERETOkBEMRERERETFDSjAUEREREREzpARDERERERExQ0owFBERERERM6QEQxERERERMUOaaagHEBER0UmSFgE+CnwQWBgYBTwB3A5cBlxme/LQjTAiIoaKbA/1GCIiIhonaXVgJ2AdYCTwIPA48BIwL/BWYA7gv8CxwKG2nxuSwUZExJBIMBQREdMdSecDawK/B34NXG77yS7njACWB9YDNgEWBDaz/YdBHm5ERAyRXu0ZkjRK0oOSJkl6e28vLulUSZa0Xf+HGBER07Ouc4ykLevcYUljpvC9Zep3HpA0qsvhu4AlbW9s++yugRCA7cm2b7B9gO3lgK8Co5v8t0VExPDW2wIK/wcsAvze9h19uP6P6uuekubo08giImJG0a85xvbtlJWfRes12o/tYPuRvgzC9nm2z+jLdyIiYto21WBI0kigtbJzcl8ubvvfwM3AApQUhIiIiP8ZyBxTnVJft6tpbxEREb3Wm4ljdcom01eBiwAkLS7popqa8FL9uUnSDpLU5fu/r6+bNzbqiIiYXqxOlzmmi2UlXS5pgqQ7JX22y/GLgEn1GqtP7WaS5pZ0kqRHJT0m6WRJ8w3snxAREdOq3gZDALfYfrH+vgDw8fr7rcBzwHLAYcC2Xb7/j/r6gW5yuiMiYsa2en1tn2PanUEpbDARWBo4U9IKrYO2X6BkIACs0Yv7HU2Zw74KbA+8FzimXyOPiIhpXm+CoXfU13vbPrsLWML2W22vCCwEXF6PfaHL9++rr7MAS/RznMNCUjAiIhrX3RzT7nDb7wCWBV6glMjeqcs5rXlmmdYHklbq4XprAxvZPt/26fVaa/dj3IOim2yLiIhoUG/+537u+jq+7bNXgF0k3SfpFUqKwqr12MJdvt/es2FupkG10tHoNOWLiGhcd3NMu98A2H4QuKJ+tnyXc1rzTPscc6mkn0nqOu88Dry/7f1K9bNhRdIckt7l9L+IiOiomXpxzrP1da62zw4HvlZ/vxN4ClgKmJ/y1K7dm9p+f2ZKN6olVLvrEH617Qm9GGvj6lO5oyjN+vaWpExOERGN6W6O6UlPqySteaZ9jlkeOBK4TdLOtlvFGfYBLpJ0I6Xh6tuALfs04sGxCyWFb31JI/IwLiKiM3oTDLXKnI5p++yD9fWPtteRNBtwNSUY6mrx+voK3aRB1Kd2X6s/b6P7yW6ipPOAn9ke14sxN20ksBiAbXcNiHobIEnaGtgaYI455njfO97xjql8IyJi+nLttdc+YXuBto+6m2PabQTcKGlh4EP1s5u6nNOaZ/5Xltv2fcCnJX0KOELS14Bv2P6NpBuAteqpl9q+pX//mo56DJgXSj+kgTyIy9wTETO6buae/9HU/rZKWge4mFLpZ7TtFyWdymulsu+g/MEeUV/vsz2m7fs/BL4H/M32Kl2u/R1gN2ACcCZl39G/KCkLL9XrLUEJvj5J2Rx7GfDN2l+io1qTj6Q1gD2AdYHJtifW43MCL/bnid3YsWN9zTXXNDvgiIhhTtK1tse2ve9ujtkSOKGe8gLwACVj4E3AZOC9tm+o35+Dsro0Eljb9iXd3HM2ylyzA/BTYN8eijUMubZ55y2USnkbUebVV+rxRYHngUm2e0ot7FHmnoiYEXWde9r1Zs/QJcDDlFWkdetnOwLnUv4gz0Vprvr7br8N69fX7vpHbAp8BVjU9va2f2f7btvjbb9q+zHbf7d9hO2PUZ7+3QCs14txD1jbU7jnKCkXY9oCoR0o/6YrJW0rabHBGFNExHSmuzmm3ecpD8hmBf4DbNwKhKp1KYHQg8C47m5ge4LtPYAVgfcAt0r6dFP/gCZ1Wf0ZDby7LRD6LuXB4VXAoZJW6eYSERHRB1MNhmy/StkzA7VXkO1HbX/G9ly2F7Z9sO0tbavLqtB7KRWAHgdO6+baK9o+p7crK7Yftr2j7R/35vz+qMUSPiFpA0mj61O6a4F/Uv4tSNoC2Bk4AriGEpxtI2n2To0rImJ61MMcc2KdT2T7Qtur2p7N9tK2f9vlEpvV15/YntT6UNKskvaUdJWkf0n6GfCc7Y9TKsj9RNL5khZniNViCf8n6fuS3i9pTtuPAOdRgjdq8LYt5QHiQZSU8l0kTdNVWiMihlpvS0UfATwEfErSMlM7uU2r/Ol+tp/v08iGQE23uJYy0ZwJnA60Gvw9A2xYf18BOM32ONvbAZdSVrm6zUWMiIgp6tccU89dr373yC6HjwK2As4Bfgl8ALgQwPaZlJLetwHXS9ptoP+A/qr99/5BmUO2BQ6lBDkjKPtsP1lPXRi4wfattk8Efge8j9J7KSIi+qlXwZDtl2wvantkX/bq2N60Ptk7aupnv56kL0v6jaQzJH11kHotbEfJT/8kpTrei5QVn1GUwGieet54Slf0Jev78yl57F3LikdExFQMYI653faI+t2XuhzeENjG9kG2f0IJmt7TWkmx/YLtnYCPAB9t6t/SD+tT5o8NKIHN+ZSm5itRArlXJY2mpBIuJOlj9Xv/oOyVykO4iIgBGJZNRCXtB+xN2TT7FHAY8MNBuPUEYDYA2/dSSpt+GFiT8gTxw/VJ5J8phR12qjncO9bv3zMIY4yIiKl7mpraXC1LSS17tv0k2zfbXn0Qx9XVc8CbKY3MJ1D24M5FmXceoDQ1Xwu4krICtkMtYnQ45QHdFd1dNCIiemdIg6Faja07WwKr2d7J9jeAbRicPhD3UyoarSJpQcqTuAnAXLbvAv5OKfZwBaVC3lspqQ3LAp+uOd4RETH09gT2l3STpH9SUuQOsf3UEI+rq/spVVTXl/QBSjGI0ZRaCpMoxYqWtP048H3KPtUFKb341q4lxCMiop9602eok26X9G3bZ3RzrL2ogutPR9k+W9KqwK6U1Z5FgJtt/7qe8ghl8+olts+TdDmlf9JMtp/t9qIRETHobJ9Wg6CPArMA37J91RAP6w1s3yzp15QHa5tSgpybKStEAPcBm0k6up67j+****************************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\n", "text/plain": ["<Figure size 864x216 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.ticker import FuncFormatter\n", "\n", "# Define constants\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# Data for the a图\n", "labels_a = ['NS-Alchemy', 'Alchemy']\n", "data_a = [13.5534, 13.1247, 12.7967]  # Example data for LRU, NS-Alchemy, Alchemy\n", "\n", "# Convert list to NumPy array\n", "data_a = np.array(data_a)\n", "\n", "# Transform delay as per the formula\n", "transformed_data_a = data_a / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) + (1 - data_a / 100) * (DDR_DELAY + CXL_DELAY)\n", "\n", "# Calculate percentage decrease compared to LRU\n", "baseline_a = transformed_data_a[0]  # LRU is the baseline\n", "percentage_decrease_a = (baseline_a - transformed_data_a[1:]) / baseline_a * 100\n", "\n", "# Data for the b图\n", "labels_b = ['NS-Alchemy', 'Alchemy']\n", "data_b = [37, 63]  # Efficiency data\n", "\n", "# Define colors\n", "colors_a = ['#2ca42c', '#ff7f0e']  # Colors for a图\n", "colors_b = ['#2ca42c', '#ff7f0e']  # Colors for b图\n", "\n", "# Create the figure with two subplots\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 3), gridspec_kw={'width_ratios': [1, 1]})\n", "\n", "# Plot the a图\n", "bar_width_a = 0.3\n", "x_a = np.arange(len(labels_a)) * 0.4\n", "\n", "ax1.bar(x_a, percentage_decrease_a, width=bar_width_a, color=colors_a)\n", "\n", "ax1.set_ylim(0, 10)\n", "ax1.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax1.set_ylabel('Latency Reduction\\nCompared to LRU (%)', fontsize=16)\n", "ax1.set_xticks(x_a)\n", "ax1.set_xticklabels(labels_a, fontsize=22)\n", "\n", "# Add label for 图a\n", "ax1.text(-0.34, 10.5, '(a)', fontsize=14, fontweight='bold', va='top', ha='right')\n", "\n", "# Modify y-axis tick labels' font size and rotation for a图\n", "for label in ax1.get_yticklabels():\n", "    label.set_fontsize(16)\n", "    label.set_rotation(45)\n", "\n", "# Plot the b图\n", "bar_width_b = 0.3\n", "x_b = np.arange(len(labels_b)) * 0.4\n", "\n", "ax2.bar(x_b, data_b, width=bar_width_b, color=colors_b)\n", "\n", "ax2.set_ylim(0, 100)\n", "ax2.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax2.set_ylabel('Efficiency of Bypass (%)', fontsize=16)\n", "ax2.set_xticks(x_b)\n", "ax2.set_xticklabels(labels_b, fontsize=22)\n", "\n", "# Add label for 图b\n", "ax2.text(-0.355, 105, '(b)', fontsize=14, fontweight='bold', va='top', ha='right')\n", "\n", "# Modify y-axis tick labels' font size and rotation for b图\n", "for label in ax2.get_yticklabels():\n", "    label.set_fontsize(16)\n", "    label.set_rotation(45)\n", "\n", "# Adjust layout for tight spacing\n", "plt.subplots_adjust(left=0.1, right=0.95, top=0.95, bottom=0.18, wspace=0.3)\n", "\n", "plt.savefig(\"eva_fig1_combined.pdf\", format=\"pdf\", dpi=300)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.ticker import FuncFormatter\n", "\n", "# Define constants\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# Data for the a图\n", "labels_a = ['NS-Alchemy', 'Alchemy']\n", "data_a = [13.5534, 13.1247, 12.7967]  # Example data for LRU, NS-Alchemy, Alchemy\n", "\n", "# Convert list to NumPy array\n", "data_a = np.array(data_a)\n", "\n", "# Transform delay as per the formula\n", "transformed_data_a = data_a / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) + (1 - data_a / 100) * (DDR_DELAY + CXL_DELAY)\n", "\n", "# Calculate percentage decrease compared to LRU\n", "baseline_a = transformed_data_a[0]  # LRU is the baseline\n", "percentage_decrease_a = (baseline_a - transformed_data_a[1:]) / baseline_a * 100\n", "\n", "# Data for the b图\n", "labels_b = ['NS-Alchemy', 'Alchemy']\n", "data_b = [37, 63]  # Efficiency data\n", "\n", "# Define colors\n", "colors_a = ['#2ca42c', '#ff7f0e']  # Colors for a图\n", "colors_b = ['#2ca42c', '#ff7f0e']  # Colors for b图\n", "\n", "# Create the figure with two subplots\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 3), gridspec_kw={'width_ratios': [1, 1]})\n", "\n", "# Plot the a图\n", "bar_width_a = 0.3\n", "x_a = np.arange(len(labels_a)) * 0.4\n", "\n", "ax1.bar(x_a, percentage_decrease_a, width=bar_width_a, color=colors_a)\n", "\n", "ax1.set_ylim(0, 10)\n", "ax1.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax1.set_ylabel('Latency Reduction\\nCompared to LRU (%)', fontsize=14)\n", "ax1.set_xticks(x_a)\n", "ax1.set_xticklabels(labels_a, fontsize=18)\n", "\n", "# Add label for 图a\n", "ax1.text(-0.4, 10.5, '(a)', fontsize=14, fontweight='bold', va='top', ha='right')\n", "\n", "# Modify y-axis tick labels' font size and rotation for a图\n", "for label in ax1.get_yticklabels():\n", "    label.set_fontsize(14)\n", "    label.set_rotation(45)\n", "\n", "# Plot the b图\n", "bar_width_b = 0.3\n", "x_b = np.arange(len(labels_b)) * 0.4\n", "\n", "ax2.bar(x_b, data_b, width=bar_width_b, color=colors_b)\n", "\n", "ax2.set_ylim(0, 100)\n", "ax2.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax2.set_ylabel('Efficiency of Bypass (%)', fontsize=14)\n", "ax2.set_xticks(x_b)\n", "ax2.set_xticklabels(labels_b, fontsize=18)\n", "\n", "# Add label for 图b\n", "ax2.text(-0.4, 105, '(b)', fontsize=14, fontweight='bold', va='top', ha='right')\n", "\n", "# Modify y-axis tick labels' font size and rotation for b图\n", "for label in ax2.get_yticklabels():\n", "    label.set_fontsize(14)\n", "    label.set_rotation(45)\n", "\n", "# Adjust layout for tight spacing\n", "plt.subplots_adjust(left=0.12, right=0.95, top=0.95, bottom=0.2, wspace=0.4)\n", "\n", "plt.savefig(\"eva_fig1_combined.pdf\", format=\"pdf\", dpi=300)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x216 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.ticker import FuncFormatter\n", "\n", "# Define constants\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# Data for the a图\n", "labels_a = ['NS-Alchemy', 'Alchemy']\n", "data_a = [13.5534, 13.1247, 12.7967]  # Example data for LRU, NS-Alchemy, Alchemy\n", "\n", "# Convert list to NumPy array\n", "data_a = np.array(data_a)\n", "\n", "# Transform delay as per the formula\n", "transformed_data_a = data_a / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) + (1 - data_a / 100) * (DDR_DELAY + CXL_DELAY)\n", "\n", "# Calculate percentage decrease compared to LRU\n", "baseline_a = transformed_data_a[0]  # LRU is the baseline\n", "percentage_decrease_a = (baseline_a - transformed_data_a[1:]) / baseline_a * 100\n", "\n", "# Data for the b图\n", "labels_b = ['NS-Alchemy', 'Alchemy']\n", "data_b = [37, 63]  # Efficiency data\n", "\n", "# Define colors\n", "colors_a = ['#2ca42c', '#ff7f0e']  # Colors for a图\n", "colors_b = ['#2ca42c', '#ff7f0e']  # Colors for b图\n", "\n", "# Create the figure with two subplots\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(8, 3), gridspec_kw={'width_ratios': [1, 1]})\n", "\n", "# Plot the a图\n", "bar_width_a = 0.2  # Decreased bar width for slimmer bars\n", "x_a = np.arange(len(labels_a)) * 0.5  # Adjust x position for spacing\n", "\n", "ax1.bar(x_a, percentage_decrease_a, width=bar_width_a, color=colors_a)\n", "\n", "ax1.set_ylim(0, 10)\n", "ax1.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax1.set_ylabel('Latency Reduction\\nCompared to LRU (%)', fontsize=15)\n", "ax1.set_xticks(x_a)\n", "ax1.set_xticklabels(labels_a, fontsize=18)\n", "\n", "# Add label for 图a\n", "ax1.text(-0.225, 10.5, '(a)', fontsize=14, fontweight='bold', va='top', ha='right')\n", "\n", "# Modify y-axis tick labels' font size and rotation for a图\n", "for label in ax1.get_yticklabels():\n", "    label.set_fontsize(14)\n", "    label.set_rotation(45)\n", "\n", "# Plot the b图\n", "bar_width_b = 0.2  # Decreased bar width for slimmer bars\n", "x_b = np.arange(len(labels_b)) * 0.5  # Adjust x position for spacing\n", "\n", "ax2.bar(x_b, data_b, width=bar_width_b, color=colors_b)\n", "\n", "ax2.set_ylim(0, 100)\n", "ax2.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x)}%'))\n", "ax2.set_ylabel('Efficiency of Bypass (%)', fontsize=15)\n", "ax2.set_xticks(x_b)\n", "ax2.set_xticklabels(labels_b, fontsize=18)\n", "\n", "# Add label for 图b\n", "ax2.text(-0.225, 105, '(b)', fontsize=14, fontweight='bold', va='top', ha='right')\n", "\n", "# Modify y-axis tick labels' font size and rotation for b图\n", "for label in ax2.get_yticklabels():\n", "    label.set_fontsize(14)\n", "    label.set_rotation(45)\n", "\n", "# Adjust layout for tight spacing\n", "plt.subplots_adjust(left=0.15, right=0.95, top=0.95, bottom=0.2, wspace=0.4)\n", "\n", "plt.savefig(\"eva_fig3.pdf\", format=\"pdf\", dpi=300)\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 4}