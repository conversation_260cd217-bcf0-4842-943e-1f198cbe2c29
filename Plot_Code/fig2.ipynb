{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 机器名称\n", "machines = ['1:1:1:1', '1:0.9:0.8:0.7', '1:0.85:0.7:0.55', '1:0.8:0.6:0.4']\n", "\n", "# miss_rate 数据\n", "miss_rate = [\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # Machine A 的miss rate\n", "    [9.5400, 10.3333, 10.9489, 11.8924],  # Machine B 的miss rate\n", "    [8.9626, 9.5834, 10.5790, 11.4544],  # Machine C 的miss rate\n", "    [7.9674, 9.3264, 10.0916, 11.7648],  # Machine D 的miss rate\n", "]\n", "\n", "# 计算每个机器的单独延迟数据\n", "machine_delays = []\n", "for rates in miss_rate:\n", "    delays = [(rate/100 * (SSD_DELAY + CXL_DELAY + CXL_DELAY) + (1 - rate/100) * (CXL_DELAY + DDR_DELAY)) for rate in rates]\n", "    machine_delays.append(delays)\n", "    \n", "# 计算平均延迟\n", "average_delay = [np.mean(delays) for delays in machine_delays]\n", "\n", "# 绘制图表\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "\n", "# X轴位置\n", "x = np.arange(len(machines))\n", "width = 0.15  # 柱子宽度\n", "\n", "# 平均延迟柱状图\n", "ax.bar(x - 2 * width, average_delay, width, label='Average Delay', color='gray')\n", "\n", "# 各机器的单独延迟柱状图\n", "for i in range(4):\n", "    ax.bar(x + (i - 1) * width, [machine[i] for machine in machine_delays], width, label=f'Machine {machines[i]} Delay')\n", "\n", "# 设置标签和标题\n", "# ax.set_xlabel('Machines')\n", "ax.set_ylabel('Avg Delay (ns)')\n", "ax.set_title('Avg Access Delay by Diff Perf Mode')\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(machines)\n", "ax.legend()\n", "\n", "# 设置Y轴为科学计数法显示\n", "# ax.ticklabel_format(style='sci', axis='y', scilimits=(0, 0))\n", "# 设置Y轴为常规格式显示\n", "ax.get_yaxis().get_major_formatter().set_scientific(False)\n", "\n", "\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 机器名称\n", "machines = ['1:1:1:1', '1:0.9:0.8:0.7', '1:0.85:0.7:0.55', '1:0.8:0.6:0.4']\n", "\n", "# miss_rate 数据\n", "miss_rate = [\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # Machine A 的 miss rate\n", "    [9.5400, 10.3333, 10.9489, 11.8924],  # Machine B 的 miss rate\n", "    [8.9626, 9.5834, 10.5790, 11.4544],  # Machine C 的 miss rate\n", "    [7.9674, 9.3264, 10.0916, 11.7648],  # Machine D 的 miss rate\n", "]\n", "\n", "# 计算每个机器的单独延迟数据\n", "machine_delays = []\n", "for rates in miss_rate:\n", "    delays = [(rate / 100 * (SSD_DELAY + CXL_DELAY + CXL_DELAY) + (1 - rate / 100) * (CXL_DELAY + DDR_DELAY)) for rate in rates]\n", "    machine_delays.append(delays)\n", "\n", "# 计算平均延迟\n", "average_delay = [np.mean(delays) for delays in machine_delays]\n", "\n", "# 对每个机器的延迟进行归一化\n", "normalized_delays = []\n", "for i, delays in enumerate(machine_delays):\n", "    normalized = [delay / average_delay[i] for delay in delays]\n", "    normalized_delays.append(normalized)\n", "\n", "# 绘制图表\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "\n", "# X轴位置\n", "x = np.arange(len(machines))\n", "width = 0.15  # 柱子宽度\n", "\n", "# 手动设置每组柱子的位置和名称\n", "offsets = [-1.5 * width, -0.5 * width, 0.5 * width, 1.5 * width]\n", "labels = ['Prime CN', 'Performance CN', 'Balanced CN', 'Economy CN']\n", "colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']  # 使用不同颜色\n", "\n", "# 各机器的归一化延迟柱状图，手动指定位置和颜色\n", "for i in range(4):\n", "    ax.bar(x + offsets[i], [machine[i] for machine in normalized_delays], width, label=labels[i], color=colors[i])\n", "\n", "# 设置标签和标题\n", "ax.set_ylabel('Relative Memory Access Latency')\n", "ax.set_title('Normalized Average Memory Access Latency Across 4 CN Nodes')\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(machines)\n", "ax.legend()\n", "\n", "# 设置 y 轴范围，从 0.8 开始\n", "ax.set_ylim(0.8, max(max(normalized_delays)) + 0.2)\n", "\n", "plt.savefig(\"Differences Across Heterogeneous 4 CNs.pdf\", format=\"pdf\", dpi=300)  # 保存图像\n", "# 显示图表\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 机器名称\n", "machines = ['1:1:1:1', '1:0.9:0.8:0.7', '1:0.85:0.7:0.55', '1:0.8:0.6:0.4']\n", "\n", "# miss_rate 数据\n", "miss_rate = [\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # Machine A 的 miss rate\n", "    [9.5400, 10.3333, 10.9489, 11.8924],  # Machine B 的 miss rate\n", "    [8.9626, 9.5834, 10.5790, 11.4544],  # Machine C 的 miss rate\n", "    [7.9674, 9.3264, 10.0916, 11.7648],  # Machine D 的 miss rate\n", "]\n", "\n", "# 计算每个机器的单独延迟数据\n", "machine_delays = []\n", "for rates in miss_rate:\n", "    delays = [(rate / 100 * (SSD_DELAY + CXL_DELAY + CXL_DELAY) + (1 - rate / 100) * (CXL_DELAY + DDR_DELAY)) for rate in rates]\n", "    machine_delays.append(delays)\n", "\n", "# 计算平均延迟\n", "average_delay = [np.mean(delays) for delays in machine_delays]\n", "\n", "# 对每个机器的延迟进行归一化\n", "normalized_delays = []\n", "for i, delays in enumerate(machine_delays):\n", "    normalized = [delay / average_delay[i] for delay in delays]\n", "    normalized_delays.append(normalized)\n", "\n", "# 绘制图表\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "\n", "# X轴位置\n", "x = np.arange(len(machines))\n", "width = 0.15  # 柱子宽度\n", "\n", "# 手动设置每组柱子的位置和名称\n", "offsets = [-1.5 * width, -0.5 * width, 0.5 * width, 1.5 * width]\n", "labels = ['Prime CN', 'Performance CN', 'Balanced CN', 'Economy CN']\n", "colors = ['#1c74b4', '#2ca42c', '#fc7c0c', '#d4242c']  # 使用不同颜色\n", "\n", "# 各机器的归一化延迟柱状图，手动指定位置和颜色\n", "for i in range(4):\n", "    ax.bar(x + offsets[i], [machine[i] for machine in normalized_delays], width, label=labels[i], color=colors[i])\n", "\n", "# 设置标签和标题\n", "ax.set_ylabel('Relative Memory Access Latency')\n", "ax.set_title('Normalized Average Memory Access Latency Across 4 CN Nodes')\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(machines)\n", "ax.legend()\n", "\n", "# 设置 y 轴范围，从 0.8 开始\n", "ax.set_ylim(0.8, max(max(normalized_delays)) + 0.2)\n", "\n", "plt.savefig(\"Differences Across Heterogeneous 4 CNs.pdf\", format=\"pdf\", dpi=300)  # 保存图像\n", "# 显示图表\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 机器名称:\n", "machines = ['1:1:1:1', '1:0.9:0.8:0.7', '1:0.85:0.7:0.55', '1:0.8:0.6:0.4',\"1:1:0.5:0.5\"]\n", "\n", "# miss_rate 数据\n", "miss_rate = [\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # Machine A 的 miss rate\n", "    [9.5400, 10.3333, 10.9489, 11.8924],  # Machine B 的 miss rate\n", "    [8.9626, 9.5834, 10.5790, 11.4544],  # Machine C 的 miss rate\n", "    [7.9674, 9.3264, 10.0916, 11.7648],  # Machine D 的 miss rate\n", "#     [10.1174, 10.1590, 12.5953, 12.4293],  # Machine D 的 miss rate\n", "    [15.9349,15.9349,18.8008,18.8008]\n", "]\n", "\n", "# 计算每个机器的单独延迟数据\n", "machine_delays = []\n", "for rates in miss_rate:\n", "    delays = [(rate / 100 * (SSD_DELAY + CXL_DELAY + CXL_DELAY) + (1 - rate / 100) * (CXL_DELAY + DDR_DELAY)) for rate in rates]\n", "    machine_delays.append(delays)\n", "\n", "# 计算平均延迟\n", "average_delay = [np.mean(delays) for delays in machine_delays]\n", "\n", "# 对每个机器的延迟进行归一化\n", "normalized_delays = []\n", "for i, delays in enumerate(machine_delays):\n", "    normalized = [delay / average_delay[i] for delay in delays]\n", "    normalized_delays.append(normalized)\n", "\n", "# 绘制图表\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "\n", "# X轴位置\n", "x = np.arange(len(machines))\n", "width = 0.15  # 柱子宽度\n", "\n", "# 手动设置每组柱子的位置和名称\n", "offsets = [-1.5 * width, -0.5 * width, 0.5 * width, 1.5 * width]\n", "labels = ['Prime CN', 'Performance CN', 'Balanced CN', 'Economy CN']\n", "\n", "# 使用不同透明度的红色\n", "colors = ['#d4242c', '#d4242cb0', '#d4242c90', '#d4242c60']\n", "# colors = ['#8b0000', '#a52a2a', '#b22222', '#cd5c5c']\n", "\n", "# 各机器的归一化延迟柱状图，手动指定位置和颜色\n", "for i in range(4):\n", "    ax.bar(x + offsets[i], [machine[i] for machine in normalized_delays], width,color=colors[i])\n", "#     ax.bar(x + offsets[i], [machine[i] for machine in normalized_delays], width, label=labels[i], color=colors[i])\n", "\n", "# 设置标签和标题\n", "ax.set_ylabel('Relative Memory Access Latency')\n", "# ax.set_title('Normalized Average Memory Access Latency Across 4 CN Nodes')\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(machines)\n", "# ax.legend()\n", "\n", "# 设置 y 轴范围，从 0.8 开始\n", "ax.set_ylim(0.8, max(max(normalized_delays)) + 0.2)\n", "\n", "plt.savefig(\"Differences Across Heterogeneous 4 CNs.pdf\", format=\"pdf\", dpi=300)  # 保存图像\n", "# 显示图表\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 机器名称:\n", "machines = ['1:1:1:1', '1:0.9:0.8:0.7', '1:0.85:0.7:0.55', '1:0.8:0.6:0.4',\"1:1:0.5:0.5\",\"1:1:1:1:0.5:0.5:0.5:0.5\"]\n", "\n", "# miss_rate 数据\n", "miss_rate = [\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # Machine A 的 miss rate\n", "    [9.5400, 10.3333, 10.9489, 11.8924],  # Machine B 的 miss rate\n", "    [8.9626, 9.5834, 10.5790, 11.4544],  # Machine C 的 miss rate\n", "    [7.9674, 9.3264, 10.0916, 11.7648],  # Machine D 的 miss rate\n", "    [10.1174, 10.1590, 12.5953, 12.4293],  # Machine D 的 miss rate\n", "    [15.9349,15.9349,15.9349,15.9349,18.8008,18.8008,18.8008,18.8008]\n", "]\n", "\n", "# 计算每个机器的单独延迟数据\n", "machine_delays = []\n", "for rates in miss_rate:\n", "    delays = [(rate / 100 * (SSD_DELAY + CXL_DELAY + CXL_DELAY) + (1 - rate / 100) * (CXL_DELAY + DDR_DELAY)) for rate in rates]\n", "    machine_delays.append(delays)\n", "\n", "# 计算平均延迟\n", "average_delay = [np.mean(delays) for delays in machine_delays]\n", "\n", "# 对每个机器的延迟进行归一化\n", "normalized_delays = []\n", "for i, delays in enumerate(machine_delays):\n", "    normalized = [delay / average_delay[i] for delay in delays]\n", "    normalized_delays.append(normalized)\n", "\n", "# 绘制图表\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "\n", "# X轴位置\n", "x = np.arange(len(machines))\n", "width = 0.15  # 柱子宽度\n", "\n", "# 手动设置每组柱子的位置和名称\n", "offsets = [-1.5 * width, -0.5 * width, 0.5 * width, 1.5 * width]\n", "labels = ['Prime CN', 'Performance CN', 'Balanced CN', 'Economy CN']\n", "\n", "# 使用不同透明度的红色\n", "colors = ['#d4242c', '#d4242cb0', '#d4242c90', '#d4242c60']\n", "# colors = ['#8b0000', '#a52a2a', '#b22222', '#cd5c5c']\n", "\n", "# 各机器的归一化延迟柱状图，手动指定位置和颜色\n", "for i in range(4):\n", "    ax.bar(x + offsets[i], [machine[i] for machine in normalized_delays], width,color=colors[i])\n", "#     ax.bar(x + offsets[i], [machine[i] for machine in normalized_delays], width, label=labels[i], color=colors[i])\n", "\n", "# 设置标签和标题\n", "ax.set_ylabel('Relative Memory Access Latency')\n", "# ax.set_title('Normalized Average Memory Access Latency Across 4 CN Nodes')\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(machines)\n", "# ax.legend()\n", "\n", "# 设置 y 轴范围，从 0.8 开始\n", "ax.set_ylim(0.8, max(max(normalized_delays)) + 0.2)\n", "\n", "plt.savefig(\"Differences Across Heterogeneous 4 CNs.pdf\", format=\"pdf\", dpi=300)  # 保存图像\n", "# 显示图表\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Machine 1:1:1:1 normalized delays: [0.9913475112889064, 1.0110066610267952, 1.0112384564548027, 0.9864073712294956]\n", "Max-Min difference for 1:1:1:1: 0.02\n", "Machine 1:0.9:0.8:0.7 normalized delays: [0.8952789933335927, 0.968238352740312, 1.0248547420643714, 1.1116279118617238]\n", "Max-Min difference for 1:0.9:0.8:0.7: 0.22\n", "Machine 1:0.85:0.7:0.55 normalized delays: [0.8856555820646519, 0.9456978866992588, 1.0419899590159707, 1.1266565722201185]\n", "Max-Min difference for 1:0.85:0.7:0.55: 0.24\n", "Machine 1:0.8:0.6:0.4 normalized delays: [0.817658181602138, 0.9538021978660143, 1.030459593925704, 1.1980800266061438]\n", "Max-Min difference for 1:0.8:0.6:0.4: 0.38\n", "Machine 1:1:0.5:0.5 normalized delays: [0.8951498764991108, 0.8987610576371967, 1.1102495337602387, 1.0958395321034538]\n", "Max-Min difference for 1:1:0.5:0.5: 0.22\n", "Machine 1:1:1:1:0.5:0.5:0.5:0.5 normalized delays: [0.917080238166851, 0.9278997959370615, 0.9283332626690257, 0.9114280601224236, 1.074485696966149, 1.0821683244392504, 1.0838223422322715, 1.0747822794669666]\n", "Max-Min difference for 1:1:1:1:0.5:0.5:0.5:0.5: 0.17\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 机器名称:\n", "machines = ['1:1:1:1', '1:0.9:0.8:0.7', '1:0.85:0.7:0.55', '1:0.8:0.6:0.4', \"1:1:0.5:0.5\", \"1:1:1:1:0.5:0.5:0.5:0.5\"]\n", "\n", "# miss_rate 数据\n", "miss_rate = [\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # Machine A 的 miss rate\n", "    [9.5400, 10.3333, 10.9489, 11.8924],  # Machine B 的 miss rate\n", "    [8.9626, 9.5834, 10.5790, 11.4544],  # Machine C 的 miss rate\n", "    [7.9674, 9.3264, 10.0916, 11.7648],  # Machine D 的 miss rate\n", "    [10.1174, 10.1590, 12.5953, 12.4293],  # Machine E 的 miss rate\n", "    [15.8847, 16.0744, 16.0820, 15.7856, 18.6445, 18.7792, 18.8082, 18.6497]  # Machine F 的 miss rate\n", "]\n", "\n", "# 计算每个机器的单独延迟数据\n", "machine_delays = []\n", "for rates in miss_rate:\n", "    delays = [(rate / 100 * (SSD_DELAY + CXL_DELAY + CXL_DELAY) + (1 - rate / 100) * (CXL_DELAY + DDR_DELAY)) for rate in rates]\n", "    machine_delays.append(delays)\n", "\n", "# 计算平均延迟\n", "average_delay = [np.mean(delays) for delays in machine_delays]\n", "\n", "# 对每个机器的延迟进行归一化\n", "normalized_delays = []\n", "for i, delays in enumerate(machine_delays):\n", "    normalized = [delay / average_delay[i] for delay in delays]\n", "    normalized_delays.append(normalized)\n", "\n", "# 打印每个机器的归一化延迟以及最高和最低的差值\n", "for i, machine in enumerate(normalized_delays):\n", "    max_delay = max(machine)\n", "    min_delay = min(machine)\n", "    difference = max_delay - min_delay\n", "    print(f\"Machine {machines[i]} normalized delays: {machine}\")\n", "    print(f\"Max-Min difference for {machines[i]}: {difference:.2f}\")\n", "\n", "# 绘制图表\n", "fig, ax = plt.subplots(figsize=(15, 5))\n", "\n", "# X轴位置\n", "x = np.arange(len(machines))\n", "width = 0.15  # 柱子宽度\n", "\n", "# 使用不同透明度的红色\n", "colors = ['#d4242c', '#d4242ccc', '#d4242caa', '#d4242c88', '#d4242c66', '#d4242c55', '#d4242c44', '#d4242c33']\n", "\n", "# 绘制每个机器的归一化延迟柱状图\n", "for i, machine in enumerate(normalized_delays):\n", "    for j in range(len(machine)):\n", "        ax.bar(x[i] + (j - (len(machine) - 1) / 2) * width, machine[j], width, color=colors[j])\n", "\n", "# 设置标签和标题，调整字体大小\n", "ax.set_ylabel('Relative Memory Access Latency', fontsize=20)\n", "# ax.set_xlabel('Machine Configurations', fontsize=14)\n", "\n", "# 调整 x 轴标签位置，使其位于每组柱子中间\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(machines, fontsize=18)\n", "ax.tick_params(axis='y', labelsize=18)\n", "\n", "# 设置 y 轴范围，从 0.8 开始\n", "ax.set_ylim(0.8, max(max(normalized_delays)) + 0.2)\n", "\n", "plt.subplots_adjust(left=0.068, right=1.0002, top=0.99, bottom=0.065)\n", "\n", "plt.savefig(\"Differences_Across_Heterogeneous_4_CNs.pdf\", format=\"pdf\", dpi=300)  # 保存图像\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([10683.95687, 10596.89423])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "miss_rate = np.array([14.0405, 13.9245])\n", "\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "delays = (miss_rate / 100 * (SSD_DELAY + CXL_DELAY + CXL_DELAY) + (1 - miss_rate / 100) * (CXL_DELAY + DDR_DELAY))\n", "delays"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Machine 1:1:1:1 delays: [10271.760302, 10475.456858, 10477.858586, 10220.573473999999]\n", "Max-Min difference for 1:1:1:1: 257.29\n", "Machine 1:0.9:0.8:0.7 delays: [7306.151599999999, 7901.554982, 8363.587406, 9071.721896000001]\n", "Max-Min difference for 1:0.9:0.8:0.7: 1765.57\n", "Machine 1:0.85:0.7:0.55 delays: [6872.789803999999, 7338.725035999999, 8085.96266, 8742.985376]\n", "Max-Min difference for 1:0.85:0.7:0.55: 1870.20\n", "Machine 1:0.8:0.6:0.4 delays: [6125.852395999999, 7145.836256, 7720.149463999999, 8975.952991999999]\n", "Max-Min difference for 1:0.8:0.6:0.4: 2850.10\n", "Machine 1:1:0.5:0.5 delays: [7739.513396, 7770.735860000001, 9599.276462, 9474.686822]\n", "Max-Min difference for 1:1:0.5:0.5: 1859.76\n", "Machine 1:1:1:1:0.5:0.5:0.5:0.5 delays: [12068.102738000001, 12210.480176000001, 12216.184280000001, 11993.724224, 14139.44303, 14240.540767999999, 14262.306428, 14143.345838]\n", "Max-Min difference for 1:1:1:1:0.5:0.5:0.5:0.5: 2268.58\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 定义常量和数据\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "# 机器名称:\n", "machines = ['1:1:1:1', '1:0.9:0.8:0.7', '1:0.85:0.7:0.55', '1:0.8:0.6:0.4', \"1:1:0.5:0.5\", \"1:1:1:1:0.5:0.5:0.5:0.5\"]\n", "\n", "# miss_rate 数据\n", "miss_rate = [\n", "    [13.4913, 13.7627, 13.7659, 13.4231],  # Machine A 的 miss rate\n", "    [9.5400, 10.3333, 10.9489, 11.8924],  # Machine B 的 miss rate\n", "    [8.9626, 9.5834, 10.5790, 11.4544],  # Machine C 的 miss rate\n", "    [7.9674, 9.3264, 10.0916, 11.7648],  # Machine D 的 miss rate\n", "    [10.1174, 10.1590, 12.5953, 12.4293],  # Machine E 的 miss rate\n", "    [15.8847, 16.0744, 16.0820, 15.7856, 18.6445, 18.7792, 18.8082, 18.6497]  # Machine F 的 miss rate\n", "]\n", "\n", "# 计算每个机器的单独延迟数据\n", "machine_delays = []\n", "for rates in miss_rate:\n", "    delays = [(rate / 100 * (SSD_DELAY + CXL_DELAY + CXL_DELAY) + (1 - rate / 100) * (CXL_DELAY + DDR_DELAY)) for rate in rates]\n", "    machine_delays.append(delays)\n", "\n", "# 打印每个机器的延迟以及最高和最低的差值\n", "for i, delays in enumerate(machine_delays):\n", "    max_delay = max(delays)\n", "    min_delay = min(delays)\n", "    difference = max_delay - min_delay\n", "    print(f\"Machine {machines[i]} delays: {delays}\")\n", "    print(f\"Max-Min difference for {machines[i]}: {difference:.2f}\")\n", "\n", "# 绘制图表\n", "fig, ax = plt.subplots(figsize=(15, 5))\n", "\n", "# X轴位置\n", "x = np.arange(len(machines))\n", "width = 0.15  # 柱子宽度\n", "\n", "# 使用不同透明度的红色\n", "colors = ['#d4242c', '#d4242ccc', '#d4242caa', '#d4242c88', '#d4242c66', '#d4242c55', '#d4242c44', '#d4242c33']\n", "\n", "# 绘制每个机器的延迟柱状图\n", "for i, delays in enumerate(machine_delays):\n", "    for j in range(len(delays)):\n", "        ax.bar(x[i] + (j - (len(delays) / 2)) * width, delays[j], width, color=colors[j])\n", "\n", "# 设置标签和标题\n", "ax.set_ylabel('Memory Access Latency (ns)')\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(machines)\n", "\n", "# 设置 y 轴范围，从 0 开始\n", "ax.set_ylim(0, max(max(machine_delays)) + 10000)\n", "\n", "plt.savefig(\"Actual_Latency_Across_Heterogeneous_4_CNs.pdf\", format=\"pdf\", dpi=300)  # 保存图像\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 4}