{"cells": [{"cell_type": "code", "execution_count": 184, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.4275219  1.34987474 0.62307393 1.59952943]\n", "Adjusted Mean: 1.0\n"]}], "source": ["import numpy as np\n", "\n", "def generate_adjusted_uniform_random_numbers(n, variance):\n", "    mean = 1\n", "    # 计算区间 [a, b] 的端点\n", "    range_width = np.sqrt(12 * variance)\n", "    a = mean - range_width / 2\n", "    b = mean + range_width / 2\n", "    \n", "    # 生成均匀分布的随机数\n", "    random_numbers = np.random.uniform(low=a, high=b, size=n)\n", "    \n", "    # 调整随机数的均值\n", "    current_mean = np.mean(random_numbers)\n", "    adjustment = mean - current_mean\n", "    adjusted_numbers = random_numbers + adjustment\n", "    \n", "    return adjusted_numbers\n", "\n", "# 示例用法\n", "n = 4  # 生成的数字个数\n", "variance = 0.2  # 指定的方差\n", "adjusted_random_numbers = generate_adjusted_uniform_random_numbers(n, variance)\n", "print(adjusted_random_numbers)\n", "print(\"Adjusted Mean:\", np.mean(adjusted_random_numbers))\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best Sample: [1.29698282 0.67989022 0.8939849  1.06654615 1.08666757 1.05661756\n", " 1.00284665 0.70772986 1.22633783 0.98260714]\n", "Best Mean: 1.0000210690671836\n", "Best Variance: 0.035401473336755925\n"]}], "source": ["import numpy as np\n", "\n", "def generate_uniform_samples(n_samples, sample_size, mean_target, std_deviation_target):\n", "    best_sample = None\n", "    best_mean_diff = float('inf')\n", "    best_variance_diff = float('inf')\n", "    \n", "    # 计算区间 [a, b] 的端点\n", "    range_width = std_deviation_target * np.sqrt(12)\n", "    a = mean_target - range_width / 2\n", "    b = mean_target + range_width / 2\n", "    \n", "    for _ in range(n_samples):\n", "        # 生成均匀分布的随机数\n", "        random_numbers = np.random.uniform(low=a, high=b, size=sample_size)\n", "        \n", "        # 计算样本的均值和方差\n", "        sample_mean = np.mean(random_numbers)\n", "        sample_variance = np.var(random_numbers)\n", "        \n", "        # 计算均值和方差的差异\n", "        mean_diff = abs(sample_mean - mean_target)\n", "        variance_diff = abs(sample_variance - std_deviation_target**2)\n", "        \n", "        # 更新最接近的样本\n", "        if mean_diff < best_mean_diff or (mean_diff == best_mean_diff and variance_diff < best_variance_diff):\n", "            best_sample = random_numbers\n", "            best_mean_diff = mean_diff\n", "            best_variance_diff = variance_diff\n", "    \n", "    return best_sample, np.mean(best_sample), np.var(best_sample)\n", "\n", "# 示例用法\n", "n_samples = 1000  # 生成的样本数量\n", "sample_size = 10  # 每个样本的大小\n", "mean_target = 1  # 目标均值\n", "std_deviation_target = 0.2  # 目标标准差\n", "\n", "best_sample, best_mean, best_variance = generate_uniform_samples(n_samples, sample_size, mean_target, std_deviation_target)\n", "\n", "print(\"Best Sample:\", best_sample)\n", "print(\"Best Mean:\", best_mean)\n", "print(\"Best Variance:\", best_variance)\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["最符合的样本: 22.509891096288456, 8.269167123786026, 23.358972882103235, 10.019375954541488\n", "样本的均值: 16.039351764179802\n", "样本的方差: 64.02020355091919\n", "样本的标准差: 8.001262622294009\n"]}], "source": ["import numpy as np\n", "\n", "# 给定的均值和标准差\n", "mu = 1\n", "sigma = 0.5\n", "\n", "# 计算区间 [a, b]\n", "a = mu - (sigma * np.sqrt(12)) / 2\n", "b = mu + (sigma * np.sqrt(12)) / 2\n", "\n", "# 生成样本\n", "n_samples = 4\n", "n_trials = 10000\n", "samples = [np.random.uniform(a, b, n_samples) for _ in range(n_trials)]\n", "\n", "# 计算每组样本的均值和标准差\n", "sample_means = [np.mean(sample) for sample in samples]\n", "sample_stds = [np.std(sample, ddof=1) for sample in samples]\n", "\n", "# 找到最符合的样本\n", "closest_index = np.argmin([(abs(mu - mean) + abs(sigma - std)) for mean, std in zip(sample_means, sample_stds)])\n", "closest_sample = samples[closest_index]\n", "\n", "closest_sample = closest_sample * 16\n", "\n", "# 计算最符合样本的均值、方差和标准差\n", "closest_mean = np.mean(closest_sample)\n", "closest_variance = np.var(closest_sample, ddof=1)\n", "closest_std = np.std(closest_sample, ddof=1)\n", "\n", "closest_sample_str = ', '.join(map(str, closest_sample))\n", "\n", "print(\"最符合的样本:\", closest_sample_str)\n", "print(\"样本的均值:\", closest_mean)\n", "print(\"样本的方差:\", closest_variance)\n", "print(\"样本的标准差:\", closest_std)\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["最符合的样本: 1.5295212131772216, 0.6823976394476592, 1.2230984615782816, 0.5720156673423145\n", "样本的均值: 1.0017582453863692\n", "样本的方差: 0.20473170716159372\n", "样本的标准差: 0.4524728800288408\n"]}], "source": ["import numpy as np\n", "\n", "# 给定的均值和标准差\n", "mu = 1\n", "sigma = 0.45\n", "\n", "# 计算区间 [a, b]\n", "a = mu - (sigma * np.sqrt(12)) / 2\n", "b = mu + (sigma * np.sqrt(12)) / 2\n", "\n", "# 生成样本\n", "n_samples = 4\n", "n_trials = 10000\n", "samples = [np.random.uniform(a, b, n_samples) for _ in range(n_trials)]\n", "\n", "# 计算每组样本的均值和标准差\n", "sample_means = [np.mean(sample) for sample in samples]\n", "sample_stds = [np.std(sample, ddof=1) for sample in samples]\n", "\n", "# 找到最符合的样本\n", "closest_index = np.argmin([(abs(mu - mean) + abs(sigma - std)) for mean, std in zip(sample_means, sample_stds)])\n", "closest_sample = samples[closest_index]\n", "\n", "# closest_sample = closest_sample * 16\n", "\n", "# 计算最符合样本的均值、方差和标准差\n", "closest_mean = np.mean(closest_sample)\n", "closest_variance = np.var(closest_sample, ddof=1)\n", "closest_std = np.std(closest_sample, ddof=1)\n", "\n", "closest_sample_str = ', '.join(map(str, closest_sample))\n", "\n", "print(\"最符合的样本:\", closest_sample_str)\n", "print(\"样本的均值:\", closest_mean)\n", "print(\"样本的方差:\", closest_variance)\n", "print(\"样本的标准差:\", closest_std)\n"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformed Data A:\n", "[[ 1317.275  1158.5  ]\n", " [11441.075  9446.   ]\n", " [ 2663.675  2320.85 ]\n", " [20900.225 18370.85 ]]\n", "\n", "Ratios of the second value to the first value for each load:\n", "[0.13555741 0.17663229 0.1361673  0.12187278]\n"]}], "source": ["import numpy as np\n", "\n", "# Define constants\n", "SSD_DELAY = 75000\n", "CXL_DELAY = 100\n", "DDR_DELAY = 46\n", "\n", "data_a = [\n", "    [1.5617, 1.35],  # Load 1\n", "    [15.0601, 12.4],  # <PERSON><PERSON> 2\n", "    [3.3569, 2.8998],  # Load 3\n", "    [27.6723, 24.2998]  # Load 3\n", "]\n", "\n", "# Convert lists to NumPy arrays\n", "data_a = np.array(data_a)\n", "\n", "# Transform delay as per the formula\n", "transformed_data_a = data_a / 100 * (SSD_DELAY + CXL_DELAY + DDR_DELAY) + (1 - data_a / 100) * (DDR_DELAY + CXL_DELAY)\n", "\n", "# Calculate the ratio of the second value to the first value for each load\n", "ratios = data_a[:, 1] / data_a[:, 0]\n", "\n", "# Print the transformed data and the ratios\n", "print(\"Transformed Data A:\")\n", "print(transformed_data_a)\n", "print(\"\\nRatios of the second value to the first value for each load:\")\n", "print(1-ratios)\n"]}], "metadata": {"kernelspec": {"display_name": "torch_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}