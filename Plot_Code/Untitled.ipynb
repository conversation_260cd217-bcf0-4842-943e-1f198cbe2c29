{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 假设有几组数据\n", "data = [np.random.rand(50)*100 for _ in range(5)]\n", "\n", "# 负载名字（横轴标签）\n", "load_names = ['BERT', 'Page Rank', 'Radiosity', 'XZ', 'YCSB']\n", "\n", "# 绘制箱形图\n", "plt.boxplot(data)\n", "\n", "# 设置横轴为负载名字\n", "plt.xticks(np.arange(1, len(load_names) + 1), load_names)\n", "\n", "plt.xlabel('Load Name')\n", "plt.ylabel('Performance per $')\n", "plt.title('Performance per $ (x over DRAM-only)')\n", "\n", "# 显示图表\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 662.4x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "category_names = ['Strongly disagree', 'Disagree',\n", "                  'Neither agree nor disagree', 'Agree', 'Strongly agree']\n", "results = {\n", "    'Question 1': [10, 15, 17, 32, 26],\n", "    'Question 2': [26, 22, 29, 10, 13],\n", "    'Question 3': [35, 37, 7, 2, 19],\n", "    'Question 4': [32, 11, 9, 15, 33],\n", "    'Question 5': [21, 29, 5, 5, 40],\n", "    'Question 6': [8, 19, 5, 30, 38]\n", "}\n", "\n", "\n", "def survey(results, category_names):\n", "    \"\"\"\n", "    Parameters\n", "    ----------\n", "    results : dict\n", "        A mapping from question labels to a list of answers per category.\n", "        It is assumed all lists contain the same number of entries and that\n", "        it matches the length of *category_names*.\n", "    category_names : list of str\n", "        The category labels.\n", "    \"\"\"\n", "    labels = list(results.keys())\n", "    data = np.array(list(results.values()))\n", "    data_cum = data.cumsum(axis=1)\n", "    category_colors = plt.get_cmap('RdYlGn')(\n", "        np.linspace(0.15, 0.85, data.shape[1]))\n", "\n", "    fig, ax = plt.subplots(figsize=(9.2, 5))\n", "    ax.invert_yaxis()\n", "    ax.xaxis.set_visible(False)\n", "    ax.set_xlim(0, np.sum(data, axis=1).max())\n", "\n", "    for i, (colname, color) in enumerate(zip(category_names, category_colors)):\n", "        widths = data[:, i]\n", "        starts = data_cum[:, i] - widths\n", "        ax.barh(labels, widths, left=starts, height=0.5,\n", "                label=colname, color=color)\n", "        xcenters = starts + widths / 2\n", "\n", "        r, g, b, _ = color\n", "        text_color = 'white' if r * g * b < 0.5 else 'darkgrey'\n", "        for y, (x, c) in enumerate(zip(xcenters, widths)):\n", "            ax.text(x, y, str(int(c)), ha='center', va='center',\n", "                    color=text_color)\n", "    ax.legend(ncol=len(category_names), bbox_to_anchor=(0, 1),\n", "              loc='lower left', fontsize='small')\n", "\n", "    return fig, ax\n", "\n", "\n", "survey(results, category_names)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.2.2\n"]}], "source": ["import matplotlib\n", "print(matplotlib.__version__)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean: 0.4961111111111111\n", "Variance: 0.03370254320987654\n", "Standard Deviation: 0.18358252424965874\n", "\n", "Using statistics module:\n", "Mean: 0.4961111111111111\n", "Variance: 0.03370254320987654\n", "Standard Deviation: 0.18358252424965874\n"]}], "source": ["import math\n", "import statistics\n", "\n", "def calculate_mean(data):\n", "    return sum(data) / len(data)\n", "\n", "def calculate_variance(data, mean):\n", "    return sum((x - mean) ** 2 for x in data) / len(data)\n", "\n", "def calculate_standard_deviation(variance):\n", "    return math.sqrt(variance)\n", "\n", "# 示例数据\n", "# data = [0.697, 0.774, 0.634, 0.608, 0.556,0.403,0.481,0.437]\n", "# data = [0.46,0.376,0.264,0.318,0.215,0.237,0.149,0.211]\n", "data = [0.666,0.243,0.245,0.343,0.639,0.657,0.360,0.593,0.719]\n", "# data = [6,5.92,5.58,5.92]\n", "# 计算均值\n", "mean = calculate_mean(data)\n", "\n", "# 计算方差\n", "variance = calculate_variance(data, mean)\n", "\n", "# 计算标准差\n", "standard_deviation = calculate_standard_deviation(variance)\n", "\n", "print(f\"Mean: {mean}\")\n", "print(f\"Variance: {variance}\")\n", "print(f\"Standard Deviation: {standard_deviation}\")\n", "\n", "# 使用 statistics 模块验证结果\n", "mean_stat = statistics.mean(data)\n", "variance_stat = statistics.pvariance(data)  # 使用总体方差\n", "standard_deviation_stat = statistics.pstdev(data)  # 使用总体标准差\n", "\n", "print(\"\\nUsing statistics module:\")\n", "print(f\"Mean: {mean_stat}\")\n", "print(f\"Variance: {variance_stat}\")\n", "print(f\"Standard Deviation: {standard_deviation_stat}\")\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean: 0.4961111111111111\n", "Population Variance: 0.03370254320987654\n", "Sample Variance: 0.03791536111111111\n"]}], "source": ["import statistics\n", "\n", "# 示例数据\n", "data = [0.666,0.243,0.245,0.343,0.639,0.657,0.360,0.593,0.719]\n", "\n", "# 计算均值\n", "mean = statistics.mean(data)\n", "\n", "# 计算总体方差\n", "variance_population = statistics.pvariance(data)\n", "\n", "# 计算样本方差\n", "variance_sample = statistics.variance(data)\n", "\n", "print(f\"Mean: {mean}\")\n", "print(f\"Population Variance: {variance_population}\")\n", "print(f\"Sample Variance: {variance_sample}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 4}