def add_offset_to_trace(input_file, output_file, offset):
    with open(input_file, 'r') as infile, open(output_file, 'w') as outfile:
        for line in infile:
            parts = line.split()
            if len(parts) > 2:
                # 将第三列的地址转换为整数并加上偏置
                address = int(parts[2]) + offset
                # 用新的地址替换原有地址
                parts[2] = str(address)
                # 将修改后的行写入输出文件
                outfile.write(' '.join(parts) + '\n')

if __name__ == "__main__":
    file1 = 'pr_t3.pagetrace'
    file2 = 'spec508_namd.pagetrace'
    output_file1 = 'pr_t3_off.pagetrace'
    output_file2 = 'spec508_namd_off.pagetrace'
    
    offset1 = 1000000  # 为第一个文件添加的偏置
    offset2 = 2000000  # 为第二个文件添加的偏置

    add_offset_to_trace(file1, output_file1, offset1)
    add_offset_to_trace(file2, output_file2, offset2)
