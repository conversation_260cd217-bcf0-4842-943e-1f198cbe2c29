#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <math.h>  // 新增，用于计算指数函数
#include <float.h> // For DBL_MAX
#include <limits.h> // For INT_MAX

// Switch to enable/disable segmented reporting
// Set to 1 to enable, 0 to disable
#define ENABLE_SEGMENTED_REPORTING 1

// TODO: User might want to change this to e.g., "trace/cac_dp_cc_sssp_repeat4_test"
#define BASE_FILENAME "trace/gap_bc_new_16CNs"
#define INPUT_FILENAME BASE_FILENAME ".pagetrace"
#define LOG_FILENAME BASE_FILENAME ".log"
#define CACHE_SIZE (1L * 64 * 1024 * 1024 / 4096)
#define SSD_DELAY 75000
#define CXL_DELAY 100
#define DDR_DELAY 46
#define PREFETCH_N 4
#define LINES_PER_SET 4
#define NUM_SETS (CACHE_SIZE / LINES_PER_SET)
#define NUM_MACHINES 16  // 定义机器数量

// RRIP constants
#define MAX_RRPV 3   // Maximum Re-Reference Prediction Value
#define INIT_RRPV 3  // Initial RRPV for new cache lines
#define LONG_RRPV 0  // RRPV for long re-reference intervals

// LRFU constants
#define LAMBDA 0.001  // LRFU权重参数，0表示纯LFU，1表示纯LRU
#define TIME_INTERVAL 1.0  // 时间单位

typedef struct CachePage {
  unsigned long long page_number;
  int valid;
  int lru_counter;
  int fifo_order;
  int load_id;
  int accessed;
  int rrpv;  // 新增字段，用于RRIP算法
  int access_frequency;  // 新增字段，用于LFU算法
  double crf_value;  // 新增字段，用于LRFU算法的CRF值
  long last_access_time;  // 新增字段，记录最后访问时间
} CachePage;

CachePage cache[NUM_SETS][LINES_PER_SET];
int fifo_counter[NUM_SETS] = {0};
int strategy = 6;  // 6表示使用LRFU策略
int prefetch_enabled = 0;
int log_enabled = 0;
int separate_miss_rate = 1;
long current_time = 0;  // 全局时间计数器

// --- Segment and Overall Statistics Variables ---
#if ENABLE_SEGMENTED_REPORTING == 1
// Overall statistics accumulators
long long overall_total_delay_all_segments = 0;
int overall_eviction_count_all_segments = 0;
int overall_non_accessed_eviction_count_all_segments = 0;
int overall_access_counts_all_segments[NUM_MACHINES + 1] = {0};
int overall_total_memory_accesses_all_segments = 0;
int overall_hits_all_segments[NUM_MACHINES + 1] = {0};
int overall_total_accesses_all_segments[NUM_MACHINES + 1] = {0};
int overall_conflict_misses_all_segments[NUM_MACHINES + 1] = {0};
int overall_inter_load_conflict_misses_all_segments[NUM_MACHINES + 1] = {0};
long long overall_delay_per_load_all_segments[NUM_MACHINES + 1] = {0};
#endif

// File-static counters for the current segment's evictions
// These are updated by evict_and_load_modified
static int current_segment_eviction_count_static = 0;
static int current_segment_non_accessed_eviction_count_static = 0;

// Original global counters - these will be effectively replaced by segment/overall logic
// int eviction_count = 0; // Replaced
// int non_accessed_eviction_count = 0; // Replaced
// int access_counts[NUM_MACHINES + 1] = {0}; // Replaced
// int total_memory_accesses = 0; // Replaced

// 计算LRFU的CRF值 (This function updates CRF upon an access)
double calculate_crf_on_access(double old_crf, long time_diff) {
    // F(x) = (1/2)^(λx)，其中x是时间差
    double decay = pow(0.5, LAMBDA * time_diff * TIME_INTERVAL);
    return old_crf * decay + 1.0; // Recursive formula: C_new(B) = C_init + F(t_c - t_last_ref) * C_old(B)
}

// Calculates the comparable CRF value for victim selection (decay only)
double get_comparable_crf(double stored_crf_at_last_access, long time_diff_since_last_access) {
    double decay = pow(0.5, LAMBDA * time_diff_since_last_access * TIME_INTERVAL);
    return stored_crf_at_last_access * decay;
}


void init() {
  for (int i = 0; i < NUM_SETS; i++) {
    for (int j = 0; j < LINES_PER_SET; j++) {
      cache[i][j].valid = 0;
      cache[i][j].lru_counter = 0;
      cache[i][j].fifo_order = 0;
      cache[i][j].load_id = -1;
      cache[i][j].accessed = 0;
      cache[i][j].rrpv = INIT_RRPV;  // 初始化RRPV
      cache[i][j].access_frequency = 0;  // 初始化访问频率
      cache[i][j].crf_value = 0.0;  // 初始化CRF值
      cache[i][j].last_access_time = 0;  // 初始化最后访问时间
    }
  }
  // eviction_count = 0; // Handled by segment logic
  // non_accessed_eviction_count = 0; // Handled by segment logic
  current_segment_eviction_count_static = 0;
  current_segment_non_accessed_eviction_count_static = 0;
  current_time = 0;  // 初始化全局时间
  // Resetting overall counters is done at the start of simulate_accesses if segmented reporting is on
}

int find_in_cache(unsigned long long page_number) {
  int set_index = page_number % NUM_SETS;
  
  current_time++; // Update global time on each access attempt (hit or miss path)
  
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (cache[set_index][i].valid &&
        cache[set_index][i].page_number == page_number) {
      
      if (strategy == 6) {  // LRFU策略
        long time_diff = current_time - cache[set_index][i].last_access_time;
        cache[set_index][i].crf_value = calculate_crf_on_access(cache[set_index][i].crf_value, time_diff);
        cache[set_index][i].last_access_time = current_time;
      } else {
        cache[set_index][i].accessed = 1;
        cache[set_index][i].rrpv = LONG_RRPV;
        cache[set_index][i].access_frequency++;
      }
      return i;
    }
  }
  return -1;
}

void update_lru(int set_index, int accessed_index) {
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (cache[set_index][i].valid) {
      cache[set_index][i].lru_counter++;
    }
  }
  cache[set_index][accessed_index].lru_counter = 0;
}

void evict_and_load_modified(int set_index, int load_index,
                             unsigned long long page_number, int load_id) {
  if (cache[set_index][load_index].valid) {
    current_segment_eviction_count_static++;
    if (!cache[set_index][load_index].accessed) {
      current_segment_non_accessed_eviction_count_static++;
    }
  }
  // 更新缓存行信息
  cache[set_index][load_index].page_number = page_number;
  cache[set_index][load_index].valid = 1;
  cache[set_index][load_index].load_id = load_id;
  cache[set_index][load_index].accessed = 0;
  cache[set_index][load_index].rrpv = INIT_RRPV;
  cache[set_index][load_index].access_frequency = 1;
  
  // LRFU特有的初始化
  if (strategy == 6) { // LRFU
    cache[set_index][load_index].crf_value = 1.0;  // Initial CRF for a new page
    cache[set_index][load_index].last_access_time = current_time;
  }
}

// LRFU缓存替换策略
void load_to_cache_lrfu_modified(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  int victim_index = 0;
  int found_empty_slot = 0;

  // First, check for an empty slot
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid) {
      victim_index = i;
      found_empty_slot = 1;
      break;
    }
  }

  if (!found_empty_slot) { // If no empty slot, find the one with minimum CRF
    double min_comparable_crf = DBL_MAX; // Use DBL_MAX from float.h
    victim_index = 0;
    for (int i = 0; i < LINES_PER_SET; i++) {
      long time_diff = current_time - cache[set_index][i].last_access_time;
      // For victim selection, we need to compare their current "decayed" CRF values
      double comparable_crf = get_comparable_crf(cache[set_index][i].crf_value, time_diff);
      if (comparable_crf < min_comparable_crf) {
        min_comparable_crf = comparable_crf;
        victim_index = i;
      }
    }
  }
  evict_and_load_modified(set_index, victim_index, page_number, load_id);
  // fifo_order is not directly part of LRFU eviction logic itself.
  // LRFU specific updates for the new page (crf_value, last_access_time) are handled in evict_and_load_modified.
}

void load_to_cache_lru_modified(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  int lru_index = 0;
  int max_counter = -1;
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid) {
      lru_index = i;
      break;
    }
    if (cache[set_index][i].lru_counter > max_counter) {
      max_counter = cache[set_index][i].lru_counter;
      lru_index = i;
    }
  }
  evict_and_load_modified(set_index, lru_index, page_number, load_id);
  update_lru(set_index, lru_index);
}

void load_to_cache_fifo_modified(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  int fifo_index = 0;
  int oldest_order = INT_MAX; // Use INT_MAX from limits.h
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid) {
      fifo_index = i;
      break;
    }
    if (cache[set_index][i].fifo_order < oldest_order) {
      oldest_order = cache[set_index][i].fifo_order;
      fifo_index = i;
    }
  }
  evict_and_load_modified(set_index, fifo_index, page_number, load_id);
  cache[set_index][fifo_index].fifo_order = fifo_counter[set_index]++;
}

void load_to_cache_rrip_modified(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  while (1) {
    for (int i = 0; i < LINES_PER_SET; i++) {
      if (!cache[set_index][i].valid || cache[set_index][i].rrpv == MAX_RRPV) {
        evict_and_load_modified(set_index, i, page_number, load_id);
        return;
      }
    }
    for (int i = 0; i < LINES_PER_SET; i++) {
      cache[set_index][i].rrpv++;
    }
  }
}

void load_to_cache_lfu_modified(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  int lfu_index = 0;
  int min_frequency = INT_MAX; // Use INT_MAX
  
  // 首先检查是否有空行
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid) {
      lfu_index = i;
      break; // Found empty slot
    }
    // 找到访问频率最低的行
    if (cache[set_index][i].access_frequency < min_frequency) {
      min_frequency = cache[set_index][i].access_frequency;
      lfu_index = i;
    }
    // 如果访问频率相同，优先选择先进入缓存的行 (FIFO tie-breaking)
    else if (cache[set_index][i].access_frequency == min_frequency) {
      if (cache[set_index][i].fifo_order < cache[set_index][lfu_index].fifo_order) {
        lfu_index = i;
      }
    }
  }
  
  evict_and_load_modified(set_index, lfu_index, page_number, load_id);
  cache[set_index][lfu_index].fifo_order = fifo_counter[set_index]++; // Update FIFO order for LFU tie-breaking
  // access_frequency is set to 1 in evict_and_load_modified
}

void prefetch_modified(unsigned long long page_number) {
  if (!prefetch_enabled) return;
  for (int i = 1; i <= PREFETCH_N; i++) {
    unsigned long long next_page = page_number + i;
    if (find_in_cache(next_page) == -1) { // find_in_cache is fine as is
      if (strategy == 6) {
        load_to_cache_lrfu_modified(next_page, -1); // -1 for load_id indicates prefetch
      } else if (strategy == 3) {
        load_to_cache_lfu_modified(next_page, -1);
      } else if (strategy == 2) {
        load_to_cache_rrip_modified(next_page, -1);
      } else if (strategy == 1) {
        load_to_cache_lru_modified(next_page, -1);
      } else if (strategy == 0) {
        load_to_cache_fifo_modified(next_page, -1);
      }
    }
  }
}


// --- Helper functions for segmented reporting ---
void reset_segment_counters(
    int hits[], int total_accesses_arr[],
    long long delay_per_load_arr[], long long *current_total_delay_ptr,
    int conflict_misses_arr[], int inter_load_conflict_misses_arr[],
    int *eviction_count_static_ptr, int *non_accessed_eviction_count_static_ptr, // Pointers to static segment counters
    int access_counts_arr[], int *current_total_memory_accesses_ptr,
    int num_machines_val) {

    memset(hits, 0, (num_machines_val + 1) * sizeof(int));
    memset(total_accesses_arr, 0, (num_machines_val + 1) * sizeof(int));
    memset(delay_per_load_arr, 0, (num_machines_val + 1) * sizeof(long long));
    *current_total_delay_ptr = 0;
    memset(conflict_misses_arr, 0, (num_machines_val + 1) * sizeof(int));
    memset(inter_load_conflict_misses_arr, 0, (num_machines_val + 1) * sizeof(int));
    
    // Reset the static counters for eviction via pointers
    *eviction_count_static_ptr = 0;
    *non_accessed_eviction_count_static_ptr = 0;
    
    memset(access_counts_arr, 0, (num_machines_val + 1) * sizeof(int));
    *current_total_memory_accesses_ptr = 0;
}

void print_and_accumulate_segment_stats(
    int segment_number_val,
    int current_segment_hits[], int current_segment_total_accesses[],
    long long current_segment_delay_per_load[], long long current_segment_total_delay_val,
    int current_segment_conflict_misses[], int current_segment_inter_load_conflict_misses[],
    int segment_eviction_count_val, int segment_non_accessed_eviction_count_val, // Direct values for this segment
    int current_segment_access_counts[], int current_segment_total_memory_accesses_val,
    int separate_miss_rate_flag, int num_machines_val) {

    printf("\n--- Statistics for Segment %d ---\n", segment_number_val);

    int seg_total_hits_for_rate = 0;
    int seg_total_access_for_rate = 0;
    for (int i = 1; i <= num_machines_val; i++) {
        seg_total_hits_for_rate += current_segment_hits[i];
        seg_total_access_for_rate += current_segment_total_accesses[i];
    }

    if (seg_total_access_for_rate > 0) {
        double overall_miss_rate_segment = 1.0 - ((double)seg_total_hits_for_rate / seg_total_access_for_rate);
        printf("Segment %d Overall miss rate: %.4f%%\n", segment_number_val, overall_miss_rate_segment * 100);
    } else {
        printf("Segment %d Overall miss rate: N/A (no accesses)\n", segment_number_val);
    }

    if (separate_miss_rate_flag) {
        for (int i = 1; i <= num_machines_val; i++) {
            if (current_segment_total_accesses[i] > 0) {
                double miss_rate = 1.0 - ((double)current_segment_hits[i] / current_segment_total_accesses[i]);
                printf("  Segment %d - Load %d: Miss Rate: %.4f%%, Delay: %lld ns, Conflict Miss Rate: %.4f%%, Inter-Load Conflict Miss Rate: %.4f%%\n",
                       segment_number_val, i, miss_rate * 100, current_segment_delay_per_load[i],
                       ((double)current_segment_conflict_misses[i] / current_segment_total_accesses[i]) * 100,
                       ((double)current_segment_inter_load_conflict_misses[i] / current_segment_total_accesses[i]) * 100);
            } else {
                 printf("  Segment %d - Load %d: No accesses\n", segment_number_val, i);
            }
        }
    }
    printf("Segment %d Total delay: %lld ns\n", segment_number_val, current_segment_total_delay_val);
    printf("Segment %d Total evictions: %d\n", segment_number_val, segment_eviction_count_val);
    printf("Segment %d Evictions without re-access: %d\n", segment_number_val, segment_non_accessed_eviction_count_val);
    for (int i = 1; i <= num_machines_val; i++) {
        printf("  Segment %d Access count for node %d: %d\n", segment_number_val, i, current_segment_access_counts[i]);
    }
    printf("Segment %d Total memory accesses: %d\n", segment_number_val, current_segment_total_memory_accesses_val);

#if ENABLE_SEGMENTED_REPORTING == 1
    overall_total_delay_all_segments += current_segment_total_delay_val;
    overall_eviction_count_all_segments += segment_eviction_count_val;
    overall_non_accessed_eviction_count_all_segments += segment_non_accessed_eviction_count_val;
    overall_total_memory_accesses_all_segments += current_segment_total_memory_accesses_val;

    for (int i = 1; i <= num_machines_val; i++) {
        overall_hits_all_segments[i] += current_segment_hits[i];
        overall_total_accesses_all_segments[i] += current_segment_total_accesses[i];
        overall_delay_per_load_all_segments[i] += current_segment_delay_per_load[i];
        overall_conflict_misses_all_segments[i] += current_segment_conflict_misses[i];
        overall_inter_load_conflict_misses_all_segments[i] += current_segment_inter_load_conflict_misses[i];
        overall_access_counts_all_segments[i] += current_segment_access_counts[i];
    }
#endif
}
// --- End of Helper functions ---

void simulate_accesses(const char *input_filename, const char *log_filename) {
  FILE *input_file = fopen(input_filename, "r");
  if (!input_file) {
    fprintf(stderr, "Failed to open file %s\n", input_filename);
    return;
  }
  FILE *log_file = NULL;
  if (log_enabled) {
    log_file = fopen(log_filename, "w");
    if (!log_file) {
      fprintf(stderr, "Failed to open log file %s\n", log_filename);
      fclose(input_file);
      return;
    }
  }
  // --- Segment specific statistics arrays/variables ---
  int current_segment_hits[NUM_MACHINES + 1];
  int current_segment_total_accesses[NUM_MACHINES + 1];
  long long current_segment_delay_per_load[NUM_MACHINES + 1];
  long long current_segment_total_delay = 0;
  int current_segment_conflict_misses[NUM_MACHINES + 1];
  int current_segment_inter_load_conflict_misses[NUM_MACHINES + 1];
  // current_segment_eviction_count_static and current_segment_non_accessed_eviction_count_static are used directly
  int current_segment_access_counts[NUM_MACHINES + 1];
  int current_segment_total_memory_accesses = 0;

  reset_segment_counters(current_segment_hits, current_segment_total_accesses,
                         current_segment_delay_per_load, &current_segment_total_delay,
                         current_segment_conflict_misses, current_segment_inter_load_conflict_misses,
                         &current_segment_eviction_count_static, &current_segment_non_accessed_eviction_count_static,
                         current_segment_access_counts, &current_segment_total_memory_accesses, NUM_MACHINES);

#if ENABLE_SEGMENTED_REPORTING == 1
    // Initialize overall statistics
    overall_total_delay_all_segments = 0;
    overall_eviction_count_all_segments = 0;
    overall_non_accessed_eviction_count_all_segments = 0;
    overall_total_memory_accesses_all_segments = 0;
    memset(overall_hits_all_segments, 0, sizeof(overall_hits_all_segments));
    memset(overall_total_accesses_all_segments, 0, sizeof(overall_total_accesses_all_segments));
    memset(overall_delay_per_load_all_segments, 0, sizeof(overall_delay_per_load_all_segments));
    memset(overall_conflict_misses_all_segments, 0, sizeof(overall_conflict_misses_all_segments));
    memset(overall_inter_load_conflict_misses_all_segments, 0, sizeof(overall_inter_load_conflict_misses_all_segments));
    memset(overall_access_counts_all_segments, 0, sizeof(overall_access_counts_all_segments));
    int segment_number = 1;
#endif

  char line_buffer[256];
  long timestamp, dummy1, dummy2, rw_flag, load_id;
  unsigned long long page_number;

  while (fgets(line_buffer, sizeof(line_buffer), input_file) != NULL) {
    // Trim newline characters from line_buffer for strcmp
    line_buffer[strcspn(line_buffer, "\r\n")] = 0;

#if ENABLE_SEGMENTED_REPORTING == 1
    if (strcmp(line_buffer, "repeat") == 0) {
        print_and_accumulate_segment_stats(
            segment_number, current_segment_hits, current_segment_total_accesses,
            current_segment_delay_per_load, current_segment_total_delay,
            current_segment_conflict_misses, current_segment_inter_load_conflict_misses,
            current_segment_eviction_count_static, current_segment_non_accessed_eviction_count_static,
            current_segment_access_counts, current_segment_total_memory_accesses,
            separate_miss_rate, NUM_MACHINES);

        reset_segment_counters(current_segment_hits, current_segment_total_accesses,
                               current_segment_delay_per_load, &current_segment_total_delay,
                               current_segment_conflict_misses, current_segment_inter_load_conflict_misses,
                               &current_segment_eviction_count_static, &current_segment_non_accessed_eviction_count_static,
                               current_segment_access_counts, &current_segment_total_memory_accesses, NUM_MACHINES);
        segment_number++;
        if (log_enabled && log_file) {
            fprintf(log_file, "--- End of Segment %d, Start of Segment %d ---\n", segment_number - 2, segment_number -1 ); // Corrected segment numbers for log
        }
        continue; // Move to the next line after processing "repeat"
    }
#endif
    // Attempt to parse the line as a trace entry
    if (sscanf(line_buffer, "%ld %ld %llu %ld %ld %ld", &timestamp, &dummy1,
               &page_number, &dummy2, &rw_flag, &load_id) == 6) {
      
      if (load_id <= 0 || load_id > NUM_MACHINES) continue; // Validate load_id

      current_segment_total_accesses[load_id]++;
      current_segment_access_counts[load_id]++;
      current_segment_total_memory_accesses++;

      int set_index = page_number % NUM_SETS;
      int cache_index = find_in_cache(page_number); // find_in_cache updates global current_time

      if (cache_index != -1) { // Cache Hit
        current_segment_hits[load_id]++;
        long long hit_delay = (DDR_DELAY + CXL_DELAY);
        current_segment_total_delay += hit_delay;
        current_segment_delay_per_load[load_id] += hit_delay;
        if (strategy == 1) { // LRU
          update_lru(set_index, cache_index);
        }
        if (log_enabled && log_file) {
          fprintf(log_file, "Timestamp %ld: Page %llu (Load %ld), Cache hit\n", timestamp, page_number, load_id);
        }
      } else { // Cache Miss
        long long miss_delay = (SSD_DELAY + CXL_DELAY + DDR_DELAY);
        current_segment_total_delay += miss_delay;
        current_segment_delay_per_load[load_id] += miss_delay;

        int is_conflict_miss = 1;
        for (int i = 0; i < LINES_PER_SET; i++) {
          if (!cache[set_index][i].valid) {
            is_conflict_miss = 0;
            break;
          }
        }
        if (is_conflict_miss) {
          current_segment_conflict_misses[load_id]++;
          int inter_load_conflict = 0;
          for (int i = 0; i < LINES_PER_SET; i++) {
            if (cache[set_index][i].load_id != load_id) {
              inter_load_conflict = 1;
              break;
            }
          }
          if (inter_load_conflict) {
            current_segment_inter_load_conflict_misses[load_id]++;
          }
        }

        if (strategy == 6) load_to_cache_lrfu_modified(page_number, load_id);
        else if (strategy == 3) load_to_cache_lfu_modified(page_number, load_id);
        else if (strategy == 2) load_to_cache_rrip_modified(page_number, load_id);
        else if (strategy == 1) load_to_cache_lru_modified(page_number, load_id);
        else if (strategy == 0) load_to_cache_fifo_modified(page_number, load_id);

        if (log_enabled && log_file) {
          fprintf(log_file, "Timestamp %ld: Page %llu (Load %ld), Cache miss\n", timestamp, page_number, load_id);
        }
        prefetch_modified(page_number);
      }
    } else {
        // Log malformed lines if not empty and not "repeat" (if repeat is handled above)
        if (strlen(line_buffer) > 0 ) { // Avoid logging empty lines if "repeat" was the only content
            #if ENABLE_SEGMENTED_REPORTING == 0
            // If segmented reporting is off, "repeat" is an unknown line
            if (strcmp(line_buffer, "repeat") != 0) {
                 if (log_enabled && log_file) fprintf(log_file, "Warning: Malformed line: %s\n", line_buffer);
            }
            #else
            // If segmented reporting is on, "repeat" is handled. Any other non-parsable line is malformed.
            // (strcmp(line_buffer, "repeat") != 0) is implicitly true here because of the continue above for "repeat"
            if (log_enabled && log_file) fprintf(log_file, "Warning: Malformed line or unhandled keyword: %s\n", line_buffer);
            #endif
        }
    }
  }

  fclose(input_file);
  if (log_enabled && log_file) {
    fclose(log_file);
  }

#if ENABLE_SEGMENTED_REPORTING == 1
  // Print stats for the last segment (or the only segment if no "repeat" lines)
  print_and_accumulate_segment_stats(
      segment_number, current_segment_hits, current_segment_total_accesses,
      current_segment_delay_per_load, current_segment_total_delay,
      current_segment_conflict_misses, current_segment_inter_load_conflict_misses,
      current_segment_eviction_count_static, current_segment_non_accessed_eviction_count_static,
      current_segment_access_counts, current_segment_total_memory_accesses,
      separate_miss_rate, NUM_MACHINES);

  // Print overall statistics for all segments
  printf("\n========== Overall Statistics (All Segments Combined) ==========\n");
  int total_hits_all_segments_combined = 0;
  int total_access_all_segments_combined = 0;
  for (int i = 1; i <= NUM_MACHINES; i++) {
      total_hits_all_segments_combined += overall_hits_all_segments[i];
      total_access_all_segments_combined += overall_total_accesses_all_segments[i];
  }
   if (total_access_all_segments_combined > 0) {
      double overall_miss_rate_final = 1.0 - ((double)total_hits_all_segments_combined / total_access_all_segments_combined);
      printf("Overall miss rate (All Segments): %.4f%%\n", overall_miss_rate_final * 100);
  } else {
      printf("Overall miss rate (All Segments): N/A (no accesses)\n");
  }

  if (separate_miss_rate) {
      for (int i = 1; i <= NUM_MACHINES; i++) {
          if (overall_total_accesses_all_segments[i] > 0) {
              double miss_rate = 1.0 - ((double)overall_hits_all_segments[i] / overall_total_accesses_all_segments[i]);
              printf("  Overall - Load %d: Miss Rate: %.4f%%, Delay: %lld ns, Conflict Miss Rate: %.4f%%, Inter-Load Conflict Miss Rate: %.4f%%\n",
                     i, miss_rate * 100, overall_delay_per_load_all_segments[i],
                     ((double)overall_conflict_misses_all_segments[i] / overall_total_accesses_all_segments[i]) * 100,
                     ((double)overall_inter_load_conflict_misses_all_segments[i] / overall_total_accesses_all_segments[i]) * 100);
          } else {
              printf("  Overall - Load %d: No accesses\n", i);
          }
      }
  }
  printf("Overall Total delay (All Segments): %lld ns\n", overall_total_delay_all_segments);
  printf("Overall Total evictions (All Segments): %d\n", overall_eviction_count_all_segments);
  printf("Overall Evictions without re-access (All Segments): %d\n", overall_non_accessed_eviction_count_all_segments);
  for (int i = 1; i <= NUM_MACHINES; i++) {
      printf("  Overall Access count for node %d: %d\n", i, overall_access_counts_all_segments[i]);
  }
  printf("Overall Total memory accesses (All Segments): %d\n", overall_total_memory_accesses_all_segments);

#else // Original behavior if ENABLE_SEGMENTED_REPORTING is 0
  // The current_segment_ variables will hold the totals if segmented reporting is off
  int total_hits_orig = 0;
  int total_access_orig = 0;
  for (int i = 1; i <= NUM_MACHINES; i++) {
    total_hits_orig += current_segment_hits[i]; // Use current_segment_hits which holds all data
    total_access_orig += current_segment_total_accesses[i]; // Use current_segment_total_accesses
  }
  if (total_access_orig > 0) {
    double overall_miss_rate_orig = 1.0 - ((double)total_hits_orig / total_access_orig);
    printf("Overall miss rate: %.4f%%\n", overall_miss_rate_orig * 100);
  } else {
     printf("Overall miss rate: N/A (no accesses)\n");
  }

  if (separate_miss_rate) {
    for (int i = 1; i <= NUM_MACHINES; i++) {
      if (current_segment_total_accesses[i] > 0) {
        double miss_rate = 1.0 - ((double)current_segment_hits[i] / current_segment_total_accesses[i]);
        printf("Miss rate for load %d: %.4f%%\n", i, miss_rate * 100);
        printf("Total delay for load %d: %lld ns\n", i, current_segment_delay_per_load[i]);
        double conflict_miss_rate = (double)current_segment_conflict_misses[i] / current_segment_total_accesses[i];
        double inter_load_conflict_miss_rate = (double)current_segment_inter_load_conflict_misses[i] / current_segment_total_accesses[i];
        printf("Conflict miss rate for load %d: %.4f%%\n", i, conflict_miss_rate * 100);
        printf("Inter-load conflict miss rate for load %d: %.4f%%\n", i, inter_load_conflict_miss_rate * 100);
      } else {
        printf("Miss rate for load %d: No accesses\n", i); // Provide info for loads with no accesses
      }
    }
  }
  printf("Total delay: %lld ns\n", current_segment_total_delay);
  printf("Total evictions: %d\n", current_segment_eviction_count_static);
  printf("Evictions without re-access: %d\n", current_segment_non_accessed_eviction_count_static);
  for (int i = 1; i <= NUM_MACHINES; i++) {
    printf("Access count for node %d: %d\n", i, current_segment_access_counts[i]);
  }
  printf("Total memory accesses: %d\n", current_segment_total_memory_accesses);
#endif
  
  // 输出LRFU参数
  if (strategy == 6) { // LRFU
    printf("\nLRFU Parameters:\n");
    printf("Lambda: %.3f\n", LAMBDA); // Adjusted precision for Lambda
    printf("Time Interval: %.1f\n", TIME_INTERVAL); // Adjusted precision for Time Interval
  }
}

int main() {
  init();
  clock_t start_time = clock();
  simulate_accesses(INPUT_FILENAME, LOG_FILENAME);
  clock_t end_time = clock();
  double duration = (double)(end_time - start_time) / CLOCKS_PER_SEC;
  printf("Simulation time: %.2f seconds\n", duration);
  return 0;
}
