# 假设你的数据文件名为 'memory_trace.txt'
filename = 'pr_t3_CN6.pagetrace'

# 用于存储已访问的内存地址
visited_addresses = set()
total_accesses = 0
hit_count = 0

# 读取文件并统计内存命中
with open(filename, 'r') as file:
    for line in file:
        parts = line.split()
        if len(parts) >= 3:
            address = int(parts[2])  # 第三个字段是内存地址
            total_accesses += 1

            # 检查是否命中
            if address in visited_addresses:
                hit_count += 1
            else:
                visited_addresses.add(address)

# 计算命中比例
hit_proportion = hit_count / total_accesses

# 打印结果
print(f"内存最低miss比例: {100-100*hit_proportion:.4f}%")
