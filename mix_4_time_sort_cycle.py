# 其中一个文件如果真正读完了，那么给他一个真正读完的标志位，再就
# 让他再从头开始读，而对于第一列的到达时间，用重新开始的到达时间加上刚才读完时最后的到达时间作为新的到达时间，直到四个文件真正读完了，四个标记为全满足了，就停止读取
import heapq

def mix_files_by_arrival_time(files, output_file):
    # 打开所有输入文件
    file_handles = [open(file, 'r') for file in files]

    # 创建一个优先队列（最小堆）
    min_heap = []
    last_arrival_times = [0] * len(files)  # 记录每个文件的最后到达时间
    files_finished = [False] * len(files)  # 记录每个文件是否已处理完
    files_restarted = [False] * len(files)  # 记录每个文件是否已重新开始读取

    # 初始化堆，将每个文件的第一行放入堆中
    for file_index, fh in enumerate(file_handles):
        line = fh.readline().strip()
        if line:
            parts = line.split()
            arrival_time = int(parts[0])  # 假设到达时间在第一列
            heapq.heappush(min_heap, (arrival_time, line, file_index))

    # 打开输出文件
    with open(output_file, 'w') as out:
        # 不断从堆中取出最小的元素
        while min_heap:
            arrival_time, line, file_index = heapq.heappop(min_heap)
            out.write(f'{line} {file_index + 1}\n')

            # 从同一个文件读取下一行
            next_line = file_handles[file_index].readline().strip()
            if next_line:
                parts = next_line.split()
                next_arrival_time = int(parts[0]) + last_arrival_times[file_index]
                heapq.heappush(min_heap, (next_arrival_time, next_line, file_index))
            else:
                # 标记该文件已处理完
                if not files_restarted[file_index]:
                    files_finished[file_index] = True
                    files_restarted[file_index] = True
                    last_arrival_times[file_index] = arrival_time
                    # 重新打开文件并从头开始读取
                    file_handles[file_index].seek(0)
                else:
                    # 如果文件已经重新开始读取过，标记为完全处理完
                    files_finished[file_index] = True

            # 如果所有文件都已真正处理完，则停止处理
            if all(files_finished):
                break

    # 关闭所有文件
    for fh in file_handles:
        fh.close()

if __name__ == "__main__":
    # files = ['gap_bc_mix2.pagetrace', 'deepsjeng_t2_mix2.pagetrace', 'cactusBSSN_mix2.pagetrace', 'spec505_mcf_mix2.pagetrace']
    # output_file = 'mix2_bc_deepsjeng_cactusBSSN_mcf_time.pagetrace'

		# files = ['pr_t3_t1_mix1.pagetrace', 'spec508_namd_mix1.pagetrace', 'spec_lbm_mix1.pagetrace', 'gap_cc_mix1.pagetrace']
		# output_file = 'mix1_pr_namd_lbm_cc_time.pagetrace'

		files = ['gap_cc_off1.pagetrace', 'gap_cc_off2.pagetrace', 'gap_cc_off3.pagetrace', 'gap_cc_off4.pagetrace']
		output_file = 'gap_cc_mix1.pagetrace'

		mix_files_by_arrival_time(files, output_file)
