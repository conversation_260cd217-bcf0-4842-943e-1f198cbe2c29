# --- Configuration ---
# Set the desired number of repetitions for the longest trace file.
# Other shorter traces will repeat as necessary until the longest trace completes this many repetitions.
# If set to 1, each trace effectively runs once, controlled by the longest trace completing its single run.
DESIRED_REPETITIONS = 1 # Defaulting to 4, change as needed.
# --- End Configuration ---

def get_intrinsic_file_duration(filepath):
    total_duration_for_file = 0
    try:
        with open(filepath, 'r') as fp:
            for line in fp:
                parts = line.split()
                if parts:
                    try:
                        total_duration_for_file += int(parts[0])
                    except ValueError:
                        print(f"Warning: Non-integer value found for arrival time in {filepath}, line: {line.strip()}")
                        # Decide how to handle: skip line, count as 0, or raise error
                        pass
    except FileNotFoundError:
        print(f"Warning: File not found during intrinsic duration calculation: {filepath}")
        return 0 # Or raise an error
    return total_duration_for_file

def merge_pagetrace_files(files, output_file, performance_ratios, time_offsets):
    # Ensure DESIRED_REPETITIONS is at least 1
    effective_repetitions = max(1, DESIRED_REPETITIONS)
    if DESIRED_REPETITIONS < 1:
        print(f"Warning: DESIRED_REPETITIONS was {DESIRED_REPETITIONS}, adjusted to 1. Files will run at least once.")

    # 初始化一个列表来作为优先队列
    priority_queue = []

    # 打开所有文件，并初始化文件指针
    file_pointers = [open(file, 'r') for file in files]
    last_arrival_times = [0] * len(files)  # 存储每个文件最后一条记录的累积到达时间
    execution_counts = [0] * len(files)  # 记录每个文件已执行的次数

    designated_longest_file_idx = -1
    if files:
        intrinsic_durations = [get_intrinsic_file_duration(f) for f in files]
        if intrinsic_durations:
            max_duration = -1
            # Handle case where all durations might be 0 or negative if files are empty/problematic
            # max() on an empty sequence of valid durations would error if all get_intrinsic_file_duration return 0 and list becomes [0,0,0]
            # and then .index(0) would just get the first one.
            # We need to find the actual max value first.
            current_max_val = -float('inf')
            for i, dur in enumerate(intrinsic_durations):
                if dur > current_max_val:
                    current_max_val = dur
                    designated_longest_file_idx = i
            
            if designated_longest_file_idx != -1:
                print(f"File designated as longest: {files[designated_longest_file_idx]} with intrinsic duration {intrinsic_durations[designated_longest_file_idx]}")
            else:
                print("Warning: Could not determine the longest file (all files might be empty or have zero duration). No 'repeat' markers will be added for a specific longest file.")
        else:
            print("Warning: Could not calculate intrinsic durations for files. No 'repeat' markers will be added for a specific longest file.")
    else:
        print("Warning: No input files provided. No 'repeat' markers will be added.")

    try:
        # 首先读取每个文件的第一行并加入优先队列
        for source, fp in enumerate(file_pointers, start=1):
            line = fp.readline().strip()
            if line:
                parts = line.split()
                arrival_time = int(parts[0])
                # 根据性能比例调整到达时间，同时加上时间偏置
                adjusted_arrival_time = (arrival_time + time_offsets[source - 1]) / performance_ratios[source - 1]
                # 将调整后的到达时间、行内容、来源索引加入队列
                priority_queue.append((adjusted_arrival_time, parts, source))
                last_arrival_times[source - 1] = arrival_time

        # 自定义排序函数
        def sort_priority_queue(queue):
            # 按到达时间排序
            queue.sort(key=lambda x: x[0])

        # 打开输出文件
        with open(output_file, 'w') as out_fp:
            # 处理队列中的每个元素
            while priority_queue:
                # 对队列进行排序
                sort_priority_queue(priority_queue)
                # 弹出队列中最小的元素
                adjusted_arrival_time, parts, source = priority_queue.pop(0)
                # 构建输出行，第一列为排序所用的 adjusted_arrival_time
                output_line_parts = [str(int(round(adjusted_arrival_time)))] + parts[1:]
                out_fp.write(' '.join(output_line_parts) + f' {source}\n')

                # 从相应的文件中读取下一行
                next_line = file_pointers[source - 1].readline().strip()
                if next_line:
                    next_parts = next_line.split()
                    next_arrival_time = int(next_parts[0]) + last_arrival_times[source - 1]
                    # 根据性能比例调整到达时间，同时加上时间偏置
                    adjusted_next_arrival_time = (next_arrival_time + time_offsets[source - 1]) / performance_ratios[source - 1]
                    # 将新行加入队列
                    priority_queue.append((adjusted_next_arrival_time, next_parts, source))
                    last_arrival_times[source - 1] = next_arrival_time
                else:
                    # 文件到达末尾，增加执行计数
                    execution_counts[source - 1] += 1
                    print(f"File {files[source - 1]} completed pass {execution_counts[source - 1]}. Cumulative time for this file at end of pass: {last_arrival_times[source - 1]}")

                    # Add "repeat" marker if the longest file just completed a pass AND more passes are expected for it.
                    if designated_longest_file_idx != -1 and \
                       (source - 1) == designated_longest_file_idx and \
                       execution_counts[source - 1] < effective_repetitions: # Only write "repeat" if it's NOT the final pass
                        out_fp.write("repeat\n")
                        print(f"Added 'repeat' marker for file {files[source-1]} after pass {execution_counts[source-1]} (target: {effective_repetitions} passes)")

                    # Determine if the current file should be reset and re-added to the queue
                    should_reset_current_file = False
                    if designated_longest_file_idx != -1:
                        # A longest file was identified. Its completion of 4 passes dictates overall repetition.
                        if execution_counts[designated_longest_file_idx] < effective_repetitions:
                            should_reset_current_file = True
                    else:
                        # No longest file identified (e.g., all files empty or zero duration).
                        # Fallback: individual files repeat if their own count is less than 4.
                        if execution_counts[source - 1] < effective_repetitions:
                            should_reset_current_file = True

                    if should_reset_current_file:
                        file_pointers[source - 1].seek(0)
                        # last_arrival_times[source-1] stores the cumulative time from the end of this file's previous pass.
                        line_after_reset = file_pointers[source - 1].readline().strip()
                        if line_after_reset:
                            parts_after_reset = line_after_reset.split()
                            arrival_time_in_file = int(parts_after_reset[0]) # Time from the file line
                            
                            # New cumulative time for this event = file's own time + its total time from previous passes
                            new_cumulative_arrival_time_for_event = arrival_time_in_file + last_arrival_times[source - 1]
                            
                            adjusted_time_for_queue = (new_cumulative_arrival_time_for_event + time_offsets[source - 1]) / performance_ratios[source - 1]
                            
                            priority_queue.append((adjusted_time_for_queue, parts_after_reset, source))
                            
                            # Update last_arrival_times for *this file* to its new cumulative time up to this event
                            last_arrival_times[source - 1] = new_cumulative_arrival_time_for_event
                        # else: file is empty after reset, will not contribute further.
                    else:
                        # This file will not be reset.
                        if designated_longest_file_idx != -1 and execution_counts[designated_longest_file_idx] >= effective_repetitions:
                            print(f"File {files[source - 1]} (pass {execution_counts[source - 1]}) will not be reset as the designated longest file ({files[designated_longest_file_idx]}) has completed {execution_counts[designated_longest_file_idx]}/{effective_repetitions} passes.")
                        elif designated_longest_file_idx == -1 and execution_counts[source - 1] >= effective_repetitions:
                             print(f"File {files[source - 1]} has completed {execution_counts[source - 1]}/{effective_repetitions} passes (no specific longest file identified). No more lines will be read from it.")
                        else:
                             # This case should ideally not be hit if logic is correct, but as a safeguard:
                             print(f"File {files[source - 1]} (pass {execution_counts[source - 1]}) will not be reset.")
                        # File is considered done for the purpose of adding new events.
                        # It will be closed in the finally block.
    finally:
        # 确保所有文件指针都被关闭
        for fp in file_pointers:
            fp.close()

# 示例调用
# files = ['spec505_mcf_mix2.pagetrace', 'deepsjeng_t2_mix2.pagetrace', 'cactusBSSN_new_mix2.pagetrace', 'gap_bc_new_mix2.pagetrace']
# output_file = 'mcf_deep_cactus_bc_mix2_difspeed.pagetrace'
# performance_ratios = [1.0, 0.8, 0.6, 0.4]
# time_offsets = [1000, 2000, 3000, 4000]  # 示例时间偏置

# files = ['gap_sssp_CN0.pagetrace', 'gap_sssp_CN1.pagetrace']
# output_file = 'gap_sssp_2CNs.pagetrace'
# performance_ratios = [1.0, 1.0]
# time_offsets = [1400, 125000]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

# files = ['gap_sssp_CN0.pagetrace', 'gap_sssp_CN1.pagetrace', 'gap_sssp_CN2.pagetrace']
# output_file = 'gap_sssp_3CNs.pagetrace'
# performance_ratios = [1.0, 1.0,1.0]
# time_offsets = [1400, 25000,100000]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)


# files = ['gap_bc_new_CN0.pagetrace', 'gap_bc_new_CN1.pagetrace', 'gap_bc_new_CN2.pagetrace','gap_bc_new_CN3.pagetrace']
# output_file = 'gap_bc_new_4CNs.pagetrace'
# performance_ratios = [1.0, 1.0,1.0,1.0]
# time_offsets = [1400, 25000,70000,150000]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

# files = ['cactusBSSN_new_CN00.pagetrace', 'cactusBSSN_new_CN11.pagetrace', 'cactusBSSN_new_CN22.pagetrace','cactusBSSN_new_CN33.pagetrace']
# output_file = 'cactusBSSN_new_4CNs_0.15.pagetrace'
# performance_ratios = [0.9527383204465942, 1.1321941692792594, 1.1054876398311988, 0.808557106901081]
# time_offsets = [24154, 457524,75427,124858]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

# files = ['cactusBSSN_new_CN00.pagetrace', 'cactusBSSN_new_CN11.pagetrace']
# output_file = 'cactusBSSN_new_2CNs_0.5.pagetrace'
# performance_ratios = [0.6442833519803634, 1.3547916789880534]
# time_offsets = [24154, 457524]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

# files = ['spec_lbm_new_CN1.pagetrace','spec508_namd_CN2.pagetrace','deepsjeng_t2_CN3.pagetrace','cactusBSSN_new_CN4.pagetrace']
# output_file = 'lbm_namd_dp_cb.pagetrace'
# performance_ratios = [1,1,1,1]
# time_offsets = [0,0,0,0]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)


# files = ['radiosity_off1.pagetrace', 'radiosity_off2.pagetrace','radiosity_off3.pagetrace','radiosity_off4.pagetrace']
# output_file = 'radiosity_off1_4CNs.pagetrace'
# performance_ratios = [1,1,1,1]
# time_offsets = [54752, 25285,158951,123397]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

# files = ['trace/cac1.pagetrace', 'trace/dep2.pagetrace','trace/cc3.pagetrace','trace/sssp4.pagetrace']
# output_file = 'trace/cac_dp_cc_sssp_repeat4_test.pagetrace'
# performance_ratios = [1,1,1,1]
# time_offsets = [0,0,0,0,0]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

# files = ['trace/cactusBSSN_new_CN1.pagetrace', 'trace/gap_deepsjeng_CN2.pagetrace','trace/gap_cc_CN3.pagetrace','trace/gap_sssp_CN4.pagetrace']
# output_file = 'trace/cac_dp_cc_sssp_repeat1.pagetrace'
# performance_ratios = [1,1,1,1]
# time_offsets = [0,0,0,0,0]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

files = ['trace/splash_ocean_CN1.pagetrace', 'trace/ycsb_f_p_CN2.pagetrace','trace/xz_p_CN3.pagetrace','trace/splash_radiosity_p_CN4.pagetrace']
output_file = 'trace/ocean_ycsb_xz_radio_repeat1.pagetrace'
performance_ratios = [1,1,1,1]
time_offsets = [0,0,0,0,0]  # 示例时间偏置
merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

