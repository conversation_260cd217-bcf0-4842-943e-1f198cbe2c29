#ifndef CACHE_H
#define CACHE_H

#define CACHE_SIZE (1L * 64 * 1024 * 1024 / 4096)
#define NUM_SETS (CACHE_SIZE / LINES_PER_SET)
#define LINES_PER_SET 4

typedef struct CachePage {
    unsigned long long page_number;
    int valid;
    int lru_counter;
    int fifo_order;
    int load_id;
} CachePage;

extern CachePage cache[NUM_SETS][LINES_PER_SET];
// extern int strategy;

void init_cache();
int find_in_cache(unsigned long long page_number,long load_id);
void update_lru(int set_index, int accessed_index);
void load_to_cache_lru(unsigned long long page_number, int load_id);
void load_to_cache_fifo(unsigned long long page_number, int load_id);

#endif
