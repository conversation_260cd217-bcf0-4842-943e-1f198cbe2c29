#include "cache.h"
#include <limits.h>
#include "eq.h"
#include "stdlib.h"

CachePage cache[NUM_SETS][LINES_PER_SET];
int fifo_counter[NUM_SETS] = {0};
// int strategy = 1;

void init_cache() {
    for (int i = 0; i < NUM_SETS; i++) {
        for (int j = 0; j < LINES_PER_SET; j++) {
            cache[i][j].valid = 0;
            cache[i][j].lru_counter = 0;
            cache[i][j].fifo_order = 0;
            cache[i][j].load_id = -1;
        }
    }

	init_evaluation_queues();  // 初始化 EQ
}

int find_in_cache(unsigned long long page_number,long load_id) {
    int set_index = page_number % NUM_SETS;
    for (int i = 0; i < LINES_PER_SET; i++) {
        if (cache[set_index][i].valid && cache[set_index][i].page_number == page_number) {
			//加入EQ
			if (set_index % 64 == 0) {  // 检查是否是采样的 set index
                Naper_EQEntry *entry = (Naper_EQEntry *)malloc(sizeof(Naper_EQEntry));
                if (entry) {
                    entry->address = page_number;
                    entry->action_index = 0;  // 根据实际情况设置
                    entry->in_cache = 1;
                    entry->cpu = load_id;  // 根据实际情况设置
                    entry->has_reward = false;
                    entry->reward = 0;
                    entry->position = 0;
                    entry->trigger = true;
                    entry->cache_set = set_index;

                    uint32_t eq_index = set_index / 64;  // 计算 EQ 索引
                    enqueue(eq_index, entry);

					log_eq_entry(entry, "log/eq.log");
                }
            }
            return i;
        }
    }
    return -1;
}

void update_lru(int set_index, int accessed_index) {
    for (int i = 0; i < LINES_PER_SET; i++) {
        if (cache[set_index][i].valid) {
            cache[set_index][i].lru_counter++;
        }
    }
    cache[set_index][accessed_index].lru_counter = 0;
}

void load_to_cache_lru(unsigned long long page_number, int load_id) {
    int set_index = page_number % NUM_SETS;
    int lru_index = 0;
    int max_counter = -1;

    for (int i = 0; i < LINES_PER_SET; i++) {
        if (!cache[set_index][i].valid) {
            lru_index = i;
            break;
        }
        if (cache[set_index][i].lru_counter > max_counter) {
            max_counter = cache[set_index][i].lru_counter;
            lru_index = i;
        }
    }

    cache[set_index][lru_index].page_number = page_number;
    cache[set_index][lru_index].valid = 1;
    cache[set_index][lru_index].load_id = load_id;
    update_lru(set_index, lru_index);
}

void load_to_cache_fifo(unsigned long long page_number, int load_id) {
    int set_index = page_number % NUM_SETS;
    int fifo_index = 0;
    int oldest_order = INT_MAX;

    for (int i = 0; i < LINES_PER_SET; i++) {
        if (!cache[set_index][i].valid) {
            fifo_index = i;
            break;
        }
        if (cache[set_index][i].fifo_order < oldest_order) {
            oldest_order = cache[set_index][i].fifo_order;
            fifo_index = i;
        }
    }

    cache[set_index][fifo_index].page_number = page_number;
    cache[set_index][fifo_index].valid = 1;
    cache[set_index][fifo_index].fifo_order = fifo_counter[set_index]++;
    cache[set_index][fifo_index].load_id = load_id;
}
