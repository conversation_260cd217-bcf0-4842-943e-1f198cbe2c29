# 定义编译器和编译选项
CC = g++
CFLAGS = -Wall -O2

# 定义源文件和头文件，使用 wildcard 函数搜索当前目录下的所有 .c 和 .h 文件
SRCS = $(wildcard *.c)
HDRS = $(wildcard *.h)

# 定义目标文件目录
OBJDIR = build

# 定义目标文件，将 .c 文件替换为 .o 文件，并存放在 OBJDIR 目录中
OBJS = $(patsubst %.c, $(OBJDIR)/%.o, $(SRCS))

# 定义生成的可执行文件名称
TARGET = cache_simulator

# 默认目标
all: $(TARGET)

# 链接目标文件，生成可执行文件
$(TARGET): $(OBJS)
	$(CC) $(CFLAGS) -o $@ $(OBJS)

# 编译每个源文件生成对应的对象文件，并添加头文件依赖
$(OBJDIR)/%.o: %.c $(HDRS)
	@mkdir -p $(OBJDIR)
	$(CC) $(CFLAGS) -c $< -o $@

# 运行程序
run: $(TARGET)
	./$(TARGET)

# 清理生成的文件
clean:
	rm -f $(TARGET) $(OBJDIR)/*.o

# 伪目标
.PHONY: all clean run
