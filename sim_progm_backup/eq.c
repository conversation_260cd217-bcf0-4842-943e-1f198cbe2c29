#include "eq.h"
#include <stdlib.h>
#include <stdio.h>

// 全局EQ数组
EvaluationQueue evaluation_queues[NUM_EQ];
bool log_file_initialized = false;
// 初始化EQ
void init_evaluation_queues(void) {
    for (int i = 0; i < NUM_EQ; i++) {
        evaluation_queues[i].front = 0;
        evaluation_queues[i].rear = 0;
        evaluation_queues[i].size = 0;
        for (int j = 0; j < EQ_SIZE; j++) {
            evaluation_queues[i].entries[j] = NULL;
        }
    }
}

// 将条目添加到EQ
bool enqueue(uint32_t eq_index, Naper_EQEntry *entry) {
    if (eq_index >= NUM_EQ) return false; // 索引超出范围
    EvaluationQueue *eq = &evaluation_queues[eq_index];
    if (eq->size >= EQ_SIZE) return false; // EQ已满

    eq->entries[eq->rear] = entry;
    eq->rear = (eq->rear + 1) % EQ_SIZE;
    eq->size++;
    return true;
}

// 从EQ中移除条目
Naper_EQEntry* dequeue(uint32_t eq_index) {
    if (eq_index >= NUM_EQ) return NULL; // 索引超出范围
    EvaluationQueue *eq = &evaluation_queues[eq_index];
    if (eq->size == 0) return NULL; // EQ为空

    Naper_EQEntry *entry = eq->entries[eq->front];
    eq->front = (eq->front + 1) % EQ_SIZE;
    eq->size--;
    return entry;
}

// 更新EQ中条目的位置
void update_eq_position(uint32_t eq_index) {
    if (eq_index >= NUM_EQ) return; // 索引超出范围
    EvaluationQueue *eq = &evaluation_queues[eq_index];
    for (int i = 0; i < eq->size; i++) {
        int index = (eq->front + i) % EQ_SIZE;
        eq->entries[index]->position = (eq->entries[index]->position - 1 + EQ_SIZE) % EQ_SIZE;
    }
}

// 记录EQ条目到日志文件
void log_eq_entry(Naper_EQEntry *entry, const char *log_filename) {
    if (entry == NULL) return;

    // 检查是否需要清空并初始化日志文件
    if (!log_file_initialized) {
        FILE *log_file = fopen(log_filename, "w");
        if (log_file) {
            fclose(log_file);
            log_file_initialized = true; // 标记日志文件已初始化
        }
    }

    // 追加模式写入日志文件
    FILE *log_file = fopen(log_filename, "a");
    if (log_file) {
        fprintf(log_file, "Address: %lu, ActionIndex: %d, InCache: %d, CPU: %d, CacheSet: %d\n",
                entry->address, entry->action_index, entry->in_cache, entry->cpu, entry->cache_set);
        fclose(log_file);
    }
}