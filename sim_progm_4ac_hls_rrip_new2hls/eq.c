#include "eq.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 全局EQ数组
EvaluationQueue evaluation_queues[NUM_EQ];
bool log_file_initialized = false;

// 初始化EQ
void init_evaluation_queues(void) {
  for (int i = 0; i < NUM_EQ; i++) {
    evaluation_queues[i].front = 0;
    evaluation_queues[i].rear = 0;
    evaluation_queues[i].size = 0;
  }
}

int enqueue(uint32_t eq_index, Naper_EQEntry *entry,
            Naper_EQEntry *evicted_entry) {
  EvaluationQueue *eq = &evaluation_queues[eq_index];
  int rt = 0;
  if (eq->size == EQ_SIZE) {
    // 队列已满，出队一个元素
    Naper_EQEntry *dequeued_entry = dequeue(eq_index);
    if (evicted_entry != NULL && dequeued_entry != NULL) {
      *evicted_entry = *dequeued_entry;  // 复制被逐出的条目
      rt = 1;
    }
  }

  eq->address[eq->rear] = entry->address;
  //   eq->timestamp[eq->rear] = entry->timestamp;
  eq->action_index[eq->rear] = entry->action_index;
  eq->in_cache[eq->rear] = entry->in_cache;
  eq->cpu[eq->rear] = entry->cpu;
  for (int i = 0; i < NUM_CPUS; i++) {
    eq->obstruction[eq->rear][i] = entry->obstruction[i];
  }
  eq->has_reward[eq->rear] = entry->has_reward;
  eq->reward[eq->rear] = entry->reward;
  eq->position[eq->rear] = entry->position;
  eq->trigger[eq->rear] = entry->trigger;
  eq->cache_set[eq->rear] = entry->cache_set;
  eq->hit_eq[eq->rear] = entry->hit_eq;

  eq->rear = (eq->rear + 1) % EQ_SIZE;
  eq->size++;
  return rt;
}

Naper_EQEntry *dequeue(uint32_t eq_index) {
  EvaluationQueue *eq = &evaluation_queues[eq_index];
  if (eq->size == 0) {
    return NULL;  // 队列为空
  }

  static Naper_EQEntry entry;
  entry.address = eq->address[eq->front];
  entry.timestamp = eq->timestamp[eq->front];
  entry.action_index = eq->action_index[eq->front];
  entry.in_cache = eq->in_cache[eq->front];
  entry.cpu = eq->cpu[eq->front];
  for (int i = 0; i < NUM_CPUS; i++) {
    entry.obstruction[i] = eq->obstruction[eq->front][i];
  }
  entry.has_reward = eq->has_reward[eq->front];
  entry.reward = eq->reward[eq->front];
  entry.position = eq->position[eq->front];
  entry.trigger = eq->trigger[eq->front];
  entry.cache_set = eq->cache_set[eq->front];
  entry.hit_eq = eq->hit_eq[eq->front];

  eq->front = (eq->front + 1) % EQ_SIZE;
  eq->size--;
  return &entry;
}

// int enqueue(uint32_t eq_index, Naper_EQEntry *entry,
//             Naper_EQEntry *evicted_entry) {
//   if (eq_index >= NUM_EQ) {
//     printf("enqueue 超出范围\r\n");
//     evicted_entry = NULL;  // 确保指针为空
//     return false;           // 索引超出范围，没有驱逐
//   }

//   EvaluationQueue *eq = &evaluation_queues[eq_index];

//   if (eq->size >= EQ_SIZE) {
//     // EQ已满，逐出一个条目
//     // *evicted_entry = &eq->entries[eq->front];
// 	Naper_EQEntry *evi = &eq->entries[eq->front];
// 	// 复制被逐出条目的数据到 entry_evi
//     // *evicted_entry = *evicted_entry;  // 直接赋值整个结构体
// 	memcpy(evicted_entry, evi, sizeof(Naper_EQEntry));

//     // 直接赋值整个结构体
//     eq->entries[eq->front] = *entry;

//     eq->front = (eq->front + 1) % EQ_SIZE;
//     eq->rear = (eq->rear + 1) % EQ_SIZE;  // rear也要前进

//     return true;  // 发生了驱逐
//   } else {
//     // 添加新条目到EQ
//     eq->entries[eq->rear] = *entry;
//     eq->rear = (eq->rear + 1) % EQ_SIZE;
//     eq->size++;
//     evicted_entry = NULL;  // 队列未满，没有条目被驱逐

//     return false;  // 没有驱逐
//   }
// }

// Naper_EQEntry *dequeue(uint32_t eq_index) {
//   if (eq_index >= NUM_EQ) return NULL;  // 索引超出范围
//   EvaluationQueue *eq = &evaluation_queues[eq_index];
//   if (eq->size == 0) return NULL;  // EQ为空

//   Naper_EQEntry *entry = &eq->entries[eq->front];
//   eq->front = (eq->front + 1) % EQ_SIZE;
//   eq->size--;
//   return entry;
// }

// void update_eq_position(uint32_t eq_index) {
//   if (eq_index >= NUM_EQ) return;  // 索引超出范围
//   EvaluationQueue *eq = &evaluation_queues[eq_index];
//   for (int i = 0; i < eq->size; i++) {
//     int index = (eq->front + i) % EQ_SIZE;
//     eq->entries[index].position =
//         (eq->entries[index].position - 1 + EQ_SIZE) % EQ_SIZE;
//   }
// }

void update_eq_position(uint32_t eq_index) {
  EvaluationQueue *eq = &evaluation_queues[eq_index];
  for (int i = 0; i < eq->size; i++) {
    int index = (eq->front + i) % EQ_SIZE;
    eq->position[index] = i;
  }
}

void log_eq_entry(Naper_EQEntry *entry, const char *log_filename) {
  FILE *log_file = fopen(log_filename, "a");
  if (log_file == NULL) {
    perror("Failed to open log file");
    return;
  }

  fprintf(
      log_file,
      "Address: %llu, Timestamp: %llu, Action Index: %d, In Cache: %d, CPU: "
      "%u, Reward: %d, Position: %u, Trigger: %d, Cache Set: %u, Hit EQ: %d\n",
      entry->address, entry->timestamp, entry->action_index, entry->in_cache,
      entry->cpu, entry->reward, entry->position, entry->trigger,
      entry->cache_set, entry->hit_eq);

  fclose(log_file);
}

// void log_eq_entry(Naper_EQEntry *entry, const char *log_filename) {
//   if (entry == NULL) return;

//   if (!log_file_initialized) {
//     FILE *log_file = fopen(log_filename, "w");
//     if (log_file) {
//       fclose(log_file);
//       log_file_initialized = true;
//     }
//   }

//   FILE *log_file = fopen(log_filename, "a");
//   if (log_file) {
//     fprintf(
//         log_file,
//         "Address: %lu, ActionIndex: %d, InCache: %d, CPU: %d, CacheSet:
//         %d\n", entry->address, entry->action_index, entry->in_cache,
//         entry->cpu, entry->cache_set);
//     fclose(log_file);
//   }
// }

bool print_specific_eq_only = true;
int specific_eq_index = 6;

// void print_evaluation_queues(const char *log_filename) {
//   FILE *log_file = fopen(log_filename, "a");
//   if (log_file == NULL) {
//     perror("Failed to open log file");
//     return;
//   }

//   for (int i = 0; i < NUM_EQ; ++i) {
//     if (print_specific_eq_only && i != specific_eq_index) {
//       continue;
//     }

//     fprintf(log_file, "EQ %d:\n", i);
//     EvaluationQueue *eq = &evaluation_queues[i];
//     for (int j = 0; j < eq->size; ++j) {
//       int index = (eq->rear - 1 - j + EQ_SIZE) % EQ_SIZE;
//       Naper_EQEntry *entry = &eq->entries[index];
//       fprintf(log_file,
//               "  Address: %llu, ActionIndex: %d, InCache: %d, CPU: %d, "
//               " Reward: %d, HasReward: %d, Trigger: %d\n",
//               entry->address, entry->action_index, entry->in_cache,
//               entry->cpu, entry->reward, entry->has_reward, entry->trigger);
//     }
//   }
//   fclose(log_file);
// }
void print_evaluation_queues(const char *log_filename) {
  FILE *log_file = fopen(log_filename, "a");
  if (log_file == NULL) {
    perror("Failed to open log file");
    return;
  }

  for (int i = 0; i < NUM_EQ; ++i) {
    if (print_specific_eq_only && i != specific_eq_index) {
      continue;
    }

    fprintf(log_file, "EQ %d:\n", i);
    EvaluationQueue *eq = &evaluation_queues[i];
    for (int j = 0; j < eq->size; ++j) {
      // int index = (eq->front + j) % EQ_SIZE;
      int index = (eq->rear - 1 - j + EQ_SIZE) % EQ_SIZE;
      fprintf(log_file,
              "  Address: %llu, ActionIndex: %d, InCache: %d, CPU: %d, "
              " Reward: %d, HasReward: %d, Trigger: %d\n",
              eq->address[index], eq->action_index[index], eq->in_cache[index],
              eq->cpu[index], eq->reward[index], eq->has_reward[index],
              eq->trigger[index]);
    }
  }
  fclose(log_file);
}

// Naper_EQEntry *find_in_evaluation_queue(uint32_t eq_index, uint64_t address)
// {
//   if (eq_index >= NUM_EQ) {
//     printf("超出范围\r\n");
//     return NULL;
//   }

//   //  EvaluationQueue *eq = &evaluation_queues[eq_index];

//   for (int i = 0; i < EQ_SIZE; i++) {
//     int index = (evaluation_queues[eq_index].rear - i - 1 + EQ_SIZE) %
//     EQ_SIZE; if (evaluation_queues[eq_index].entries[index].address ==
//     address) {
//       return &evaluation_queues[eq_index].entries[index];
//     }
//   }
//   return NULL;
// }

bool find_in_evaluation_queue(uint32_t eq_index, uint64_t address,
                              Naper_EQEntry *result) {
  EvaluationQueue *eq = &evaluation_queues[eq_index];
  for (int i = 0; i < eq->size; i++) {
    int index = (eq->rear - i - 1 + EQ_SIZE) % EQ_SIZE;
    if (eq->address[index] == address) {
      // 复制找到的条目到 result
      result->address = eq->address[index];
      result->timestamp = eq->timestamp[index];
      result->action_index = eq->action_index[index];
      result->in_cache = eq->in_cache[index];
      result->cpu = eq->cpu[index];
      for (int j = 0; j < NUM_CPUS; j++) {
        result->obstruction[j] = eq->obstruction[index][j];
      }
      result->has_reward = eq->has_reward[index];
      result->reward = eq->reward[index];
      result->position = eq->position[index];
      result->trigger = eq->trigger[index];
      result->cache_set = eq->cache_set[index];
      result->hit_eq = eq->hit_eq[index];
      return true;
    }
  }
  return false;
}

// // 不能return
// Naper_EQEntry *find_in_evaluation_queue(uint32_t eq_index, uint64_t address) {
//   EvaluationQueue *eq = &evaluation_queues[eq_index];
//   for (int i = 0; i < eq->size; i++) {
//     int index = (eq->rear - i - 1 + EQ_SIZE) % EQ_SIZE;
//     // int index = (eq->front + i) % EQ_SIZE;
//     if (eq->address[index] == address) {
//       static Naper_EQEntry entry;
//       entry.address = eq->address[index];
//       entry.timestamp = eq->timestamp[index];
//       entry.action_index = eq->action_index[index];
//       entry.in_cache = eq->in_cache[index];
//       entry.cpu = eq->cpu[index];
//       for (int j = 0; j < NUM_CPUS; j++) {
//         entry.obstruction[j] = eq->obstruction[index][j];
//       }
//       entry.has_reward = eq->has_reward[index];
//       entry.reward = eq->reward[index];
//       entry.position = eq->position[index];
//       entry.trigger = eq->trigger[index];
//       entry.cache_set = eq->cache_set[index];
//       entry.hit_eq = eq->hit_eq[index];
//       return &entry;
//     }
//   }
//   return NULL;
// }

// int find_eq_entry_index(uint32_t eq_index, uint64_t address) {
//   if (eq_index >= NUM_EQ) {
//     printf("超出范围\r\n");
//     return -1;
//   }

//   //  static EvaluationQueue *eq = &evaluation_queues[eq_index];

// find_eq_entry_index_label0:
//   for (int i = 0; i < EQ_SIZE; i++) {
//     int index = (evaluation_queues[eq_index].rear - 1 - i + EQ_SIZE) %
//     EQ_SIZE; if (evaluation_queues[eq_index].entries[index].address ==
//     address) {
//       return index;
//     }
//   }
//   return -1;
// }

int find_eq_entry_index(uint32_t eq_index, uint64_t address) {
  EvaluationQueue *eq = &evaluation_queues[eq_index];
  for (int i = 0; i < eq->size; i++) {
    int index = (eq->rear - 1 - i + EQ_SIZE) % EQ_SIZE;
    // int index = (eq->front + i) % EQ_SIZE;
    if (eq->address[index] == address) {
      return index;
    }
  }
  return -1;
}

// bool is_evaluation_queue_full(uint32_t eq_index) {
//   if (eq_index >= NUM_EQ) return true;

//   EvaluationQueue *eq = &evaluation_queues[eq_index];
//   return eq->size >= EQ_SIZE;
// }
bool is_evaluation_queue_full(uint32_t eq_index) {
  EvaluationQueue *eq = &evaluation_queues[eq_index];
  return eq->size == EQ_SIZE;
}


bool peek_evaluation_queue(uint32_t eq_index, Naper_EQEntry *result) {
    EvaluationQueue *eq = &evaluation_queues[eq_index];
    if (eq->size == 0) {
        return false;  // 队列为空，操作失败
    }

    int index = eq->front;
    result->address = eq->address[index];
    result->timestamp = eq->timestamp[index];
    result->action_index = eq->action_index[index];
    result->in_cache = eq->in_cache[index];
    result->cpu = eq->cpu[index];
    for (int i = 0; i < NUM_CPUS; i++) {
        result->obstruction[i] = eq->obstruction[index][i];
    }
    result->has_reward = eq->has_reward[index];
    result->reward = eq->reward[index];
    result->position = eq->position[index];
    result->trigger = eq->trigger[index];
    result->cache_set = eq->cache_set[index];
    result->hit_eq = eq->hit_eq[index];

    return true;  // 操作成功
}

// Naper_EQEntry *peek_evaluation_queue(uint32_t eq_index) {
//   EvaluationQueue *eq = &evaluation_queues[eq_index];
//   if (eq->size == 0) {
//     return NULL;  // 队列为空
//   }

//   static Naper_EQEntry entry;
//   int index = eq->front;
//   entry.address = eq->address[index];
//   entry.timestamp = eq->timestamp[index];
//   entry.action_index = eq->action_index[index];
//   entry.in_cache = eq->in_cache[index];
//   entry.cpu = eq->cpu[index];
//   for (int i = 0; i < NUM_CPUS; i++) {
//     entry.obstruction[i] = eq->obstruction[index][i];
//   }
//   entry.has_reward = eq->has_reward[index];
//   entry.reward = eq->reward[index];
//   entry.position = eq->position[index];
//   entry.trigger = eq->trigger[index];
//   entry.cache_set = eq->cache_set[index];
//   entry.hit_eq = eq->hit_eq[index];

//   return &entry;
// }

// Naper_EQEntry *peek_evaluation_queue(uint32_t eq_index) {
//   if (eq_index >= NUM_EQ) return NULL;

//   EvaluationQueue *eq = &evaluation_queues[eq_index];
//   if (eq->size == 0) return NULL;

//   int front_index = eq->front;
//   return &eq->entries[front_index];
// }

bool modify_eq_entry_reward(uint32_t eq_index, int tail_index,
                            int32_t new_reward) {
  EvaluationQueue *eq = &evaluation_queues[eq_index];
  if (tail_index < 0 || tail_index >= eq->size) {
    return false;
  }

  //   int index = (eq->front + tail_index) % EQ_SIZE;
  eq->reward[tail_index] = new_reward;
  eq->has_reward[tail_index] = true;
  eq->trigger[tail_index] = true;
  return true;
}

// bool modify_eq_entry_reward(uint32_t eq_index, int tail_index,
//                             int32_t new_reward) {
//   if (eq_index >= NUM_EQ) return false;
//   //  EvaluationQueue *eq = &evaluation_queues[eq_index];
//   if (tail_index < 0 || tail_index >= EQ_SIZE) {
//     //    printf("modify reward over");
//     return false;
//   }
//   //  if(evaluation_queues[eq_index].entries[tail_index].has_reward == true){
//   //	  return true;
//   //  }else{
//   //	  return false;
//   //  }
//   //   Naper_EQEntry temp_entry =
//   //   evaluation_queues[eq_index].entries[tail_index]; temp_entry.reward =
//   //   new_reward; temp_entry.has_reward = true; temp_entry.trigger = true;
//   //  evaluation_queues[eq_index].entries[tail_index] = temp_entry;
//   //  int index = tail_index;
//   evaluation_queues[eq_index].entries[tail_index].reward = new_reward;
//   evaluation_queues[eq_index].entries[tail_index].has_reward = true;
//   evaluation_queues[eq_index].entries[tail_index].trigger = true;
//   return true;
// }
