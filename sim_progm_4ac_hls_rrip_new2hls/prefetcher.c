#include "prefetcher.h"
#include "cache.h"
#include "config.h"
// extern int prefetch_enabled;

void prefetch(unsigned long long page_number) {
    if (!prefetch_enabled) return;

    for (int i = 1; i <= PREFETCH_N; i++) {
        unsigned long long next_page = page_number + i;
        if (find_in_cache(next_page,0) == -1) {
            if (strategy == 1) {
                load_to_cache_lru(next_page, -1);
            } else if (strategy == 0) {
                load_to_cache_fifo(next_page, -1);
            }
        }
    }
}
