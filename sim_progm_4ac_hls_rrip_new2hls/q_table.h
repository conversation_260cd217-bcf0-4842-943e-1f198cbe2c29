#ifndef Q_TABLE_H
#define Q_TABLE_H

#include <stdint.h>
#include <stdio.h>

#define NUM_SOURCES 8  // 假设有8个不同的源
// #define NUM_PAGES 65536  // 假设有32768个不同的页号
// #define NUM_PAGES 4096  // 假设有32768个不同的页号
#define NUM_PAGES (1 << 24)  // 32768个不同的页号 代表取PN的后15位作为状态
#define NUM_ACTIONS 4  // 4种动作

// 定义 epsilon（探索概率）和 alpha（学习率）以及 gamma（折扣因子）
#define EPSILON 0.000f
// #define EPSILON 0.001f
#define ALPHA 0.05f
#define GAMMA 0.36f

// Q表项结构体
typedef struct {
  uint32_t source;              // 来源标识
  uint32_t page;                // 页号
  float q_values[NUM_ACTIONS];  // 每种动作的Q值
} QItem;

// 定义全局Q表
extern QItem q_table[NUM_SOURCES][NUM_PAGES];

// Q表操作函数声明
uint16_t lfsr(uint16_t seed);
void init_q_table(void);
int select_action(uint32_t source, uint32_t page);
int select_action_hit(uint32_t source, uint32_t page);
void update_q_table(uint32_t source, uint32_t page, int32_t action, int32_t reward,
                    float next_q_value);
float get_q_value(uint32_t source, uint32_t page, int action);
#endif  // Q_TABLE_H
