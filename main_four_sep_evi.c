#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

#define BASE_FILENAME "cactusBSSN_new_2CNs_0"
#define INPUT_FILENAME BASE_FILENAME ".pagetrace"
#define LOG_FILENAME BASE_FILENAME ".log"

#define CACHE_SIZE (1L * 64 * 1024 * 1024 / 4096)
#define SSD_DELAY 75000
#define CXL_DELAY 100
#define DDR_DELAY 46

#define PREFETCH_N 4

#define NUM_SETS (CACHE_SIZE / LINES_PER_SET)
#define LINES_PER_SET 4

typedef struct CachePage {
  unsigned long long page_number;
  int valid;
  int lru_counter;
  int fifo_order;
  int load_id;
  int accessed;  // 新增字段，用于标记是否被再次访问
} CachePage;

CachePage cache[NUM_SETS][LINES_PER_SET];
int fifo_counter[NUM_SETS] = {0};
int strategy = 1;
int prefetch_enabled = 0;
int log_enabled = 0;
int separate_miss_rate = 1;

// 逐出统计
int eviction_count = 0;
int non_accessed_eviction_count = 0;

// 访存统计
int access_counts[5] = {0};     // 每个节点的访存次数
int total_memory_accesses = 0;  // 所有节点的总访存次数

void init() {
  for (int i = 0; i < NUM_SETS; i++) {
    for (int j = 0; j < LINES_PER_SET; j++) {
      cache[i][j].valid = 0;
      cache[i][j].lru_counter = 0;
      cache[i][j].fifo_order = 0;
      cache[i][j].load_id = -1;
      cache[i][j].accessed = 0;
    }
  }
  eviction_count = 0;
  non_accessed_eviction_count = 0;
}

int find_in_cache(unsigned long long page_number) {
  int set_index = page_number % NUM_SETS;
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (cache[set_index][i].valid &&
        cache[set_index][i].page_number == page_number) {
      cache[set_index][i].accessed = 1;  // 记录被再次访问
      return i;
    }
  }
  return -1;
}

void update_lru(int set_index, int accessed_index) {
  for (int i = 0; i < LINES_PER_SET; i++) {
    if (cache[set_index][i].valid) {
      cache[set_index][i].lru_counter++;
    }
  }
  cache[set_index][accessed_index].lru_counter = 0;
}

// void evict_and_load(int set_index, int load_index, unsigned long long
// page_number, int load_id) {
//     if (cache[set_index][load_index].valid) {
//         eviction_count++;
//         if (!cache[set_index][load_index].accessed) {
//             non_accessed_eviction_count++;
//         }
//     }

//     cache[set_index][load_index].page_number = page_number;
//     cache[set_index][load_index].valid = 1;
//     cache[set_index][load_index].load_id = load_id;
//     cache[set_index][load_index].accessed = 0;
// }

void evict_and_load(int set_index, int load_index,
                    unsigned long long page_number, int load_id) {
  if (cache[set_index][load_index].valid) {
    eviction_count++;
    if (!cache[set_index][load_index].accessed) {
      non_accessed_eviction_count++;

      // 打开 evi.log 文件以追加模式写入
      FILE *evi_log_file = fopen("evi.log", "a");
      if (evi_log_file) {
        // 将未重用的被驱逐数据写入文件
        fprintf(evi_log_file, "Evicted page: %llu from set: %d, load id: %d\n",
                cache[set_index][load_index].page_number, set_index,
                cache[set_index][load_index].load_id);
        // 关闭文件
        fclose(evi_log_file);
      } else {
        fprintf(stderr, "Failed to open evi.log file for writing\n");
      }
    }
  }

  // 更新缓存行信息
  cache[set_index][load_index].page_number = page_number;
  cache[set_index][load_index].valid = 1;
  cache[set_index][load_index].load_id = load_id;
  cache[set_index][load_index].accessed = 0;
}

void load_to_cache_lru(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  int lru_index = 0;
  int max_counter = -1;

  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid) {
      lru_index = i;
      break;
    }
    if (cache[set_index][i].lru_counter > max_counter) {
      max_counter = cache[set_index][i].lru_counter;
      lru_index = i;
    }
  }

  evict_and_load(set_index, lru_index, page_number, load_id);
  update_lru(set_index, lru_index);
}

void load_to_cache_fifo(unsigned long long page_number, int load_id) {
  int set_index = page_number % NUM_SETS;
  int fifo_index = 0;
  int oldest_order = __INT_MAX__;

  for (int i = 0; i < LINES_PER_SET; i++) {
    if (!cache[set_index][i].valid) {
      fifo_index = i;
      break;
    }
    if (cache[set_index][i].fifo_order < oldest_order) {
      oldest_order = cache[set_index][i].fifo_order;
      fifo_index = i;
    }
  }

  evict_and_load(set_index, fifo_index, page_number, load_id);
  cache[set_index][fifo_index].fifo_order = fifo_counter[set_index]++;
}

void prefetch(unsigned long long page_number) {
  if (!prefetch_enabled) return;

  for (int i = 1; i <= PREFETCH_N; i++) {
    unsigned long long next_page = page_number + i;
    if (find_in_cache(next_page) == -1) {
      if (strategy == 1) {
        load_to_cache_lru(next_page, -1);
      } else if (strategy == 0) {
        load_to_cache_fifo(next_page, -1);
      }
    }
  }
}

void simulate_accesses(const char *input_filename, const char *log_filename) {
  FILE *input_file = fopen(input_filename, "r");
  if (!input_file) {
    fprintf(stderr, "Failed to open file %s\n", input_filename);
    return;
  }

  FILE *log_file = NULL;
  if (log_enabled) {
    log_file = fopen(log_filename, "w");
    if (!log_file) {
      fprintf(stderr, "Failed to open log file %s\n", log_filename);
      fclose(input_file);
      return;
    }
  }

  int hits[5] = {0};
  int total_accesses[5] = {0};
  long long total_delay = 0;
  long long delay_per_load[5] = {0};
  int conflict_misses[5] = {0};
  int inter_load_conflict_misses[5] = {0};
  long timestamp, dummy1, dummy2, rw_flag, load_id;
  unsigned long long page_number;

  while (fscanf(input_file, "%ld %ld %llu %ld %ld %ld", &timestamp, &dummy1,
                &page_number, &dummy2, &rw_flag, &load_id) == 6) {
    total_accesses[load_id]++;
    access_counts[load_id]++;  // 更新节点的访存次数
    total_memory_accesses++;   // 更新总体的访存次数

    int set_index = page_number % NUM_SETS;
    int cache_index = find_in_cache(page_number);

    if (cache_index != -1) {
      hits[load_id]++;
      total_delay += (DDR_DELAY + CXL_DELAY);
      delay_per_load[load_id] += (DDR_DELAY + CXL_DELAY);

      if (strategy == 1) {
        update_lru(set_index, cache_index);
      }
      if (log_enabled) {
        fprintf(log_file, "Timestamp %ld: Page %llu, Cache hit\n", timestamp,
                page_number);
      }
    } else {
      total_delay += (SSD_DELAY + CXL_DELAY + DDR_DELAY);
      delay_per_load[load_id] += (SSD_DELAY + CXL_DELAY + DDR_DELAY);

      int is_conflict_miss = 1;
      for (int i = 0; i < LINES_PER_SET; i++) {
        if (!cache[set_index][i].valid) {
          is_conflict_miss = 0;
          break;
        }
      }
      if (is_conflict_miss) {
        conflict_misses[load_id]++;
        int inter_load_conflict = 0;
        for (int i = 0; i < LINES_PER_SET; i++) {
          if (cache[set_index][i].load_id != load_id) {
            inter_load_conflict = 1;
            break;
          }
        }
        if (inter_load_conflict) {
          inter_load_conflict_misses[load_id]++;
        }
      }

      if (strategy == 1) {
        load_to_cache_lru(page_number, load_id);
      } else if (strategy == 0) {
        load_to_cache_fifo(page_number, load_id);
      }
      if (log_enabled) {
        fprintf(log_file, "Timestamp %ld: Page %llu, Cache miss\n", timestamp,
                page_number);
      }
      prefetch(page_number);
    }
  }

  fclose(input_file);
  if (log_enabled) {
    fclose(log_file);
  }

  int total_hits = hits[1] + hits[2] + hits[3] + hits[4];
  int total_access = total_accesses[1] + total_accesses[2] + total_accesses[3] +
                     total_accesses[4];
  double overall_miss_rate = 1.0 - ((double)total_hits / total_access);
  printf("Overall miss rate: %.4f%%\n", overall_miss_rate * 100);

  if (separate_miss_rate) {
    for (int i = 1; i <= 4; i++) {
      if (total_accesses[i] > 0) {
        double miss_rate = 1.0 - ((double)hits[i] / total_accesses[i]);
        printf("Miss rate for load %d: %.4f%%\n", i, miss_rate * 100);
        printf("Total delay for load %d: %lld ns\n", i, delay_per_load[i]);

        double conflict_miss_rate =
            (double)conflict_misses[i] / total_accesses[i];
        double inter_load_conflict_miss_rate =
            (double)inter_load_conflict_misses[i] / total_accesses[i];
        printf("Conflict miss rate for load %d: %.4f%%\n", i,
               conflict_miss_rate * 100);
        printf("Inter-load conflict miss rate for load %d: %.4f%%\n", i,
               inter_load_conflict_miss_rate * 100);
      }
    }
  }

  printf("Total delay: %lld ns\n", total_delay);

  // 输出逐出统计结果
  printf("Total evictions: %d\n", eviction_count);
  printf("Evictions without re-access: %d\n", non_accessed_eviction_count);

  // 输出每个节点和总访存次数
  for (int i = 1; i <= 4; i++) {
    printf("Access count for node %d: %d\n", i, access_counts[i]);
  }
  printf("Total memory accesses: %d\n", total_memory_accesses);
}

int main() {
  init();

  clock_t start_time = clock();

  simulate_accesses(INPUT_FILENAME, LOG_FILENAME);

  clock_t end_time = clock();
  double duration = (double)(end_time - start_time) / CLOCKS_PER_SEC;

  printf("Simulation time: %.2f seconds\n", duration);

  return 0;
}
