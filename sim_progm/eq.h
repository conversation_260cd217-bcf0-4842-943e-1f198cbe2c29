#ifndef EQ_H
#define EQ_H

#include <stdbool.h>
#include <stdint.h>

#define NUM_EQ 64
#define EQ_SIZE 12
#define NUM_CPUS 4 // 根据系统CPU数量调整

// // 定义动作
// #define H_EPV 0
// #define M_EPV 1
// #define L_EPV 2
// #define BYPASS 3

// // // 定义动作
// #define H_EPV 1
// #define M_EPV 0
// #define L_EPV 2
// #define BYPASS 3

// // 定义动作
// #define H_EPV 2
// #define M_EPV 1
// #define L_EPV 0
// #define BYPASS 3

// 定义动作
#define H_EPV 3
#define M_EPV 2
#define L_EPV 0
#define BYPASS 1

#define R_AC 50
#define R_IN -50
#define R_AC_NR 80
#define R_IN_NR -70

typedef struct Naper_EQEntry {
    uint64_t address;      // 访问地址
    uint64_t timestamp;    // 访问时间戳或序列号
    int32_t action_index;  // 动作索引
    bool in_cache;        // 是否在缓存中
    uint32_t cpu;          // CPU编号
    uint64_t obstruction[NUM_CPUS]; // 阻塞信息
    bool has_reward;      // 是否有奖励
    int32_t reward;        // 奖励值
    uint32_t position;     // 在EQ中的位置
    bool trigger;         // 触发标志
    uint32_t cache_set;    // 来源cache组索引
		bool hit_eq;         // 触发标志
} Naper_EQEntry;

typedef struct {
    Naper_EQEntry *entries[EQ_SIZE];
    int front;
    int rear;
    int size;
} EvaluationQueue;

extern EvaluationQueue evaluation_queues[NUM_EQ];
extern bool log_file_initialized;

// EQ操作函数声明
void init_evaluation_queues(void);
// bool enqueue(uint32_t eq_index, Naper_EQEntry *entry);
Naper_EQEntry* enqueue(uint32_t eq_index, Naper_EQEntry *entry);
Naper_EQEntry* dequeue(uint32_t eq_index);
void update_eq_position(uint32_t eq_index);
void log_eq_entry(Naper_EQEntry *entry, const char *log_filename);
void print_evaluation_queues(const char *log_filename);
Naper_EQEntry* find_in_evaluation_queue(uint32_t eq_index, uint64_t address);
Naper_EQEntry* peek_evaluation_queue(uint32_t eq_index);
bool modify_eq_entry_reward(uint32_t eq_index, int tail_index, int32_t new_reward);
// 查找EQ中匹配地址的条目的索引
int find_eq_entry_index(uint32_t eq_index, uint64_t address);

#endif // EQ_H