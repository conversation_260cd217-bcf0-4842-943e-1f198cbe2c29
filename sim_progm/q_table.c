#include "q_table.h"

#include <stdlib.h>

#include "config.h"
#include "eq.h"

// 全局Q表
QItem q_table[NUM_SOURCES][NUM_PAGES];

// 初始化Q表
void init_q_table(void) {
  for (int i = 0; i < NUM_SOURCES; i++) {
    for (int j = 0; j < NUM_PAGES; j++) {
      for (int k = 0; k < NUM_ACTIONS; k++) {
        q_table[i][j].q_values[k] = 0.0f;  // 初始化Q值为0
        // if (k == 2)
        //   q_table[i][j].q_values[k] = 0.0001f;  // 初始化Q值为0
        // else
        //   q_table[i][j].q_values[k] = 0.0f;  // 初始化Q值为0
      }
      q_table[i][j].source = i;
      q_table[i][j].page = j;
    }
  }
}

// 选择动作（这里使用epsilon-greedy策略）
int select_action(uint32_t source, uint32_t page) {
  float epsilon_random = (float)rand() / RAND_MAX;

  if (epsilon_random < EPSILON) {
    // 随机选择一个动作
    return rand() % NUM_ACTIONS;
  } else {
    // 选择Q值最大的动作
    int max_action = 0;
    float max_q = q_table[source][page].q_values[0];
    for (int i = 1; i < NUM_ACTIONS; ++i) {
      if (q_table[source][page].q_values[i] > max_q) {
        max_q = q_table[source][page].q_values[i];
        max_action = i;
      }
    }
    return max_action;
  }
}

// // 选择动作（只考虑前三个动作）
// int select_action_hit(uint32_t source, uint32_t page) {
//   float epsilon_random = (float)rand() / RAND_MAX;

//   if (epsilon_random < EPSILON) {
//     // 随机选择一个动作，只考虑前三个动作
//     return rand() %
//            (NUM_ACTIONS -
//             1);  // 因为只考虑前三个动作，所以这里返回的随机数范围是0到2
//   } else {
//     // 选择Q值最大的动作（只考虑前三个动作）
//     int max_action = 0;
//     float max_q = q_table[source][page].q_values[0];
//     for (int i = 1; i < NUM_ACTIONS - 1; ++i) {  // 只遍历前三个动作
//       if (q_table[source][page].q_values[i] > max_q) {
//         max_q = q_table[source][page].q_values[i];
//         max_action = i;
//       }
//     }
//     return max_action;
//   }
// }

int excluded_action = BYPASS;  // 默认排除动作3

int select_action_hit(uint32_t source, uint32_t page) {
  float epsilon_random = (float)rand() / RAND_MAX;

  if (epsilon_random < EPSILON) {
    // 随机选择一个动作，排除excluded_action
    int action;
    do {
      action = rand() % NUM_ACTIONS;
    } while (action == excluded_action);
    return action;
  } else {
    // 选择Q值最大的动作，排除excluded_action
    int max_action = -1;
    float max_q = -9999;  // 使用负无穷大作为初始最大值

    for (int i = 0; i < NUM_ACTIONS; ++i) {
      if (i == excluded_action) continue;  // 跳过被排除的动作

      if (q_table[source][page].q_values[i] > max_q) {
        max_q = q_table[source][page].q_values[i];
        max_action = i;
      }
    }
    return max_action;
  }
}

// 更新Q表
void update_q_table(uint32_t source, uint32_t page, int32_t action,
                    int32_t reward, float next_q_value) {
  // // SARSA更新规则
  q_table[source][page].q_values[action] +=
      ALPHA *
      (reward + GAMMA * next_q_value - q_table[source][page].q_values[action]);
  // q_table[source][page].q_values[action] +=
  //   ALPHA *
  //   (reward );

  // q_table[source][page].q_values[action] = 0 ;
}

// 获取Q值
float get_q_value(uint32_t source, uint32_t page, int action) {
  if (source >= NUM_SOURCES || page >= NUM_PAGES || action >= NUM_ACTIONS) {
    // 索引超出范围，返回一个错误值或处理错误
    printf("get_q_error\t");
    printf("source:%d,page:%d,action%d\r\n", source, page, action);
    return -1.0f;
  }
  return q_table[source][page].q_values[action];
}