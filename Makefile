# 定义编译器
CC = g++

# 定义编译选项
CFLAGS = -Wall -O2

# 定义目标文件
TARGET = cache_simulator
SINGLE_TARGET = cache_simulator_single
FOUR_TARGET = cache_simulator_four
FOUR_SEP_TARGET = cache_simulator_four_sep
EVI_TARGET = cache_simulator_evi
SIM_TARGET = sim_progm/cache_simulator
COUNT_TARGET = cache_simulator_count  # 新增目标文件
RRIP_TARGET = cache_simulator_rrip  # 新增目标文件
LFU_TARGET = cache_simulator_lfu  # 新增目标文件
LFU2_TARGET = cache_simulator_lfu2  # 添加LFU2目标文件
ARC_TARGET = cache_simulator_arc  # 添加ARC目标文件

# 定义源文件
SRCS = main.c
SINGLE_SRCS = main_single.c
FOUR_SRCS = main_four.c
FOUR_SEP_SRCS = main_four_sep.c
EVI_SRCS = main_four_sep_evi.c
SIM_SRCS = sim_progm/main.c
COUNT_SRCS = main_four_sep_evi_count.c  # 新增源文件
RRIP_SRCS = main_four_sep_rrip.c  # 新增源文件
LFU_SRCS = main_four_sep_lfu.c  # 新增源文件
LFU2_SRCS = main_four_sep_lfu2.c  # 添加LFU2源文件
ARC_SRCS = main_four_sep_arc.c  # 添加ARC源文件
REPEAT_ONCE_SRCS = main_four_sep_arc_repeat_once.c  # 新增repeat_once源文件

# 定义对象文件
OBJS = $(SRCS:.c=.o)
SINGLE_OBJS = $(SINGLE_SRCS:.c=.o)
FOUR_OBJS = $(FOUR_SRCS:.c=.o)
FOUR_SEP_OBJS = $(FOUR_SEP_SRCS:.c=.o)
EVI_OBJS = $(EVI_SRCS:.c=.o)
SIM_OBJS = $(SIM_SRCS:.c=.o)
COUNT_OBJS = $(COUNT_SRCS:.c=.o)  # 新增对象文件
RRIP_OBJS = $(RRIP_SRCS:.c=.o)  # 新增对象文件
LFU_OBJS = $(LFU_SRCS:.c=.o)  # 新增对象文件
LFU2_OBJS = $(LFU2_SRCS:.c=.o)  # 添加LFU2对象文件
ARC_OBJS = $(ARC_SRCS:.c=.o)  # 添加ARC对象文件
REPEAT_ONCE_OBJS = $(REPEAT_ONCE_SRCS:.c=.o)  # 新增repeat_once对象文件

# 默认目标
all: $(TARGET) commit

# 链接目标文件
$(TARGET): $(OBJS)
	$(CC) $(CFLAGS) -o $(TARGET) $(OBJS)

$(SINGLE_TARGET): $(SINGLE_OBJS)
	$(CC) $(CFLAGS) -o $(SINGLE_TARGET) $(SINGLE_OBJS)

$(FOUR_TARGET): $(FOUR_OBJS)
	$(CC) $(CFLAGS) -o $(FOUR_TARGET) $(FOUR_OBJS)

$(FOUR_SEP_TARGET): $(FOUR_SEP_OBJS)
	$(CC) $(CFLAGS) -o $(FOUR_SEP_TARGET) $(FOUR_SEP_OBJS)

$(EVI_TARGET): $(EVI_OBJS)
	$(CC) $(CFLAGS) -o $(EVI_TARGET) $(EVI_OBJS)

$(SIM_TARGET): $(SIM_OBJS)
	$(CC) $(CFLAGS) -o $(SIM_TARGET) $(SIM_OBJS)

$(COUNT_TARGET): $(COUNT_OBJS)
	$(CC) $(CFLAGS) -o $(COUNT_TARGET) $(COUNT_OBJS)

$(RRIP_TARGET): $(RRIP_OBJS)
	$(CC) $(CFLAGS) -o $(RRIP_TARGET) $(RRIP_OBJS)

$(LFU_TARGET): $(LFU_OBJS)
	$(CC) $(CFLAGS) -o $(LFU_TARGET) $(LFU_OBJS)

$(LFU2_TARGET): $(LFU2_OBJS)
	$(CC) $(CFLAGS) -o $(LFU2_TARGET) $(LFU2_OBJS)

$(ARC_TARGET): $(ARC_OBJS)
	$(CC) $(CFLAGS) -o $(ARC_TARGET) $(ARC_OBJS)

repeat_test: cache_simulator_repeat_once
	./cache_simulator_repeat_once

cache_simulator_repeat_once: $(REPEAT_ONCE_OBJS)
	$(CC) $(CFLAGS) -o cache_simulator_repeat_once $(REPEAT_ONCE_OBJS)

# 编译源文件
%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

# 新增目标 sim：编译并运行 sim_progm 目录中的程序
sim: $(SIM_TARGET)
	./$(SIM_TARGET)

# 运行程序
run: $(TARGET)
	./$(TARGET)

single: $(SINGLE_TARGET)
	./$(SINGLE_TARGET)

four: $(FOUR_TARGET)
	./$(FOUR_TARGET)

four_sep: $(FOUR_SEP_TARGET)
	./$(FOUR_SEP_TARGET)

evi: $(EVI_TARGET)
	./$(EVI_TARGET)

# 新增 count 目标：编译并运行 main_four_sep_evi_count.c
count: $(COUNT_TARGET)
	./$(COUNT_TARGET)

# 新增 rrip 目标：编译并运行 main_four_sep_rrip.c
rrip: $(RRIP_TARGET)
	./$(RRIP_TARGET)

# 新增 lfu 目标：编译并运行 main_four_sep_lfu.c
lfu: $(LFU_TARGET)
	./$(LFU_TARGET)

# 新增 lfu2 目标：编译并运行 main_four_sep_lfu2.c
lfu2: $(LFU2_TARGET)
	./$(LFU2_TARGET)

# 新增 arc 目标：编译并运行 main_four_sep_arc.c
arc: $(ARC_TARGET)
	./$(ARC_TARGET)

# 清理生成的文件
clean:
	rm -f $(TARGET) $(OBJS) $(SINGLE_TARGET) $(SINGLE_OBJS) $(FOUR_TARGET) $(FOUR_OBJS) $(FOUR_SEP_TARGET) $(FOUR_SEP_OBJS) $(EVI_TARGET) $(EVI_OBJS) $(SIM_TARGET) $(SIM_OBJS) $(COUNT_TARGET) $(COUNT_OBJS) $(RRIP_TARGET) $(RRIP_OBJS) $(LFU_TARGET) $(LFU_OBJS) $(LFU2_TARGET) $(LFU2_OBJS) $(ARC_TARGET) $(ARC_OBJS)

# Git 提交
commit:
	git add .
	git commit -m "Automatic commit after build"
# git push

# 伪目标
.PHONY: all clean run commit single four four_sep evi sim count rrip lfu lfu2 arc repeat_test
