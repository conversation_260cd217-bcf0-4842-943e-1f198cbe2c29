#include "simulator.h"

#include <stdio.h>

#include "cache.h"
#include "config.h"
#include "eq.h"
// #include "prefetcher.h"
#include "q_table.h"
#include "stdlib.h"
#include "top.h"

// extern int separate_miss_rate;
// extern int log_enabled;

// int samp_log = 2968;

void simulate_accesses(const char *input_filename, const char *log_filename) {
  // printf("input:%s",INPUT_FILENAME);
  FILE *input_file = fopen(input_filename, "r");
  if (!input_file) {
    fprintf(stderr, "Failed to open file %s\n", input_filename);
    return;
  }

  long timestamp, dummy1, dummy2, rw_flag, load_id;
  unsigned long long page_number;
  int hits[NUM_MACHINES + 1] = {0};
  int bypass_account[NUM_MACHINES + 1] = {0};
  int total_accesses[NUM_MACHINES + 1] = {0};
  long long total_delay = 0;
  long long delay_per_load[NUM_MACHINES + 1] = {0};

  //   const int SKIP_COUNT = *********/2;  // 前 105,440,564 次访问不计数
  //   const int LAST_COUNT = *********;  // 前 105,440,564 次访问不计数
  const int SKIP_COUNT = 0;          // 前 105,440,564 次访问不计数
  const int LAST_COUNT = *********;  // 前 105,440,564 次访问不计数
  int skip_count_enabled = 0;    // 标志位，用于控制是否跳过计数
  int current_access_count = 0;  // 当前的访问计数器

  while (fscanf(input_file, "%ld %ld %llu %ld %ld %ld", &timestamp, &dummy1,
                &page_number, &dummy2, &rw_flag, &load_id) == 6) {
    if (skip_count_enabled && current_access_count > SKIP_COUNT &&
        current_access_count < LAST_COUNT) {
      total_accesses[load_id]++;
    } else if (skip_count_enabled == 0) {
      total_accesses[load_id]++;
    }
	current_access_count++;  // 当前访问计数

    // 硬件实现
    int set_index = page_number % NUM_SETS;  // 获取映射到cache set的组号
    int cache_index = find_in_cache(page_number, load_id);
    int action_now = H_EPV;  // 您可以根据实际情况设置这个变量的值

	action_now = Alchemy_top(page_number, load_id,cache_index);

	//更新记录参数
    if (cache_index != -1) {
      // printf("hit??\r\n");
      // 预热
      if (skip_count_enabled && current_access_count > SKIP_COUNT &&
          current_access_count < LAST_COUNT) {
        hits[load_id]++;
        total_delay += (DDR_DELAY + CXL_DELAY);
        delay_per_load[load_id] += (DDR_DELAY + CXL_DELAY);
      } else if (skip_count_enabled == 0) {
        hits[load_id]++;
        total_delay += (DDR_DELAY + CXL_DELAY);
        delay_per_load[load_id] += (DDR_DELAY + CXL_DELAY);
      }
      update_lru(set_index, cache_index);
    } else {
      if (action_now == BYPASS) {
        // 预热
        if (skip_count_enabled && current_access_count > SKIP_COUNT &&
            current_access_count < LAST_COUNT) {
          bypass_account[load_id]++;
          total_delay += (SSD_DELAY + CXL_DELAY);  // 只有SSD和CXL延迟
          delay_per_load[load_id] +=
              (SSD_DELAY + CXL_DELAY);  // 更新每个负载的延迟
        } else if (skip_count_enabled == 0) {
          bypass_account[load_id]++;
          total_delay += (SSD_DELAY + CXL_DELAY);  // 只有SSD和CXL延迟
          delay_per_load[load_id] +=
              (SSD_DELAY + CXL_DELAY);  // 更新每个负载的延迟
        }
      } else {
        // 预热
        if (skip_count_enabled && current_access_count > SKIP_COUNT &&
            current_access_count < LAST_COUNT) {
          total_delay +=
              (SSD_DELAY + CXL_DELAY + DDR_DELAY);  // SSD、CXL和DDR延迟
          delay_per_load[load_id] +=
              (SSD_DELAY + CXL_DELAY + DDR_DELAY);  // 更新每个负载的延迟
        } else if (skip_count_enabled == 0) {
          total_delay +=
              (SSD_DELAY + CXL_DELAY + DDR_DELAY);  // SSD、CXL和DDR延迟
          delay_per_load[load_id] +=
              (SSD_DELAY + CXL_DELAY + DDR_DELAY);  // 更新每个负载的延迟
        }
        // load_to_cache_evp(page_number, load_id, action_now);
        load_to_cache_lru_evp(page_number, load_id, action_now);
        // load_to_cache_lru(page_number, load_id);  // 根据LRU策略加载到缓存
      }
    }
  }

  fclose(input_file);

  // 打印结果
  int total_hits = 0, total_access = 0;
  for (int i = 1; i <= NUM_MACHINES; i++) {
    total_hits += hits[i];
    total_access += total_accesses[i];
  }
  double overall_miss_rate = 1.0 - ((double)total_hits / total_access);
  printf("Overall miss rate: %.4f%%\n", overall_miss_rate * 100);

  if (separate_miss_rate) {
    for (int i = 1; i <= NUM_MACHINES; i++) {
      if (total_accesses[i] > 0) {
        double miss_rate = 1.0 - ((double)hits[i] / total_accesses[i]);
        double bypass_rate = ((double)bypass_account[i] / total_accesses[i]);
        printf("Miss rate for load %d: %.4f%%\n", i, miss_rate * 100);
        printf("bypass rate for load %d: %.4f%%\n", i, bypass_rate * 100);
        printf("Total delay for load %d: %lld ns\n", i, delay_per_load[i]);
      }
    }
  }

  printf("Total delay: %lld ns\n", total_delay);
}
