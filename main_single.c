#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>  // 添加用于计时的库

// 定义基础文件名宏
// #define BASE_FILENAME "pr_t3_spec508_namd_off"
#define BASE_FILENAME "trace/cactusBSSN_new_CN1"
// #define BASE_FILENAME "spec508_namd_off"

// #define BASE_FILENAME "pr_t3"
// #define BASE_FILENAME "spec508_namd"

// 自动生成输入和日志文件名
#define INPUT_FILENAME BASE_FILENAME ".pagetrace"
#define LOG_FILENAME BASE_FILENAME ".log"

// 定义缓存大小和延迟参数
#define CACHE_SIZE (int)((1L * 8 * 1024 * 1024 / 4096))  // DDR大小除以页面大小
#define SSD_DELAY 75000  // SSD访问延时（纳秒）
#define CXL_DELAY 100    // CXL延时（纳秒）
#define DDR_DELAY 46     // DDR访问延时（纳秒）

#define PREFETCH_N 4  // 预取的页面数量
// #define NUM_SETS 64   // 定义组的数量
// #define LINES_PER_SET (CACHE_SIZE / NUM_SETS)  // 每组的缓存行数

#define NUM_SETS (CACHE_SIZE / LINES_PER_SET)   // 定义组的数量
#define LINES_PER_SET 4  // 每组的缓存行数

// 定义缓存页面结构体
typedef struct CachePage {
    unsigned long long page_number;  // 页面编号
    int valid;                       // 有效位
    int lru_counter;                 // 用于 LRU 的计数器
    int fifo_order;                  // 用于 FIFO 的顺序
} CachePage;

CachePage cache[NUM_SETS][LINES_PER_SET];  // 组相连缓存
int fifo_counter[NUM_SETS] = {0};  // 每组的FIFO计数器

// 选择策略：0 表示 FIFO，1 表示 LRU
int strategy = 1;  // 默认使用 LRU

// 预取开关：0 表示关闭，1 表示开启
int prefetch_enabled = 0;  // 默认关闭预取

// 日志记录开关：0 表示关闭，1 表示开启
int log_enabled = 0;  // 默认开启日志记录

// 初始化缓存
void init() {
    for (int i = 0; i < NUM_SETS; i++) {
        for (int j = 0; j < LINES_PER_SET; j++) {
            cache[i][j].valid = 0;
            cache[i][j].lru_counter = 0;
            cache[i][j].fifo_order = 0;
        }
    }
}

// 在缓存中查找页面
int find_in_cache(unsigned long long page_number) {
    int set_index = page_number % NUM_SETS;
    for (int i = 0; i < LINES_PER_SET; i++) {
        if (cache[set_index][i].valid && cache[set_index][i].page_number == page_number) {
            return i;
        }
    }
    return -1;
}

// 更新 LRU 计数器
void update_lru(int set_index, int accessed_index) {
    for (int i = 0; i < LINES_PER_SET; i++) {
        if (cache[set_index][i].valid) {
            cache[set_index][i].lru_counter++;
        }
    }
    cache[set_index][accessed_index].lru_counter = 0;
}

// 将页面加载到缓存中（LRU 策略）
void load_to_cache_lru(unsigned long long page_number) {
    int set_index = page_number % NUM_SETS;
    int lru_index = 0;
    int max_counter = -1;

    for (int i = 0; i < LINES_PER_SET; i++) {
        if (!cache[set_index][i].valid) {
            lru_index = i;
            break;
        }
        if (cache[set_index][i].lru_counter > max_counter) {
            max_counter = cache[set_index][i].lru_counter;
            lru_index = i;
        }
    }

    cache[set_index][lru_index].page_number = page_number;
    cache[set_index][lru_index].valid = 1;
    update_lru(set_index, lru_index);
}

// 将页面加载到缓存中（FIFO 策略）
void load_to_cache_fifo(unsigned long long page_number) {
    int set_index = page_number % NUM_SETS;
    int fifo_index = 0;
    int oldest_order = __INT_MAX__;

    for (int i = 0; i < LINES_PER_SET; i++) {
        if (!cache[set_index][i].valid) {
            fifo_index = i;
            break;
        }
        if (cache[set_index][i].fifo_order < oldest_order) {
            oldest_order = cache[set_index][i].fifo_order;
            fifo_index = i;
        }
    }

    cache[set_index][fifo_index].page_number = page_number;
    cache[set_index][fifo_index].valid = 1;
    cache[set_index][fifo_index].fifo_order = fifo_counter[set_index]++;
}

// 执行预取操作
void prefetch(unsigned long long page_number) {
    if (!prefetch_enabled) return;

    for (int i = 1; i <= PREFETCH_N; i++) {
        unsigned long long next_page = page_number + i;
        if (find_in_cache(next_page) == -1) {
            if (strategy == 1) {  // LRU
                load_to_cache_lru(next_page);
            } else if (strategy == 0) {  // FIFO
                load_to_cache_fifo(next_page);
            }
        }
    }
}

// 模拟内存访问
void simulate_accesses(const char *input_filename, const char *log_filename) {
    FILE *input_file = fopen(input_filename, "r");
    if (!input_file) {
        fprintf(stderr, "Failed to open file %s\n", input_filename);
        return;
    }

    FILE *log_file = NULL;
    if (log_enabled) {
        log_file = fopen(log_filename, "w");
        if (!log_file) {
            fprintf(stderr, "Failed to open log file %s\n", log_filename);
            fclose(input_file);
            return;
        }
    }

    int hits = 0;
    int total_accesses = 0;
    long long total_delay = 0;
    long timestamp, dummy1, dummy2, rw_flag;
    unsigned long long page_number;

    // 读取并处理每个访问记录
    while (fscanf(input_file, "%ld %ld %llu %ld %ld", &timestamp, &dummy1, &page_number, &dummy2, &rw_flag) == 5) {
        total_accesses++;
        int set_index = page_number % NUM_SETS;
        int cache_index = find_in_cache(page_number);

        if (cache_index != -1) {
            hits++;
            total_delay += DDR_DELAY;
            if (strategy == 1) {  // LRU
                update_lru(set_index, cache_index);
            }
            if (log_enabled) {
                fprintf(log_file, "Timestamp %ld: Page %llu, Cache hit\n", timestamp, page_number);
            }
        } else {
            total_delay += (SSD_DELAY + CXL_DELAY);
            if (strategy == 1) {  // LRU
                load_to_cache_lru(page_number);
            } else if (strategy == 0) {  // FIFO
                load_to_cache_fifo(page_number);
            }
            if (log_enabled) {
                fprintf(log_file, "Timestamp %ld: Page %llu, Cache miss\n", timestamp, page_number);
            }
            prefetch(page_number);  // 执行预取
        }
    }

    fclose(input_file);
    if (log_enabled) {
        fclose(log_file);
    }

    // 打印统计信息
    double miss_rate = 1.0 - ((double)hits / total_accesses);
    printf("Miss rate: %.4f%%\n", miss_rate * 100);
    printf("Total delay: %lld ns\n", total_delay);
}

int main() {
    // 在这里选择替换策略：0 表示 FIFO，1 表示 LRU
    // strategy = 0;  // 选择 FIFO
    // strategy = 1;  // 选择 LRU

    // 可以在这里设置是否开启预取
    // prefetch_enabled = 1;  // 1 表示开启预取，0 表示关闭预取

    // 设置是否开启日志记录
    // log_enabled = 0;  // 0 表示关闭日志记录，1 表示开启日志记录
		printf("Test File :%s\r\n",INPUT_FILENAME);

    init();
    
    // 开始计时
    clock_t start_time = clock();
    
    simulate_accesses(INPUT_FILENAME, LOG_FILENAME);
    
    // 结束计时
    clock_t end_time = clock();
    double duration = (double)(end_time - start_time) / CLOCKS_PER_SEC;
    
    printf("Simulation time: %.2f seconds\n", duration);

    return 0;
}