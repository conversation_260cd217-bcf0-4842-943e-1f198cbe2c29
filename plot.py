import matplotlib.pyplot as plt
import numpy as np

# 定义采样比例因子
scale_factor = 10  # 你可以根据需要调整这个值

# 初始化数据列表
timestamps = []
addresses = []
sources = []

# 读取文件数据
with open('radiosity_off1_4CNs.pagetrace', 'r') as file:
    for i, line in enumerate(file):
        if i % scale_factor != 0:
            continue  # 采样：跳过不需要的行

        parts = line.split()
        timestamp = int(parts[0])
        address = int(parts[2])
        source = int(parts[5])

        timestamps.append(timestamp)
        addresses.append(address)
        sources.append(source)

# 将数据转换为 NumPy 数组
timestamps = np.array(timestamps)
addresses = np.array(addresses)
sources = np.array(sources)

# 创建颜色映射
distinct_colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']

# 绘制图形
plt.figure(figsize=(12, 6))
for source in np.unique(sources):
    mask = sources == source
    color = distinct_colors[source % len(distinct_colors)]  # 循环使用颜色
    plt.scatter(timestamps[mask], addresses[mask], s=1, color=color, label=f'Source {source}')

# 设置图形标签和标题
plt.xlabel('Timestamp')
plt.ylabel('Memory Address')
plt.title('Memory Accesses Over Time')
plt.legend(loc='upper right')

# 保存图形到文件
plt.savefig('memory_accesses.png', dpi=300, bbox_inches='tight')
# 显示图形
plt.show()
