#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>  // 添加用于计时的库

// 定义基础文件名宏
#define BASE_FILENAME "pr_t3_spec508_namd_off"
// #define BASE_FILENAME "pr_t3_off"
// #define BASE_FILENAME "spec508_namd_off"

// #define BASE_FILENAME "pr_t3"
// #define BASE_FILENAME "spec508_namd"

// #define BASE_FILENAME "pr_t3_spec508_namd_off"
#define INPUT_FILENAME BASE_FILENAME ".pagetrace"
#define LOG_FILENAME BASE_FILENAME ".log"

#define CACHE_SIZE (1L * 64 * 1024 * 1024 / 4096)
#define SSD_DELAY 75000
#define CXL_DELAY 100
#define DDR_DELAY 46

#define PREFETCH_N 4

#define NUM_SETS (CACHE_SIZE / LINES_PER_SET)
#define LINES_PER_SET 4

typedef struct CachePage {
    unsigned long long page_number;
    int valid;
    int lru_counter;
    int fifo_order;
} CachePage;

CachePage cache[NUM_SETS][LINES_PER_SET];
int fifo_counter[NUM_SETS] = {0};

int strategy = 1;
int prefetch_enabled = 0;
int log_enabled = 0;

// 新增开关：0 表示只计算总miss rate，1 表示计算分别和总miss rate
int separate_miss_rate = 1;

// 初始化缓存
void init() {
    for (int i = 0; i < NUM_SETS; i++) {
        for (int j = 0; j < LINES_PER_SET; j++) {
            cache[i][j].valid = 0;
            cache[i][j].lru_counter = 0;
            cache[i][j].fifo_order = 0;
        }
    }
}

int find_in_cache(unsigned long long page_number) {
    int set_index = page_number % NUM_SETS;
    for (int i = 0; i < LINES_PER_SET; i++) {
        if (cache[set_index][i].valid && cache[set_index][i].page_number == page_number) {
            return i;
        }
    }
    return -1;
}

void update_lru(int set_index, int accessed_index) {
    for (int i = 0; i < LINES_PER_SET; i++) {
        if (cache[set_index][i].valid) {
            cache[set_index][i].lru_counter++;
        }
    }
    cache[set_index][accessed_index].lru_counter = 0;
}

void load_to_cache_lru(unsigned long long page_number) {
    int set_index = page_number % NUM_SETS;
    int lru_index = 0;
    int max_counter = -1;

    for (int i = 0; i < LINES_PER_SET; i++) {
        if (!cache[set_index][i].valid) {
            lru_index = i;
            break;
        }
        if (cache[set_index][i].lru_counter > max_counter) {
            max_counter = cache[set_index][i].lru_counter;
            lru_index = i;
        }
    }

    cache[set_index][lru_index].page_number = page_number;
    cache[set_index][lru_index].valid = 1;
    update_lru(set_index, lru_index);
}

void load_to_cache_fifo(unsigned long long page_number) {
    int set_index = page_number % NUM_SETS;
    int fifo_index = 0;
    int oldest_order = __INT_MAX__;

    for (int i = 0; i < LINES_PER_SET; i++) {
        if (!cache[set_index][i].valid) {
            fifo_index = i;
            break;
        }
        if (cache[set_index][i].fifo_order < oldest_order) {
            oldest_order = cache[set_index][i].fifo_order;
            fifo_index = i;
        }
    }

    cache[set_index][fifo_index].page_number = page_number;
    cache[set_index][fifo_index].valid = 1;
    cache[set_index][fifo_index].fifo_order = fifo_counter[set_index]++;
}

void prefetch(unsigned long long page_number) {
    if (!prefetch_enabled) return;

    for (int i = 1; i <= PREFETCH_N; i++) {
        unsigned long long next_page = page_number + i;
        if (find_in_cache(next_page) == -1) {
            if (strategy == 1) {
                load_to_cache_lru(next_page);
            } else if (strategy == 0) {
                load_to_cache_fifo(next_page);
            }
        }
    }
}

void simulate_accesses(const char *input_filename, const char *log_filename) {
    FILE *input_file = fopen(input_filename, "r");
    if (!input_file) {
        fprintf(stderr, "Failed to open file %s\n", input_filename);
        return;
    }

    FILE *log_file = NULL;
    if (log_enabled) {
        log_file = fopen(log_filename, "w");
        if (!log_file) {
            fprintf(stderr, "Failed to open log file %s\n", log_filename);
            fclose(input_file);
            return;
        }
    }

    int hits[3] = {0};
    int total_accesses[3] = {0};
    long long total_delay = 0;
    long timestamp, dummy1, dummy2, rw_flag, load_id;
    unsigned long long page_number;

    while (fscanf(input_file, "%ld %ld %llu %ld %ld %ld", &timestamp, &dummy1, &page_number, &dummy2, &rw_flag, &load_id) == 6) {
        total_accesses[load_id]++;
        int set_index = page_number % NUM_SETS;
        int cache_index = find_in_cache(page_number);

        if (cache_index != -1) {
            hits[load_id]++;
            total_delay += DDR_DELAY;
            if (strategy == 1) {
                update_lru(set_index, cache_index);
            }
            if (log_enabled) {
                fprintf(log_file, "Timestamp %ld: Page %llu, Cache hit\n", timestamp, page_number);
            }
        } else {
            total_delay += (SSD_DELAY + CXL_DELAY);
            if (strategy == 1) {
                load_to_cache_lru(page_number);
            } else if (strategy == 0) {
                load_to_cache_fifo(page_number);
            }
            if (log_enabled) {
                fprintf(log_file, "Timestamp %ld: Page %llu, Cache miss\n", timestamp, page_number);
            }
            prefetch(page_number);
        }
    }

    fclose(input_file);
    if (log_enabled) {
        fclose(log_file);
    }

    int total_hits = hits[1] + hits[2];
    int total_access = total_accesses[1] + total_accesses[2];
    double overall_miss_rate = 1.0 - ((double)total_hits / total_access);
    printf("Overall miss rate: %.4f%%\n", overall_miss_rate * 100);

    if (separate_miss_rate) {
        for (int i = 1; i <= 2; i++) {
            if (total_accesses[i] > 0) {
                double miss_rate = 1.0 - ((double)hits[i] / total_accesses[i]);
                printf("Miss rate for load %d: %.4f%%\n", i, miss_rate * 100);
            }
        }
    }

    printf("Total delay: %lld ns\n", total_delay);
}

int main() {
    init();
    
    clock_t start_time = clock();
    
    simulate_accesses(INPUT_FILENAME, LOG_FILENAME);
    
    clock_t end_time = clock();
    double duration = (double)(end_time - start_time) / CLOCKS_PER_SEC;
    
    printf("Simulation time: %.2f seconds\n", duration);

    return 0;
}
