import random

def mix_files(file_a, file_b, output_file, ratio):
    # 解析比例
    ratio_a, ratio_b = map(int, ratio.split(':'))
    total_ratio = ratio_a + ratio_b

    # 计算选择A的概率
    prob_a = ratio_a / total_ratio

    # 打开文件
    with open(file_a, 'r') as fa, open(file_b, 'r') as fb, open(output_file, 'w') as out:
        # 读取文件内容
        lines_a = fa.readlines()
        lines_b = fb.readlines()

        # 初始化索引
        index_a = 0
        index_b = 0

        # 获取文件长度
        len_a = len(lines_a)
        len_b = len(lines_b)

        # 混合文件内容
        while index_a < len_a and index_b < len_b:
            if random.random() < prob_a:
                # 从file_a读取，并在行末添加标识符1
                out.write(lines_a[index_a].strip() + ' 1\n')
                index_a += 1
            else:
                # 从file_b读取，并在行末添加标识符2
                out.write(lines_b[index_b].strip() + ' 2\n')
                index_b += 1

        # 如果一个文件读完了，另一个文件剩下的内容将被舍弃

if __name__ == "__main__":
    file_a = 'pr_t3_off.pagetrace'
    file_b = 'spec508_namd_off.pagetrace'
    output_file = 'pr_t3_spec508_namd_off.pagetrace'
    ratio = '70:30'  # 你可以根据需要更改比例

    mix_files(file_a, file_b, output_file, ratio)
