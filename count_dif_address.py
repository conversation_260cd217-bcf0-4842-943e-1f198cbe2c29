# 读取文件并统计不同的内存地址数
def count_unique_addresses(filename):
    unique_addresses = set()  # 用集合来存储不同的地址

    with open(filename, 'r') as file:
        for line in file:
            parts = line.split()
            if len(parts) > 2:  # 检查行是否至少有第三列
                address = parts[2]
                unique_addresses.add(address)  # 将地址添加到集合中

    return len(unique_addresses)  # 返回集合的长度，即不同地址的数量

# 文件名
filename = 'spec_lbm_new_4CNs.pagetrace'
unique_address_count = count_unique_addresses(filename)
print(f"不同内存访问地址的数量为: {unique_address_count}")
