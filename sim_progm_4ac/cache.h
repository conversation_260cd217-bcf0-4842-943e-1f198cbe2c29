#ifndef CACHE_H
#define CACHE_H

#define CACHE_SIZE (1L * 64 * 1024 * 1024 / 4096)
#define NUM_SETS (CACHE_SIZE / LINES_PER_SET)
#define LINES_PER_SET 4

typedef struct CachePage {
  unsigned long long page_number;
  int valid;
  int lru_counter;
  int fifo_order;
  int load_id;
  int epv;  // 添加EPV字段，表示驱逐优先级
} CachePage;

extern CachePage cache[NUM_SETS][LINES_PER_SET];
// extern int strategy;

void init_cache();
int find_in_cache(unsigned long long page_number, long load_id);
void update_lru(int set_index, int accessed_index);
void load_to_cache_lru(unsigned long long page_number, int load_id);
void load_to_cache_fifo(unsigned long long page_number, int load_id);
// 在 cache.h 中添加函数声明
void load_to_cache_lru_evp(unsigned long long page_number, int load_id,
                           int epv);
int find_in_cache_and_update_epv(unsigned long long page_number, long load_id, int epv);
void load_to_cache_evp(unsigned long long page_number, int load_id, int epv);
#endif
