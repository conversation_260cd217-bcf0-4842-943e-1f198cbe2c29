#include "eq.h"

#include <stdio.h>
#include <stdlib.h>

// 全局EQ数组
EvaluationQueue evaluation_queues[NUM_EQ];
bool log_file_initialized = false;
// 初始化EQ
void init_evaluation_queues(void) {
  for (int i = 0; i < NUM_EQ; i++) {
    evaluation_queues[i].front = 0;
    evaluation_queues[i].rear = 0;
    evaluation_queues[i].size = 0;
    for (int j = 0; j < EQ_SIZE; j++) {
      evaluation_queues[i].entries[j] = NULL;
    }
  }
}

// // 将条目添加到EQ
// bool enqueue(uint32_t eq_index, Naper_EQEntry *entry) {
//   if (eq_index >= NUM_EQ) return false;  // 索引超出范围
//   EvaluationQueue *eq = &evaluation_queues[eq_index];
//   if (eq->size >= EQ_SIZE) return false;  // EQ已满

//   eq->entries[eq->rear] = entry;
//   eq->rear = (eq->rear + 1) % EQ_SIZE;
//   eq->size++;
//   return true;
// }

Naper_EQEntry *enqueue(uint32_t eq_index, Naper_EQEntry *entry) {
  if (eq_index >= NUM_EQ) {
    printf("enqueue 超出范围\r\n");
    return NULL;  // 索引超出范围
  }

  EvaluationQueue *eq = &evaluation_queues[eq_index];

  if (eq->size >= EQ_SIZE) {
    // EQ已满，逐出一个条目
    Naper_EQEntry *evicted_entry = eq->entries[eq->front];
    eq->entries[eq->front] = entry;  // 在队列头部替换为新条目
    eq->front = (eq->front + 1) % EQ_SIZE;
    eq->rear = (eq->rear + 1) % EQ_SIZE;  // rear也要前进
    return evicted_entry;                 // 返回被逐出的条目
  } else {
    // 添加新条目到EQ
    eq->entries[eq->rear] = entry;
    eq->rear = (eq->rear + 1) % EQ_SIZE;
    eq->size++;
    return NULL;  // 队列未满，没有条目被逐出
  }
}

// 从EQ中移除条目
Naper_EQEntry *dequeue(uint32_t eq_index) {
  if (eq_index >= NUM_EQ) return NULL;  // 索引超出范围
  EvaluationQueue *eq = &evaluation_queues[eq_index];
  if (eq->size == 0) return NULL;  // EQ为空

  Naper_EQEntry *entry = eq->entries[eq->front];
  eq->front = (eq->front + 1) % EQ_SIZE;
  eq->size--;
  return entry;
}

// 更新EQ中条目的位置
void update_eq_position(uint32_t eq_index) {
  if (eq_index >= NUM_EQ) return;  // 索引超出范围
  EvaluationQueue *eq = &evaluation_queues[eq_index];
  for (int i = 0; i < eq->size; i++) {
    int index = (eq->front + i) % EQ_SIZE;
    eq->entries[index]->position =
        (eq->entries[index]->position - 1 + EQ_SIZE) % EQ_SIZE;
  }
}

// 记录EQ条目到日志文件
void log_eq_entry(Naper_EQEntry *entry, const char *log_filename) {
  if (entry == NULL) return;

  // 检查是否需要清空并初始化日志文件
  if (!log_file_initialized) {
    FILE *log_file = fopen(log_filename, "w");
    if (log_file) {
      fclose(log_file);
      log_file_initialized = true;  // 标记日志文件已初始化
    }
  }

  // 追加模式写入日志文件
  FILE *log_file = fopen(log_filename, "a");
  if (log_file) {
    fprintf(
        log_file,
        "Address: %lu, ActionIndex: %d, InCache: %d, CPU: %d, CacheSet: %d\n",
        entry->address, entry->action_index, entry->in_cache, entry->cpu,
        entry->cache_set);
    fclose(log_file);
  }
}

// // 新增打印EQ的函数
// void print_evaluation_queues(const char *log_filename) {
//   FILE *log_file = fopen(log_filename, "a");
//   if (log_file == NULL) {
//     perror("Failed to open log file");
//     return;
//   }

//   for (int i = 0; i < NUM_EQ; ++i) {
//     fprintf(log_file, "EQ %d:\n", i);
//     for (int j = 0; j < EQ_SIZE; ++j) {
//       if (evaluation_queues[i].entries[j] != NULL) {
//         Naper_EQEntry *entry = evaluation_queues[i].entries[j];
//         fprintf(log_file,
//                 "  Address: %lu, ActionIndex: %d, InCache: %d, CPU: %d, "
//                 "CacheSet: %d reward %d , %d\n",
//                 entry->address, entry->action_index, entry->in_cache,
//                 entry->cpu, entry->cache_set, entry->reward,
//                 entry->has_reward);
//       }
//     }
//   }
//   fclose(log_file);
// }

// // 全局标志变量
// bool print_specific_eq_only = true;  // 默认为 false，表示打印所有 EQ
// int specific_eq_index = 55;          // 需要打印的特定 EQ 的索引

// // 新增打印EQ的函数，从队尾开始打印
// void print_evaluation_queues(const char *log_filename) {
//   FILE *log_file = fopen(log_filename, "a");
//   if (log_file == NULL) {
//     perror("Failed to open log file");
//     return;
//   }

//   for (int i = 0; i < NUM_EQ; ++i) {
//     // 如果标志位打开且当前 EQ 不是特定 EQ，则跳过打印
//     if (print_specific_eq_only && i != specific_eq_index) {
//       continue;
//     }

//     fprintf(log_file, "EQ %d:\n", i);
//     for (int j = evaluation_queues[i].size - 1; j >= 0; j--) {
//       int index = (evaluation_queues[i].rear - j - 1 + EQ_SIZE) % EQ_SIZE;
//       if (evaluation_queues[i].entries[index] != NULL) {
//         Naper_EQEntry *entry = evaluation_queues[i].entries[index];
//         fprintf(log_file,
//                 "  Address: %llu, ActionIndex: %d, InCache: %d, CPU: %d, "
//                 "CacheSet: %d, Reward: %d, HasReward: %d, Trigger: %d\n",
//                 entry->address, entry->action_index, entry->in_cache,
//                 entry->cpu, entry->cache_set, entry->reward,
//                 entry->has_reward, entry->trigger);
//       }
//     }
//   }
//   fclose(log_file);
// }
// 全局标志变量
bool print_specific_eq_only = true;  // 默认为 true，表示只打印特定 EQ
int specific_eq_index = 6;          // 需要打印的特定 EQ 的索引

// 新增打印EQ的函数，从最近进入的条目开始打印
void print_evaluation_queues(const char *log_filename) {
  FILE *log_file = fopen(log_filename, "a");
  if (log_file == NULL) {
    perror("Failed to open log file");
    return;
  }

  for (int i = 0; i < NUM_EQ; ++i) {
    // 如果标志位打开且当前 EQ 不是特定 EQ，则跳过打印
    if (print_specific_eq_only && i != specific_eq_index) {
      continue;
    }

    fprintf(log_file, "EQ %d:\n", i);
    EvaluationQueue *eq = &evaluation_queues[i];
    for (int j = 0; j < eq->size; ++j) {
      // 从最近进入的条目开始打印
      int index = (eq->rear - 1 - j + EQ_SIZE) % EQ_SIZE;
      if (eq->entries[index] != NULL) {
        Naper_EQEntry *entry = eq->entries[index];
        fprintf(log_file,
                "  Address: %llu, ActionIndex: %d, InCache: %d, CPU: %d, "
                " Reward: %d, HasReward: %d, Trigger: %d\n",
                entry->address, entry->action_index, entry->in_cache,
                entry->cpu, entry->reward, entry->has_reward, entry->trigger);
      }
    }
  }
  fclose(log_file);
}
// 错误
//  // 在EQ中从队尾开始查找匹配的address
//  Naper_EQEntry *find_in_evaluation_queue(uint32_t eq_index, uint64_t address)
//  {
//    if (eq_index >= NUM_EQ) {
//      printf("超出范围\r\n");
//      return NULL;  // 索引超出范围
//    }

//   EvaluationQueue *eq = &evaluation_queues[eq_index];
//   for (int i = eq->size - 1; i >= 0; i--) {
//     int index = (eq->rear - i - 1 + EQ_SIZE) % EQ_SIZE;  //
//     从队尾开始计算索引 if (eq->entries[index] != NULL &&
//     eq->entries[index]->address == address) {
// 			// if (eq_index == 55)
//       // printf("index:%d r:%d\r\n",i,eq->entries[index]->reward);
//       return eq->entries[index];
//     }
//   }
//   return NULL;  // 未找到匹配的条目
// }

// Naper_EQEntry *find_in_evaluation_queue(uint32_t eq_index, uint64_t address)
// {
//   if (eq_index >= NUM_EQ) {
//     printf("超出范围\r\n");
//     return NULL;  // 索引超出范围
//   }

//   EvaluationQueue *eq = &evaluation_queues[eq_index];

//   // 从队尾开始向队首遍历
//   for (int i = 0; i < eq->size; i++) {
//     // 计算当前要检查的索引
//     int index = (eq->rear - 1 - i + EQ_SIZE) % EQ_SIZE;
//     if (eq->entries[index] != NULL && eq->entries[index]->address == address)
//     {
//       // if (eq_index == 55)
//       //   printf("index:%d r:%d\r\n", index, eq->entries[index]->reward);
//       return eq->entries[index];
//     }
//   }
//   return NULL;  // 未找到匹配的条目
// }

Naper_EQEntry *find_in_evaluation_queue(uint32_t eq_index, uint64_t address) {
  if (eq_index >= NUM_EQ) {
    printf("超出范围\r\n");
    return NULL;  // 索引超出范围
  }

  EvaluationQueue *eq = &evaluation_queues[eq_index];

  // 从最近插入的条目开始向队首遍历
  for (int i = 0; i < eq->size; i++) {
    // 计算当前要检查的索引
    int index = (eq->rear - i - 1 + EQ_SIZE) % EQ_SIZE;
    if (eq->entries[index] != NULL && eq->entries[index]->address == address) {
      // 如果需要，可以在这里添加调试信息
      // if (eq_index == 55)
      //   printf("index:%d r:%d\r\n", index, eq->entries[index]->reward);
      return eq->entries[index];
    }
  }
  return NULL;  // 未找到匹配的条目
}

// // 查找EQ中匹配地址的条目的索引
// int find_eq_entry_index(uint32_t eq_index, uint64_t address) {
//   if (eq_index >= NUM_EQ) {
//     printf("超出范围\r\n");
//     return -1;  // 索引超出范围
//   }

//   EvaluationQueue *eq = &evaluation_queues[eq_index];
//   for (int i = eq->size - 1; i >= 0; i--) {
//     int index = (eq->rear - i - 1 + EQ_SIZE) % EQ_SIZE;
//     if (eq->entries[index] != NULL && eq->entries[index]->address == address)
//     {
//       // if (eq_index == 55)
//       // printf("indexi:%d\r\n", i);
//       return i;  // 返回找到的条目的索引
//     }
//   }
//   return -1;  // 未找到匹配的条目
// }

// 查找EQ中匹配地址的条目的索引
int find_eq_entry_index(uint32_t eq_index, uint64_t address) {
  if (eq_index >= NUM_EQ) {
    printf("超出范围\r\n");
    return -1;  // 索引超出范围
  }

  EvaluationQueue *eq = &evaluation_queues[eq_index];

  // 从队尾开始向队头遍历
  for (int i = 0; i < eq->size; i++) {
    // 计算当前要检查的索引
    int index = (eq->rear - 1 - i + EQ_SIZE) % EQ_SIZE;
    if (eq->entries[index] != NULL && eq->entries[index]->address == address) {
      return index;  // 返回找到的条目的索引
    }
  }
  return -1;  // 未找到匹配的条目
}

// int find_eq_entry_index(uint32_t eq_index, uint64_t address) {
//   if (eq_index >= NUM_EQ) {
//     printf("超出范围\r\n");
//     return -1;  // 索引超出范围
//   }

//   EvaluationQueue *eq = &evaluation_queues[eq_index];

//   // 从最近插入的条目开始向队首遍历
//   for (int i = 0; i < eq->size; i++) {
//     // 计算当前要检查的索引
//     int index = (eq->rear - i - 1 + EQ_SIZE) % EQ_SIZE;
//     if (eq->entries[index] != NULL && eq->entries[index]->address == address)
//     {
//       // 如果需要，可以在这里添加调试信息
//       // if (eq_index == 55)
//       //   printf("index:%d\r\n", index);
//       return index;  // 返回找到的条目的索引
//     }
//   }
//   return -1;  // 未找到匹配的条目
// }

// // 查找EQ中匹配地址的条目的索引
// int find_eq_entry_index(uint32_t eq_index, uint64_t address) {
//   if (eq_index >= NUM_EQ) {
//     printf("超出范围\r\n");
//     return -1;  // 索引超出范围
//   }

//   EvaluationQueue *eq = &evaluation_queues[eq_index];

//   // 从队尾开始向队首遍历
//   for (int i = 0; i < eq->size; i++) {
//     // 计算当前要检查的索引
//     int index = (eq->rear - 1 - i + EQ_SIZE) % EQ_SIZE;
//     if (eq->entries[index] != NULL && eq->entries[index]->address == address)
//     {
//       if (eq_index == 55)
//         printf("index:%d r:%d\r\n", index, eq->entries[index]->reward);
//       return index;  // 返回找到的条目的索引
//     }
//   }
//   return -1;  // 未找到匹配的条目
// }

// 检查EQ是否已满
bool is_evaluation_queue_full(uint32_t eq_index) {
  if (eq_index >= NUM_EQ)
    return true;  // 索引超出范围，视为错误，返回true表示“满”

  EvaluationQueue *eq = &evaluation_queues[eq_index];
  return eq->size >= EQ_SIZE;  // 如果EQ的大小达到了EQ_SIZE，则表示已满
}

// 查看EQ头部的条目但不移除它
Naper_EQEntry *peek_evaluation_queue(uint32_t eq_index) {
  if (eq_index >= NUM_EQ) return NULL;  // 索引超出范围

  EvaluationQueue *eq = &evaluation_queues[eq_index];
  if (eq->size == 0) return NULL;  // EQ为空

  int front_index = eq->front;
  return eq->entries[front_index];
}

// 修改EQ中最后一个条目的奖励值
bool modify_eq_entry_reward(uint32_t eq_index, int tail_index,
                            int32_t new_reward) {
  if (eq_index >= NUM_EQ) return false;  // 索引超出范围
  EvaluationQueue *eq = &evaluation_queues[eq_index];
  if (tail_index < 0 || tail_index >= EQ_SIZE || eq->size <= tail_index) {
    printf("modify reward over");
    return false;  // 索引超出EQ大小
  }

  // 获取特定索引的条目
  // int index = (eq->front + tail_index + EQ_SIZE) % EQ_SIZE;
  int index = tail_index;
  if (eq->entries[index] != NULL) {
    eq->entries[index]->reward = new_reward;  // 修改奖励值
    eq->entries[index]->has_reward = true;    // 修改奖励值
    eq->entries[index]->trigger = true;       // 修改奖励值
    return true;                              // 成功修改
  }
  printf("modify reward fail \r\n");
  return false;  // 指定索引没有有效条目
}