import heapq

def mix_files_by_arrival_time(files, output_file, performance_ratios):
    # 打开所有输入文件
    file_handles = [open(file, 'r') for file in files]

    # 创建一个优先队列（最小堆）
    min_heap = []
    last_arrival_times = [0] * len(files)  # 记录每个文件的最后到达时间
    file_read_count = [0] * len(files)  # 记录每个文件被重新读取的次数
    total_read_cycles = 0  # 记录文件完成完整读取的总次数

    # 初始化堆，将每个文件的第一行放入堆中
    for file_index, fh in enumerate(file_handles):
        line = fh.readline().strip()
        if line:
            parts = line.split()
            arrival_time = int(parts[0])  # 假设到达时间在第一列
            heapq.heappush(min_heap, (arrival_time, line, file_index))

    # 打开输出文件
    with open(output_file, 'w') as out:
        # 不断从堆中取出最小的元素
        while min_heap:
            arrival_time, line, file_index = heapq.heappop(min_heap)
            out.write(f'{line} {file_index + 1}\n')

            # 从同一个文件读取下一行
            next_line = file_handles[file_index].readline().strip()
            if next_line:
                parts = next_line.split()
                next_arrival_time = int(parts[0]) + last_arrival_times[file_index]
                # 根据性能比例调整到达时间
                adjusted_arrival_time = next_arrival_time / performance_ratios[file_index]
                heapq.heappush(min_heap, (adjusted_arrival_time, next_line, file_index))
            else:
                # 该文件读取完毕，重新从头开始读取
                file_read_count[file_index] += 1
                last_arrival_times[file_index] = arrival_time
                file_handles[file_index].seek(0)  # 从头开始读取
                next_line = file_handles[file_index].readline().strip()
                
                if next_line:
                    parts = next_line.split()
                    next_arrival_time = int(parts[0]) + last_arrival_times[file_index]
                    adjusted_arrival_time = next_arrival_time / performance_ratios[file_index]
                    heapq.heappush(min_heap, (adjusted_arrival_time, next_line, file_index))
                
                # 如果所有文件都已经至少被读取完整一遍，则停止处理
                if all(count > 0 for count in file_read_count):
                    total_read_cycles += 1
                    if total_read_cycles >= 1:  # 至少读一遍就结束程序
                        break

    # 关闭所有文件
    for fh in file_handles:
        fh.close()

if __name__ == "__main__":
		# files = ['gap_cc_off1.pagetrace', 'gap_cc_off2.pagetrace', 'gap_cc_off3.pagetrace', 'gap_cc_off4.pagetrace']
		# output_file = 'gap_cc_mix1_difspeed.pagetrace'

		files = ['spec_lbm_off1.pagetrace', 'spec_lbm_off2.pagetrace', 'spec_lbm_off3.pagetrace', 'spec_lbm_off4.pagetrace']
		output_file = 'spec_lbm_mix1_difspeed.pagetrace'

		# 假设四个机器的性能比例
		performance_ratios = [1.0, 1.0, 0.5, 0.5]  # 机器1、2、3、4的性能比例

		mix_files_by_arrival_time(files, output_file, performance_ratios)
