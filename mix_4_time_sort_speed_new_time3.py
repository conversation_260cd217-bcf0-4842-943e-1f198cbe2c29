def merge_pagetrace_files(files, output_file, performance_ratios, time_offsets):
    # 初始化一个列表来作为优先队列
    priority_queue = []

    # 打开所有文件，并初始化文件指针
    file_pointers = [open(file, 'r') for file in files]
    last_arrival_times = [0] * len(files)  # 存储每个文件最后一条记录的到达时间
    completed_files = [False] * len(files)  # 记录每个文件是否已读取完一遍

    try:
        # 首先读取每个文件的第一行并加入优先队列
        for source, fp in enumerate(file_pointers, start=1):
            line = fp.readline().strip()
            if line:
                parts = line.split()
                arrival_time = int(parts[0])
                # 根据性能比例调整到达时间，同时加上时间偏置
                adjusted_arrival_time = (arrival_time + time_offsets[source - 1]) / performance_ratios[source - 1]
                # 将调整后的到达时间、行内容、来源索引加入队列
                priority_queue.append((adjusted_arrival_time, parts, source))
                last_arrival_times[source - 1] = arrival_time

        # 自定义排序函数
        def sort_priority_queue(queue):
            # 按到达时间排序
            queue.sort(key=lambda x: x[0])

        # 打开输出文件
        with open(output_file, 'w') as out_fp:
            # 处理队列中的每个元素
            while priority_queue:
                # 对队列进行排序
                sort_priority_queue(priority_queue)
                # 弹出队列中最小的元素
                adjusted_arrival_time, parts, source = priority_queue.pop(0)
                # 写入输出文件，附加来源信息
                out_fp.write(' '.join(parts) + f' {source}\n')

                # 从相应的文件中读取下一行
                next_line = file_pointers[source - 1].readline().strip()
                if next_line:
                    next_parts = next_line.split()
                    next_arrival_time = int(next_parts[0]) + last_arrival_times[source - 1]
                    # 根据性能比例调整到达时间，同时加上时间偏置
                    adjusted_next_arrival_time = (next_arrival_time + time_offsets[source - 1]) / performance_ratios[source - 1]
                    # 将新行加入队列
                    priority_queue.append((adjusted_next_arrival_time, next_parts, source))
                    last_arrival_times[source - 1] = next_arrival_time
                else:
                    # 文件到达末尾，标记为已完成
                    completed_files[source - 1] = True
                    print(f"File {files[source - 1]} has been read completely once.")
                    
                    # 检查是否所有文件都已完成
                    if all(completed_files):
                        print("All files have been read completely once. Exiting.")
                        break
                    # 重置文件指针并读取第一行
                    file_pointers[source - 1].seek(0)
                    next_line = file_pointers[source - 1].readline().strip()
                    if next_line:
                        next_parts = next_line.split()
                        # 计算新的到达时间
                        next_arrival_time = int(next_parts[0]) + last_arrival_times[source - 1]
                        # 根据性能比例调整到达时间，同时加上时间偏置
                        adjusted_next_arrival_time = (next_arrival_time + time_offsets[source - 1]) / performance_ratios[source - 1]
                        # 将新行加入队列
                        priority_queue.append((adjusted_next_arrival_time, next_parts, source))
                        last_arrival_times[source - 1] = next_arrival_time
    finally:
        # 确保所有文件指针都被关闭
        for fp in file_pointers:
            fp.close()

# 示例调用
# files = ['spec505_mcf_mix2.pagetrace', 'deepsjeng_t2_mix2.pagetrace', 'cactusBSSN_new_mix2.pagetrace', 'gap_bc_new_mix2.pagetrace']
# output_file = 'mcf_deep_cactus_bc_mix2_difspeed.pagetrace'
# performance_ratios = [1.0, 0.8, 0.6, 0.4]
# time_offsets = [1000, 2000, 3000, 4000]  # 示例时间偏置

# files = ['gap_sssp_CN0.pagetrace', 'gap_sssp_CN1.pagetrace']
# output_file = 'gap_sssp_2CNs.pagetrace'
# performance_ratios = [1.0, 1.0]
# time_offsets = [1400, 125000]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

# files = ['gap_sssp_CN0.pagetrace', 'gap_sssp_CN1.pagetrace', 'gap_sssp_CN2.pagetrace']
# output_file = 'gap_sssp_3CNs.pagetrace'
# performance_ratios = [1.0, 1.0,1.0]
# time_offsets = [1400, 25000,100000]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)


# files = ['gap_bc_new_CN0.pagetrace', 'gap_bc_new_CN1.pagetrace', 'gap_bc_new_CN2.pagetrace','gap_bc_new_CN3.pagetrace']
# output_file = 'gap_bc_new_4CNs.pagetrace'
# performance_ratios = [1.0, 1.0,1.0,1.0]
# time_offsets = [1400, 25000,70000,150000]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

# files = ['cactusBSSN_new_CN00.pagetrace', 'cactusBSSN_new_CN11.pagetrace', 'cactusBSSN_new_CN22.pagetrace','cactusBSSN_new_CN33.pagetrace']
# output_file = 'cactusBSSN_new_4CNs_0.3.pagetrace'
# performance_ratios = [0.6745411885588198, 1.3952398426055694, 1.0003219463654607, 0.9325645237741711]
# time_offsets = [24154, 457524,75427,124858]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

# files = ['cactusBSSN_new_CN00.pagetrace', 'cactusBSSN_new_CN11.pagetrace']
# output_file = 'cactusBSSN_new_2CNs_0.5.pagetrace'
# performance_ratios = [0.6442833519803634, 1.3547916789880534]
# time_offsets = [24154, 457524]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

# files = ['cactusBSSN_new_CN00.pagetrace', 'cactusBSSN_new_CN11.pagetrace', 'cactusBSSN_new_CN22.pagetrace','cactusBSSN_new_CN33.pagetrace']
# output_file = 'cactusBSSN_new_4CNs_0.35.pagetrace'
# performance_ratios = [0.8933316872518171, 0.8228285078607795, 1.524995840055055, 0.760848469661495]
# time_offsets = [24154, 457524,75427,124858]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

# files = ['cactusBSSN_new_CN0.pagetrace', 'cactusBSSN_new_CN1.pagetrace', 'cactusBSSN_new_CN2.pagetrace','cactusBSSN_new_CN3.pagetrace','cactusBSSN_new_CN4.pagetrace', 'cactusBSSN_new_CN5.pagetrace', 'cactusBSSN_new_CN6.pagetrace','cactusBSSN_new_CN7.pagetrace']
# output_file = 'cactusBSSN_new_8CNs_0.05.pagetrace'
# performance_ratios = [1.0467219381129682, 1.0555169004700737, 0.9569426017958135, 0.9539339287830254, 1.021911687537848, 1.0011274783669324, 1.0412688903085157, 0.9219943438226831]
# time_offsets = [54752, 25285,475581,22457,256795, 24560,25468,7885142]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

# files = ['cactusBSSN_new_CN0.pagetrace', 'cactusBSSN_new_CN1.pagetrace']
# output_file = 'cactusBSSN_new_2CNs_0.45.pagetrace'
# performance_ratios = [0.6844249329065966, 1.3146190590317497]
# time_offsets = [54752,25468]  # 示例时间偏置
# merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)

files = ['trace/cactusBSSN_new_CN1.pagetrace', 'trace/cactusBSSN_new_CN2.pagetrace','trace/cactusBSSN_new_CN3.pagetrace', 'trace/cactusBSSN_new_CN4.pagetrace']
output_file = 'trace/cactusBSSN_new_4CN_04.pagetrace'
performance_ratios = [1.5199256065776545, 0.5474083851432611, 0.9784379014732485, 0.9518345847027614]
time_offsets = [ 25285,178842,15158,36572]  # 示例时间偏置
merge_pagetrace_files(files, output_file, performance_ratios, time_offsets)