SSD_DELAY = 75000
CXL_DELAY = 100
DDR_DELAY = 46

# # pr_t3_t1_mix1
# access_count = 85883491
# miss_rate = 9.8393 / 100
# miss_rate_new = 12.1038 / 100

# # spec508_namd_mix1.pagetrace
# access_count = 83010642
# miss_rate = 1.0643 / 100
# miss_rate_new = 2.0823 / 100

# # spec_lbm_new_mix1
# access_count = 99209134
# miss_rate = 1.1775 / 100
# miss_rate_new = 1.5120 / 100

# # gap_cc_mix1
# access_count = 80041282
# miss_rate = 11.4684 / 100
# miss_rate_new = 18.0609 / 100

# # gap_cc_异构结点
# access_count = 80041282
# miss_rate = 11.4684 / 100
# # miss_rate_new = 21.4302 / 100
# # miss_rate_new = 20.8889 / 100
# # miss_rate_new = 26.8389 / 100
# miss_rate_new = 23.9421 / 100

# # mcf
# access_count = 341692502
# miss_rate = 1.0205 / 100
# miss_rate_new = 2.4159 / 100

# # deepsjeng_t2.pagetrace
# access_count = 73860127
# miss_rate = 26.0909 / 100
# miss_rate_new = 26.7209 / 100

# # cactusBSSN_new
# access_count = 85310166
# miss_rate = 2.8998 / 100
# miss_rate_new = 9.5649 / 100

# # gap_bc_new
# access_count = 85202125
# miss_rate = 2.3902 / 100
# miss_rate_new = 10.9647 / 100

# # spec_lbm 异构结点
access_count = 99209134
miss_rate = 1.1775 / 100
# miss_rate_new = 1.4003 / 100
# miss_rate_new = 1.4585 / 100
# miss_rate_new = 1.5061 / 100
miss_rate_new = 1.6068 / 100

latency = access_count * miss_rate * (SSD_DELAY+CXL_DELAY) + access_count * (1-miss_rate) * DDR_DELAY
latency_new = access_count * miss_rate_new * (SSD_DELAY+CXL_DELAY) + access_count * (1-miss_rate_new) * DDR_DELAY

latency_radio = latency_new / latency
print(f"latency: {latency}")
print(f"new_latency: {latency_new}")

print(f"latency_radio: {latency_radio}")