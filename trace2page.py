def get_physical_page_address(memory_address):
    page_size = 4096  # 4KB
    physical_page_address = memory_address // page_size
    return physical_page_address

def process_trace_file(input_filename, output_filename):
    with open(input_filename, 'r') as infile, open(output_filename, 'w') as outfile:
        for line in infile:
            # 将行拆分为字段
            fields = line.split()
            if len(fields) < 3:
                continue  # 确保有足够的字段
            
            # 提取内存访问地址并计算物理页面地址
            try:
                memory_address = int(fields[2])
                physical_page = get_physical_page_address(memory_address)
                # 用物理页面地址替换原始内存地址
                fields[2] = str(physical_page)
                # 写入修改后的行到输出文件
                outfile.write(" ".join(fields) + "\n")
            except ValueError:
                print("Invalid memory address encountered, skipping line.")
                continue

# 输入和输出文件名
input_filename = "trace/splash_radiosity_p.trace"
output_filename = "trace/splash_radiosity_p.pagetrace"

# 处理文件
process_trace_file(input_filename, output_filename)
