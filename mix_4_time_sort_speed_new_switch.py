import heapq

def mix_files_by_arrival_time_with_rates(files, rates, output_file, discard_on_fastest_finish=False):
    # 打开所有输入文件
    file_handles = [open(file, 'r') for file in files]
    file_offsets = [0] * len(files)  # 记录每个文件的最后一个到达时间

    # 创建一个优先队列（最小堆）
    min_heap = []
    finished_counts = [0] * len(files)  # 记录每个文件被读取的次数
    total_files = len(files)

    # 初始化堆，将每个文件的第一行放入堆中，并根据速率调整时间
    for file_index, fh in enumerate(file_handles):
        line = fh.readline().strip()
        if line:
            parts = line.split()
            arrival_time = int(parts[0])  # 假设到达时间在第一列
            adjusted_time = arrival_time / rates[file_index]  # 根据速率调整时间
            heapq.heappush(min_heap, (adjusted_time, line, file_index))

    # 打开输出文件
    with open(output_file, 'w') as out:
        fastest_finished = False  # 记录最快的文件是否完成
        # 不断从堆中取出最小的元素
        while min_heap:
            adjusted_time, line, file_index = heapq.heappop(min_heap)
            out.write(f'{line} {file_index + 1}\n')

            # 从同一个文件读取下一行
            next_line = file_handles[file_index].readline().strip()
            if next_line:
                parts = next_line.split()
                next_arrival_time = int(parts[0])
                next_adjusted_time = (file_offsets[file_index] + next_arrival_time) / rates[file_index]  # 根据速率调整时间并叠加之前的最后时间
                heapq.heappush(min_heap, (next_adjusted_time, next_line, file_index))
            else:
                # 如果文件已读完，增加读取计数并从头重新开始读取
                finished_counts[file_index] += 1
                if file_index == 0 and discard_on_fastest_finish:
                    fastest_finished = True
                    break  # 退出循环，舍弃其他文件

                if all(count >= 1 for count in finished_counts):
                    # 所有文件至少读完一遍，退出
                    break
                else:
                    # 文件重新读取，并将当前文件的最后一个到达时间记录为偏移量
                    file_handles[file_index].seek(0)  # 回到文件开始
                    file_offsets[file_index] = file_offsets[file_index] + adjusted_time  # 将最后一个访存行为的时间设为偏移量

                    # 读取文件的第一行重新加入堆
                    next_line = file_handles[file_index].readline().strip()
                    if next_line:
                        parts = next_line.split()
                        next_arrival_time = int(parts[0])
                        next_adjusted_time = (file_offsets[file_index] + next_arrival_time) / rates[file_index]  # 根据速率调整时间并叠加偏移量
                        heapq.heappush(min_heap, (next_adjusted_time, next_line, file_index))

        # 如果选择了舍弃剩余负载
        if discard_on_fastest_finish and fastest_finished:
            print("最快的文件已读完，其他负载被舍弃。")

    # 关闭所有文件
    for fh in file_handles:
        fh.close()

if __name__ == "__main__":
    # files = ['spec_lbm_off1.pagetrace', 'spec_lbm_off2.pagetrace', 'spec_lbm_off3.pagetrace', 'spec_lbm_off4.pagetrace']

    files = ['radiosity_off1.pagetrace', 'radiosity_off2.pagetrace', 'radiosity_off3.pagetrace', 'radiosity_off4.pagetrace']
    output_file = 'radiosity_mix1_difspeed.pagetrace'
    rates = [1.0, 0.9, 0.85, 0.8]  # 每个文件对应的速率比例

    discard_on_fastest_finish = True  # 是否在最快的机器完成时舍弃其他文件的负载
    mix_files_by_arrival_time_with_rates(files, rates, output_file, discard_on_fastest_finish)
